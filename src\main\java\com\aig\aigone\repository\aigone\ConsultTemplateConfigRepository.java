package com.aig.aigone.repository.aigone;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.aig.aigone.model.dto.DoctorConsultTemplateConfigDto;
import com.aig.aigone.model.entity.aigone.ConsultTemplateConfigEntity;

import io.lettuce.core.dynamic.annotation.Param;

@Repository
public interface ConsultTemplateConfigRepository extends JpaRepository<ConsultTemplateConfigEntity, Long> {
    
    @Query(value="SELECT new com.aig.aigone.model.dto.DoctorConsultTemplateConfigDto(t.id, t.name, t.type, t.code, CASE WHEN dtc.id IS NOT NULL THEN true ELSE false END) "
    		+ "FROM ConsultTemplateConfigEntity t LEFT JOIN DoctorConsultTemplateConfigEntity dtc  ON t.id = dtc.templateConfig.id AND dtc.status='ACTIVE' AND dtc.doctorEmployeeCode = :doctorEmployeeCode WHERE "
    		+ "        (t.department = :department AND EXISTS ("
    		+ "            SELECT 1 FROM ConsultTemplateConfigEntity temp WHERE temp.department = :department"
    		+ "        )) "
    		+ "        OR "
    		+ "        (t.department IS NULL AND NOT EXISTS ("
    		+ "            SELECT 1 FROM ConsultTemplateConfigEntity temp WHERE temp.department = :department"
    		+ "        )) ORDER BY t.priority desc")
    List<DoctorConsultTemplateConfigDto> findTemplates(@Param("doctorEmployeeCode") String doctorEmployeeCode,
            @Param("department") String department);
}
