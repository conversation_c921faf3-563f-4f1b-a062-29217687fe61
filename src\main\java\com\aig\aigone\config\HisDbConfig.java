package com.aig.aigone.config;

import java.util.HashMap;
import java.util.Map;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;

import com.zaxxer.hikari.HikariDataSource;

@Configuration
@EnableJpaRepositories(basePackages = {"com.aig.aigone.repository.his","com.aig.aigone.model.entity.his","com.aig.aigone.daycare.his.model","com.aig.aigone.daycare.his.repository"},entityManagerFactoryRef = "hisEntityManager", transactionManagerRef = "hisTransactionManager")
public class HisDbConfig {


    @Bean(name = "hisDataSourceProperties")
    @ConfigurationProperties(prefix = "his.datasource")
    DataSourceProperties hisDataSourceProperties() {
        return new DataSourceProperties();
    }

    //    @ConfigurationProperties("his.datasource.configuration")
    @Bean(name = "hisDataSource")
    DataSource hisDataSource(@Qualifier("hisDataSourceProperties") DataSourceProperties hisDataSourceProperties) {
        return hisDataSourceProperties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
    }
    
    @Bean
    LocalContainerEntityManagerFactoryBean hisEntityManager(EntityManagerFactoryBuilder builder,@Qualifier("hisDataSource") DataSource hisDataSource) {
//        LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
//        em.setDataSource(hisDataSource);
//        em.setPackagesToScan("com.aig.aigone.model.his.entity","com.aig.aigone.repository.his");
//        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
//        em.setJpaVendorAdapter(vendorAdapter);
////        HashMap<String, Object> properties = new HashMap<>();
////        properties.put("hibernate.hbm2ddl.auto", env.getProperty("hibernate.hbm2ddl.auto"));
////        properties.put("hibernate.dialect", env.getProperty("hibernate.dialect"));
////        em.setJpaPropertyMap(properties);
//        return em;
    	
    	Map<String, Object> properties = new HashMap<>();
        properties.put("hibernate.hbm2ddl.auto", "none");
//        properties.put("hibernate.dialect", "org.hibernate.dialect.PostgreSQLDialect");
        properties.put("hibernate.properties.hibernate.query.plan_cache_max_size", "1");
        properties.put("hibernate.properties.hibernate.plan_parameter_metadata_max_size", "1");
//        properties.put("hibernate.naming.physical-strategy", "org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy");
//        properties.put("hibernate.naming.implicit-strategy", "org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy");
        
        return builder.dataSource(hisDataSource)
                .packages("com.aig.aigone.model.entity.his","com.aig.aigone.repository.his","com.aig.aigone.daycare.his.model","com.aig.aigone.daycare.his.repository")
                .persistenceUnit("hisDb")
                .properties(properties)
                .build();
    }
 
    @Bean
    PlatformTransactionManager hisTransactionManager(LocalContainerEntityManagerFactoryBean hisEntityManager) {
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(hisEntityManager.getObject());
        return transactionManager;
    }

}