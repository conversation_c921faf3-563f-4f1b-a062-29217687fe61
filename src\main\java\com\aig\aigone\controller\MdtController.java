package com.aig.aigone.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.model.dto.mdt.MdtBoardDto;
import com.aig.aigone.model.dto.mdt.MdtCommentRequestDto;
import com.aig.aigone.model.dto.mdt.MdtDto;
import com.aig.aigone.model.dto.mdt.MdtMedicationDto;
import com.aig.aigone.model.dto.mdt.MdtRequestDto;
import com.aig.aigone.model.dto.mdt.MdtVitalsDto;
import com.aig.aigone.security.SecurityUtil;
import com.aig.aigone.service.MdtService;

import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/mdt")
public class MdtController {
	
	@Autowired
	private MdtService mdtService;
	
	@Operation(summary = "fetch Patient Profile ")
	@GetMapping("/all")
	public List<MdtDto> fetchAllMdtCases() {
		return mdtService.fetchAllMdtCases(SecurityUtil.getCurrentUser().getEmployeeId());
	}
	
	@PostMapping("/create")
	public MdtDto createMdt(@RequestBody MdtRequestDto requestDto) {
		return mdtService.createMdt(requestDto,false);
	}
	
	@PostMapping("/update")
	public MdtDto updateMdt(@RequestBody MdtRequestDto requestDto) {
		return mdtService.createMdt(requestDto,true);
	}
	
	@PutMapping("/update/{mdtId}")
	public MdtDto updateMdt(@PathVariable Long mdtId, @RequestBody MdtRequestDto requestDto) {
		return mdtService.createMdt(requestDto,true);
	}
	
	@GetMapping("/{mdtId}")
	public String loadMdt(@PathVariable Long mdtId) {
		return null;
	}
	
	@GetMapping("/board/{mdtId}")
	public List<MdtBoardDto> loadBoard(@PathVariable Long mdtId) {
		return mdtService.fetchBoard(mdtId);
	}
	
	@GetMapping("/vitals/{mdtId}")
	public List<MdtVitalsDto> fetchVitals(@PathVariable Long mdtId) {
		return mdtService.fetchVitals(mdtId);
	}
	
	@GetMapping("/vitals/patient/{uhId}")
	public List<MdtVitalsDto> fetchVitals(@PathVariable String uhId) {
		return mdtService.fetchVitals(uhId);
	}
	
	@GetMapping("/comments/{mdtId}")
	public List<MdtBoardDto> fetchComments(@PathVariable Long mdtId) {
		return mdtService.fetchAllComments(mdtId);
	}
	
	@GetMapping("/comments/patient/{uhId}")
	public List<MdtBoardDto> fetchComments(@PathVariable String uhId) {
		return mdtService.fetchAllComments(uhId);
	}
	
	@GetMapping("/ipnotes/{mdtId}")
	public List<MdtBoardDto> fetchIpNotes(@PathVariable Long mdtId) {
		return mdtService.fetchAllDoctorComments(mdtId);
	}
	
	@PostMapping("/comment/{mdtId}")
	public void saveComments(@PathVariable Long mdtId,@RequestBody MdtCommentRequestDto requestDto) {
		requestDto.setMdtId(mdtId);
		mdtService.saveComment(requestDto);
	}
	
	@GetMapping("/medications/{mdtId}")
	public List<MdtMedicationDto> fetchMedications(@PathVariable Long mdtId){
		return mdtService.fetchMedication(mdtId,null,null);
	}
	
	@GetMapping("/reports/{mdtId}/{type}")
	public List<MdtBoardDto> fetcReposrts(@PathVariable Long mdtId,@PathVariable String type) {
		return mdtService.fetchReports(mdtId, type);
	}
	
	@GetMapping("/patient/medications")
	public List<MdtMedicationDto> fetchMedications(@RequestParam(name="mdtId",required=false) Long mdtId, @RequestParam(name="uhId",required=false) String uhId, @RequestParam(name="ipNumber",required=false) String ipNumber) {
		return mdtService.fetchMedication(mdtId,uhId,ipNumber);
	}
	
	@GetMapping("/medications/patient/{uhId}/{ipNo}")
	public List<MdtMedicationDto> fetchMedications(@PathVariable String uhId,@PathVariable String ipNo) {
		return mdtService.fetchMedication(uhId,ipNo);
	}
	
	@GetMapping("/coordinators")
	public List<MdtBoardDto> fetchCoordinators() {
		return mdtService.fetchCoordinators();
	}
	
	
}
