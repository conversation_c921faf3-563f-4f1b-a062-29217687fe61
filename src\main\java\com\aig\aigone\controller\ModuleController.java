package com.aig.aigone.controller;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.model.dto.FeatureDTO;
import com.aig.aigone.model.entity.aigone.ModuleDTO;
import com.aig.aigone.model.entity.aigone.ModuleRequest;
import com.aig.aigone.service.ModuleService;

import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/modules")
@RequiredArgsConstructor
@Tag(name = "Module Management", description = "APIs for managing modules and their features")
public class ModuleController {
	 private final ModuleService moduleService;

	    @GetMapping
	    public ResponseEntity<List<ModuleDTO>> getAllModules() {
	        return ResponseEntity.ok(moduleService.getAllModules());
	    }

	    @GetMapping("/{id}")
	    public ResponseEntity<ModuleDTO> getModuleById(@PathVariable Long id) {
	        return ResponseEntity.ok(moduleService.getModuleById(id));
	    }

	    @PostMapping
	    public ResponseEntity<ModuleDTO> createModule(@Valid @RequestBody ModuleRequest request) {
	        return new ResponseEntity<>(moduleService.createModule(request), HttpStatus.CREATED);
	    }

	    @PutMapping("/{id}")
	    public ResponseEntity<ModuleDTO> updateModule(
	            @PathVariable Long id, 
	            @Valid @RequestBody ModuleRequest request) {
	        return ResponseEntity.ok(moduleService.updateModule(id, request));
	    }

	    @DeleteMapping("/{id}")
	    public ResponseEntity<Void> deleteModule(@PathVariable Long id) {
	        moduleService.deleteModule(id);
	        return ResponseEntity.noContent().build();
	    }

	    @GetMapping("/{id}/features")
	    public ResponseEntity<List<FeatureDTO>> getModuleFeatures(@PathVariable Long id) {
	        return ResponseEntity.ok(moduleService.getModuleFeatures(id));
	    }
} 