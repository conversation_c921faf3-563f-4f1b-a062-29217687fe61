package com.aig.aigone.controller;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.daycare.dto.AvailabilityDTO;
import com.aig.aigone.daycare.dto.BookingDTO;
import com.aig.aigone.daycare.dto.CancelSlotDTO;
import com.aig.aigone.daycare.dto.CancelledAppointmentsListDTO;
import com.aig.aigone.daycare.dto.DeleteAppointmentDTO;
import com.aig.aigone.daycare.dto.DialysisPatientDTO;
import com.aig.aigone.daycare.dto.ExistingBookingDetailsDTO;
import com.aig.aigone.daycare.dto.PatientDTO;
import com.aig.aigone.daycare.dto.RemainderDTO;
import com.aig.aigone.daycare.dto.RescheduleBookingDTO;
import com.aig.aigone.daycare.dto.SlotDTO;
import com.aig.aigone.daycare.dto.WaitingListAppointmentsDTO;
import com.aig.aigone.daycare.dto.WaitingListDTO;
import com.aig.aigone.daycare.exception.BookingNotFoundException;
import com.aig.aigone.daycare.exception.DialysisBookingNotFoundException;
import com.aig.aigone.daycare.exception.SlotNotFoundException;
import com.aig.aigone.daycare.his.model.BedType;
import com.aig.aigone.daycare.his.model.Company;
import com.aig.aigone.daycare.his.model.Procedure;
import com.aig.aigone.daycare.his.model.Station;
import com.aig.aigone.daycare.model.DailysisFrequency;
import com.aig.aigone.daycare.model.DailysisSlotTiming;
import com.aig.aigone.daycare.model.DialysisBooking;
import com.aig.aigone.daycare.model.PaymentMethod;
import com.aig.aigone.daycare.model.Slot;
import com.aig.aigone.daycare.service.BookingService;
import com.aig.aigone.daycare.service.DialysisService;
import com.aig.aigone.daycare.service.HisServiceDaycare;
import com.aig.aigone.daycare.service.SlotService;

@CrossOrigin("*")
@RestController
@RequestMapping("/dialysis")
public class DialysisController {
	private static final Logger logger = LoggerFactory.getLogger(DialysisController.class);

	@Autowired
	private DialysisService dialysisService;

	@Autowired
	private HisServiceDaycare hisservice;

	@Autowired
	private ApplicationContext applicationContext;
	
	@Autowired
	private BookingService bookingService;
	
	@Autowired
    private SlotService slotService;
	
	@GetMapping("/test")
	public PatientDTO test() {
		String[] beanNames = applicationContext.getBeanDefinitionNames();
		System.out.println("All Beans and their Classes in the ApplicationContext:");
		for (String beanName : beanNames) {
			Object bean = applicationContext.getBean(beanName);
			System.out.println("Bean Name: " + beanName + " | Bean Class: " + bean.getClass().getName());
		}
		
		return hisservice.getPatientByUHID("AIGG", 20225158);
		
	}

	@GetMapping("getDialysisBooking/{bookingId}")
	public DialysisBooking getDialysisBooking(@PathVariable("bookingId") Integer bookingId) {
		logger.info("Fetching dialysis booking for bookingId: {}", bookingId);
		return dialysisService.getDialysisBooking(bookingId);
	}

	/**
	 * 
	 * @param uhid
	 * @return it will return just next slot from current date
	 */
	@GetMapping("upcomingSlot/{uhid}")
	public Slot upcomingSlot(@PathVariable("uhid") String uhid) {
		logger.info("Fetching upcoming slot for patient with UHID: {}", uhid);
		return dialysisService.upcomingSlot(uhid);
	}

	/**
	 * 
	 * @param date
	 * @return all slot on a particular date
	 */
	@GetMapping("/bookingByDate")
	public List<DialysisPatientDTO> getBookingByDate(@RequestParam(value = "date", required = false) LocalDate date) {
		if (date == null) {
			logger.info("Fetching all bookings as no date provided.");
			return dialysisService.getAllBooking();
		}
		logger.info("Fetching bookings for date: {}", date);
		return dialysisService.getSlotByDate(date);
	}

	/**
	 * 
	 * @param bookingId
	 * @return based on booking id it will return all the slot in that booking id
	 */
	@GetMapping("/patientBookingDetails/{bookingId}")
	public List<Slot> markArrived(@PathVariable("bookingId") Integer bookingId) {
		logger.info("Fetching patient booked slots for bookingId: {}", bookingId);
		return dialysisService.getPatientBookedSlot(bookingId);
	}

	/**
	 * 
	 * @param slotId
	 * @return mark the arrival
	 */
	@GetMapping("/markarrival/{slotId}")
	public String markArrived(@PathVariable("slotId") Long slotId) {
		logger.info("Marking arrival for slotId: {}", slotId);
		return dialysisService.markArrival(slotId);
	}

	/**
	 * 
	 * @param startDate
	 * @param endDate
	 * @param bedCategory
	 * @return in the given range of data, it will return is there any slot
	 *         available in all date of given range
	 */

	@GetMapping("/getmonth_availability/{startDate}/{endDate}/{bedCategory}")
	public List<AvailabilityDTO> getMonthAvailability(
			@PathVariable("startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
			@PathVariable("endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
			@PathVariable("bedCategory") Integer bedCategory) {
		logger.info("Fetching month availability from {} to {} for bedCategory: {}", startDate, endDate, bedCategory);
		return dialysisService.getMonthAvailability(startDate, endDate, bedCategory);
	}

	
	@PostMapping("/booking")
	public ResponseEntity<String> booking(@RequestBody BookingDTO bookingDetails) {
		logger.info("Booking a bed with details: {}", bookingDetails);

		dialysisService.bookBed(bookingDetails);
		return new ResponseEntity<>("Booked Successfully", HttpStatus.OK);
	}
	
	
	@PutMapping("/rescheduleBooking")
	public ResponseEntity<String> rescheduleBooking(@RequestBody RescheduleBookingDTO  rescheduleBookingDTO) {
		logger.info("rescheduling booking details: {}", rescheduleBookingDTO);
 
		dialysisService.rescheduleBooking(rescheduleBookingDTO);
		return new ResponseEntity<>("Rescheduled Successfully", HttpStatus.OK);
	}
	
	
	
//	/**
//	 * Endpoint to reschedule an existing booking.
//	 *
//	 * This method handles HTTP PUT requests made to the "/booking/reschedule" URL.
//	 * It takes a BookingDTO object from the request body, which contains the details
//	 * of the booking to be rescheduled, including the reschedule reason and other
//	 * relevant information.
//	 *
//	 * @param bookingDetails The BookingDTO containing the details of the booking to be rescheduled.
//	 * @return ResponseEntity<String> containing the response message and HTTP status.
//	 */
//	@PutMapping("/booking/reschedule")
//	public ResponseEntity<String> rescheduleBooking(@RequestBody BookingDTO bookingDetails) {
//	    // Log the details of the booking rescheduling request
//	    logger.info("Rescheduling booking with details: {}", bookingDetails);
//
//	    try {
//	        // Attempt to reschedule the booking using the service layer
//	        boolean success = dialysisService.rescheduleBooking(bookingDetails);
//
//	        // Check the result of the rescheduling operation
//	        if (success) {
//	            // Return a success response if the booking was rescheduled
//	            return new ResponseEntity<>("Rescheduled Successfully", HttpStatus.OK);
//	        } else {
//	            // Return a bad request response if rescheduling failed
//	            return new ResponseEntity<>("Failed to Reschedule", HttpStatus.BAD_REQUEST);
//	        }
//	    } catch (BookingNotFoundException e) {
//	        // Log and return a not found response if the booking does not exist
//	        logger.error("Booking not found: {}", e.getMessage());
//	        return new ResponseEntity<>("Booking not found", HttpStatus.NOT_FOUND);
//	    } catch (UnavailableSlotException e) {
//	        // Log and return a bad request response if the requested slot is unavailable
//	        logger.error("Slot unavailable: {}", e.getMessage());
//	        return new ResponseEntity<>("Requested slot is unavailable", HttpStatus.BAD_REQUEST);
//	    } catch (InvalidBookingStateException e) {
//	        // Log and return a bad request response for invalid booking states
//	        logger.error("Invalid booking state: {}", e.getMessage());
//	        return new ResponseEntity<>("Invalid booking state", HttpStatus.BAD_REQUEST);
//	    } catch (Exception e) {
//	        // Log and return an internal server error response for any other unexpected exceptions
//	        logger.error("An unexpected error occurred: {}", e.getMessage());
//	        return new ResponseEntity<>("Internal server error", HttpStatus.INTERNAL_SERVER_ERROR);
//	    }
//	}


	/**
	 * 
	 * @param count
	 * @return this will return all combination of days in week like [mon, wed] etc
	 */
	@GetMapping("/getDays/{count}")
	public List<List<Integer>> getWeekdays(@PathVariable("count") Integer count) {
		logger.info("Fetching weekdays combination for count: {}", count);

		return dialysisService.getWeekDayList(count);
	}

	@GetMapping("/payment_type")
	public List<PaymentMethod> getPaymentCategory() {
		logger.info("Fetching all payment categories.");

		return dialysisService.getPaymentCategory();
	}

	/**
	 * 
	 * @return frequency of dialysis like 1 for once a week etc.
	 */
	@GetMapping("/getFrequency")
	public List<DailysisFrequency> getFrequency() {
		logger.info("Fetching dialysis frequency.");
		return dialysisService.getFrequency();
	}

	/**
	 * 
	 * @return return all slot timing of dialysis like 6am to 10 pm , etc
	 */
	@GetMapping("getSlot")
	public List<DailysisSlotTiming> getSlotTime() {
		logger.info("Fetching slot timings.");
		return dialysisService.getSlot();
	}
	
	/**
	 * Fetches existing booking details for the specified UHID.
	 * 
	 * This method checks the system for any bookings associated with the provided UHID.
	 * If bookings are found, it returns a list of ExistingBookingDetailsDTO objects,
	 * each containing the booking details and associated patient booked slots.
	 * 
	 * @param uhid the unique health identifier of the patient
	 * @return a list of ExistingBookingDetailsDTO containing booking and slot information
	 */
	@GetMapping("/getExistingBookingDetails/{uhid}")
	public List<ExistingBookingDetailsDTO> getExistingBookingDetails(@PathVariable String uhid) {
	    // Call the service method to get existing booking details for the given UHID
	    List<ExistingBookingDetailsDTO> existingBookingDetailsDTOList = dialysisService.getExistingBookingDetails(uhid);

	    // Check if the list is empty and log an appropriate message
	    if (existingBookingDetailsDTOList.isEmpty()) {
	        logger.warn("No existing bookings found for UHID: {}", uhid);
	    } else {
	        logger.info("Found {} existing bookings for UHID: {}", existingBookingDetailsDTOList.size(), uhid);
	    }

	    // Return the list of existing booking details
	    return existingBookingDetailsDTOList;
	}

	
	

    /**
     * Deletes a specific slot by its ID.
     */
	@DeleteMapping("/cancelSlot")
    public ResponseEntity<String> cancelSlot(@RequestBody CancelSlotDTO cancelSlotDTO) {
        slotService.cancelSlot(cancelSlotDTO);
        return ResponseEntity.ok("Cancelled Selected Slot "+cancelSlotDTO.getSlotId()+" Successfully"); // Return success message with 200 OK status
    }


//	/**
//     * Endpoint to delete all slots associated with a given UHID.
//     * 
//     * @param uhid The UHID of the patient for whom slots need to be deleted.
//     * @return A response indicating the result of the deletion.
//     */
//    @DeleteMapping("/deleteByUhid/{uhid}")
//    public ResponseEntity<String> deleteSlotsByUhid(@PathVariable("uhid") String uhid) {
//        try {
//            slotService.deleteSlotsByUhid(uhid);
//            return ResponseEntity.ok("Slots deleted successfully for UHID: " + uhid);
//        } catch (SlotNotFoundException e) {
//            return ResponseEntity.status(404).body(e.getMessage());
//        } catch (Exception e) {
//            return ResponseEntity.status(500).body("Error occurred while deleting slots for UHID: " + uhid);
//        }
//    }

	
	/**
	 * Endpoint to delete all slots, dialysis bookings, and booking details associated with a given UHID and booking ID.
	 * 
	 * @param uhid The UHID of the patient whose data is to be deleted.
	 * @param bookingId The ID of the booking to delete.
	 * @return A response indicating the result of the deletion.
	 */
	@DeleteMapping("/deleteByUhidAndBookingId/")
	public ResponseEntity<String> deleteAllByUhidAndBookingId(
	        @RequestBody DeleteAppointmentDTO deleteAppointment) throws BookingNotFoundException, DialysisBookingNotFoundException, SlotNotFoundException {
	    try {
	        slotService.deleteAllByUhidAndBookingId(deleteAppointment);
	        return ResponseEntity.ok("All related entries deleted successfully for UHID: " + deleteAppointment.getUhid() + " and Booking ID: " + deleteAppointment.getBookingId());
	    } catch (BookingNotFoundException | DialysisBookingNotFoundException | SlotNotFoundException e) {
	        return ResponseEntity.status(404).body(e.getMessage());
	    } catch (Exception e) {
	        return ResponseEntity.status(500).body("Error occurred while deleting entries for UHID: " + deleteAppointment.getUhid() + " and Booking ID: " + deleteAppointment.getUhid());
	    }
	}


//	public static class BookingNotFoundException extends RuntimeException {
//	    public BookingNotFoundException(String message) {
//	        super(message);
//	    }
//	}

//	public static class DialysisBookingNotFoundException extends RuntimeException {
//	    public DialysisBookingNotFoundException(String message) {
//	        super(message);
//	    }
//	}



//    // Exception handler for SlotNotFoundException
//    @ExceptionHandler(SlotNotFoundException.class)
//    public ResponseEntity<String> handleSlotNotFound(SlotNotFoundException ex) {
//        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(ex.getReason());
//    }

    
    
   @PutMapping("/rescheduleSlot")
   	public ResponseEntity<Slot> rescheduleSlot(@RequestBody SlotDTO slotDTO ) {
            Slot slot = slotService.rescheduleSlot(slotDTO);
           return ResponseEntity.ok(slot); // Return success message with 200 OK status
       }
   
   
   @GetMapping("/UHIDdialysisBookingDetails")
   public ResponseEntity<List<DialysisBooking>> getdialysisBookingDetailsByUHID(@RequestParam("UHID") String UHID){

	   logger.info("getdialysisBookingDetailsByUHID {}");

	   List<DialysisBooking> results = dialysisService.getDialysisBookingDetailsByUHID(UHID);

	   return ResponseEntity.ok(results);

   }
   
   @PostMapping("/waitingListAppointment")
	public ResponseEntity<String> waitingListAppointment(@RequestBody WaitingListDTO bookingDetails) {
		logger.info("Adding appointments to waiting list: {}", bookingDetails);

		dialysisService.waitingListAppointment(bookingDetails);
		return new ResponseEntity<>("Added to Waiting List Successfully", HttpStatus.OK);
	}
 
   @GetMapping("/waitingListData")
   public ResponseEntity<List<WaitingListAppointmentsDTO>> getWaitingListData(){
	   
	   logger.info("Waiting List Appointments Data");
	   List<WaitingListAppointmentsDTO> result = dialysisService.getWaitingListData();
	   return ResponseEntity.ok(result);
   }
   
   @GetMapping("/cancelledAppointments")
   public ResponseEntity<List<CancelledAppointmentsListDTO>> getCancelledAppointments(){
	   
	   logger.info("Cancelled List Appointments Data");
	   List<CancelledAppointmentsListDTO> result = dialysisService.getCancelledAppointments();
	   return ResponseEntity.ok(result);
   }
   
   @PostMapping("/remainderNotification")
   public ResponseEntity<String> sendRemainderNotification(@RequestBody RemainderDTO remainderDTO ){
	   
	   logger.info("Sending Remainder Notification to Patient");
	   dialysisService.sendRemainderNotification(remainderDTO);
	   return ResponseEntity.ok("Remainder Notification is sent to patient with UHID: "+remainderDTO.getUhid());
   }
   
   @PostMapping("/fetchBedSlotsThreeDays")
   public ResponseEntity<List<Map<String, Object>>> getBedAndSlotAvailabilityForThreeDays(@RequestBody Map<String,Object> input){
	   logger.info("Fetch beds availability status for three days");
	   List<Map<String, Object>> result = slotService.getBedAndSlotAvailabilityForThreeDays(input);
	   return ResponseEntity.ok(result);
   }
   
   @GetMapping("/getAppointmentsByDate")
   public ResponseEntity<List<DialysisPatientDTO>> getAppointmentsByDate(@RequestParam("startDate") LocalDate startDate,@RequestParam("endDate") LocalDate endDate) {
	   logger.info("Get total appointments for given date range");
	   List<DialysisPatientDTO> result = dialysisService.getAppointmentsByDate(startDate,endDate);
	   return ResponseEntity.ok(result);
   }
   
   @GetMapping("/fetchAlternativeMobileNumber")
   public ResponseEntity<String> fetchAlternativeMobileNumber(@RequestParam("bookingId") Integer bookingId) {
	   logger.info("Fetching Alternative Mobile Number for "+bookingId+" booking Id.");
	   return ResponseEntity.ok(dialysisService.fetchAlternativeMobileNumber(bookingId));
   }
   
   @GetMapping("/company")
	List<Company> getCompanyList() {
		logger.info("Fetching list of companies");

		return hisservice.getCompany();
	}

	@GetMapping("/procedure")
	List<Procedure> getProcedure() {
		logger.info("Fetching list of procedures");

		return hisservice.getProcedure();
	}

	@GetMapping("/station")
	List<Station> getStation() {
		logger.info("Fetching list of stations");

		return hisservice.getStation();
	}

	@GetMapping("/bedtype")
	List<BedType> getBedtype() {
		logger.info("Fetching list of bed types");

		return hisservice.getBedtype();
	}

	@GetMapping("/patientDetils/{uhid}")
	PatientDTO getPatientDetail(@PathVariable("uhid") String uhid) {
		logger.info("Fetching patient details for UHID: {}", uhid);

		String iacode = uhid.split("\\.")[0];
		Integer regNo = Integer.parseInt(uhid.split("\\.")[1]);
		return hisservice.getPatientByUHID(iacode, regNo);
	}
}
