package com.aig.aigone.controller;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.exception.AigOneException;
import com.aig.aigone.model.dto.JwtResponseVO;
import com.aig.aigone.model.dto.OtpRequestDto;
import com.aig.aigone.model.dto.UserDto;
import com.aig.aigone.model.dto.UserLoginDto;
import com.aig.aigone.model.dto.UserLoginJwtResponseVO;
import com.aig.aigone.model.entity.aigone.EventTypeEnum;
import com.aig.aigone.model.entity.aigone.OtpEntity;
import com.aig.aigone.model.entity.aigone.PatientEntity;
import com.aig.aigone.model.entity.aigone.RequestedFromEnum;
import com.aig.aigone.model.entity.aigone.RoleEntity;
import com.aig.aigone.model.entity.aigone.UserEntity;
import com.aig.aigone.repository.aigone.EmployeeTypeRepository;
import com.aig.aigone.repository.aigone.PatientRepository;
import com.aig.aigone.repository.aigone.UserRepository;
import com.aig.aigone.security.JwtUtils;
import com.aig.aigone.service.ICareService;
import com.aig.aigone.service.OtpService;
import com.aig.aigone.service.SystemSettingService;

import io.swagger.v3.oas.annotations.Operation;

@RestController
@RequestMapping("/api/auth")
public class AuthController {

	@Autowired
	private OtpService otpService;

	@Autowired
	private JwtUtils jwtUtils;

	@Autowired
	private PatientRepository patientRepo;

//	@Autowired
//	private ICareService iCareService;

	@Autowired
	private UserRepository userRepo;

	@Autowired
	private EmployeeTypeRepository employeeTypeRepo;

	@Autowired
	private SystemSettingService systemSettingService;

	@Operation(summary = "Login/Authenticate User ")
	@PostMapping("/otp/generate")
	public String generateOtp(@RequestBody OtpRequestDto otpReqDto) {
		return otpService.generateOtp(otpReqDto);
	}

	@Operation(summary = "Register User ")
	@PostMapping("/register")
	public String registration(@RequestBody UserDto userDto) {
		Optional<UserEntity> user = userRepo.findByPhoneNoAndActiveTrue(Long.valueOf(userDto.getPhoneNo()));
		if (user.isPresent() && user.get().isActive()) {
			throw new AigOneException("User Already Registered with given phone no. Please login ");
		}

		UserEntity newUser = new UserEntity();

		if (user.isPresent()) {
			newUser = user.get();

		}
		newUser.setPhoneNo((Long.valueOf(userDto.getPhoneNo())));
		newUser.setName(userDto.getName());
		newUser.setActive(true);

		// for DFCMS
		newUser.setEmployeeId(userDto.getEmployeeId());

//		if (userDto.getEmployeeTypeName() != null) {
//			EmployeeTypeEntity employeeType = employeeTypeRepo.findByTypeName(userDto.getEmployeeTypeName())
//					.orElseThrow(() -> new RuntimeException("Employee type not found"));
//			newUser.setEmployeeType(employeeType);
//		} else {
//			newUser.setEmployeeType(null); // Handle case where employeeType is null
//		}
		newUser.setGender(userDto.getGender());
//		newUser.setRoles(userDto.getRoleNames());
		userRepo.save(newUser);

		return "User Regitered Successfully";
	}

//	@Operation(summary = "Validate OTP")
//	@PostMapping("/otp/validate")
//	public JwtResponseVO validateOtp(@RequestBody OtpRequestDto otpValDto) {
//
//		OtpEntity otpEntity = otpService.findOtpByPhoneNumberAndRequestId(otpValDto.getPhoneNo(),
//				otpValDto.getRequestId());
//		boolean validateOTP = otpEntity.getOtp().equals(otpValDto.getOtp())
//				&& new Date().before(otpEntity.getExpiredTime());
//
//		if (!validateOTP) {
//			throw new AigOneException("Please Enter Valid Otp");
//		}
//		otpEntity.setVerified(true);
//		if (otpEntity.getRequestedFrom().equals(RequestedFromEnum.AIG_MITHR)
//				&& otpEntity.getType().equals(EventTypeEnum.REGISTRATION)) {
//			List<PatientEntity> byPhoneNo = patientRepo.findByPhoneNo(otpEntity.getPhoneNo());
//			if (CollectionUtils.isEmpty(byPhoneNo)) {
//				// create dummy patient
//				PatientEntity patientEntity = new PatientEntity();
//				patientEntity.setPhoneNo(otpEntity.getPhoneNo());
//				patientRepo.save(patientEntity);
//			}
//		}
//		
//		String jwt = jwtUtils.generateJwtToken(otpEntity);
//		boolean icareRegistered = false;
//		boolean employee = false;
//		boolean opConsultant = false;
//		List<String> roles = new ArrayList<>();
//		 List<RoleFeatureResponseDTO> roleFeatureResponseList = new ArrayList<>();
//		
//		if (otpEntity.getRequestedFrom().equals(RequestedFromEnum.AIG_ONE)) {
//			UserEntity user = userRepo.findByPhoneNo(otpValDto.getPhoneNo()).orElseThrow(
//					() -> new UsernameNotFoundException("User Not Found with Phone Number: " + otpValDto.getPhoneNo()));
//		   
//			if (!StringUtils.isEmpty(user.getEmployeeId())) {
//
//				String fetchUserUrl = iCareService.fetchUserUrl(user.getEmployeeId());
//				String empCode = systemSettingService
//						.getSettingValue(SystemSettingsKey.ICARE_EMPLOYEE_USER_URL.getName());
//				
//				if (fetchUserUrl != null && !fetchUserUrl.equalsIgnoreCase(empCode)) {
//					icareRegistered = true;
//				}
//				employee = true;
//
//				Set<RoleEntity> roleEntities = user.getRoles();
//				if (!CollectionUtils.isEmpty(roleEntities)) {
//					opConsultant = roleEntities.stream().anyMatch(r -> r.getCode().equals("OP_CONSULTATION"));
//					roles.addAll(roleEntities.stream().map(RoleEntity::getCode).toList());
//
////					List<Integer> billIds = appointmentDetails.stream().map(PateintPreviousAppointmentsDto::getBillId).collect(Collectors.toList());
//					 for (RoleEntity role : roleEntities) {
//		                    RoleFeatureResponseDTO roleFeatureResponse = roleFeatureService.getRoleFeatures(role.getId());
//		                    Long id = role.getId();
//		   
//		                    roleFeatureResponseList.add(roleFeatureResponse);
//		                }
//				}
//
//			}
//			user.setICareUser(icareRegistered);
//			userRepo.save(user);
//		}
//		JwtResponseVO jwtResponseVO = new JwtResponseVO(jwt, new Date(), otpValDto.getPhoneNo(),
//				String.valueOf(otpValDto.getPhoneNo()), null, icareRegistered, employee, opConsultant,roleFeatureResponseList);
//
//		jwtResponseVO.setRoles(roles);
//
//		return jwtResponseVO;
//	}
//
//	

	@Operation(summary = "Validate OTP")
	@PostMapping("/otp/validate")
	public JwtResponseVO validateOtp(@RequestBody OtpRequestDto otpValDto) {
		try {
			// Validate OTP and handle invalid attempts
			otpService.validateOTP(otpValDto.getPhoneNo(), otpValDto.getOtp(), otpValDto.getRequestId());

			OtpEntity otpEntity = otpService.findOtpByPhoneNumberAndRequestId(otpValDto.getPhoneNo(),
					otpValDto.getRequestId());

			if (otpEntity.getRequestedFrom().equals(RequestedFromEnum.AIG_MITHR)
					&& otpEntity.getType().equals(EventTypeEnum.REGISTRATION)) {
				List<PatientEntity> byPhoneNo = patientRepo.findByPhoneNo(otpEntity.getPhoneNo());
				if (CollectionUtils.isEmpty(byPhoneNo)) {
					// create dummy patient
					PatientEntity patientEntity = new PatientEntity();
					patientEntity.setPhoneNo(otpEntity.getPhoneNo());
					patientRepo.save(patientEntity);
				}
			}

	        String jwt = jwtUtils.generateJwtToken(otpEntity);
	        boolean icareRegistered = false;
	        boolean employee = false;
	        boolean opConsultant = false;
	        List<String> roles = new ArrayList<>();
	        List<String> approvalTabList = new ArrayList<>();

	        // For AIG_ONE: Fetch user and update device token ONLY
	        if (otpEntity.getRequestedFrom().equals(RequestedFromEnum.AIG_ONE)) {
	            UserEntity user = userRepo.findByPhoneNoAndActiveTrue(otpValDto.getPhoneNo())
	                .orElseThrow(() -> new UsernameNotFoundException(
	                    "User Not Found with Phone Number: " + otpValDto.getPhoneNo()
	                ));

	            // Update only essential fields
	            user.setDeviceToken(otpValDto.getDeviceToken());
	            userRepo.save(user);

	            // Use existing iCare status (updated by cron)
	            icareRegistered = user.isICareUser();
	            approvalTabList = user.getICareTabs();
	            employee = !StringUtils.isEmpty(user.getEmployeeId());

	            // Process roles
	            Set<RoleEntity> roleEntities = user.getRoles();
	            if (!CollectionUtils.isEmpty(roleEntities)) {
	                opConsultant = roleEntities.stream()
	                    .anyMatch(r -> "OP_CONSULTATION".equals(r.getCode()));
	                roles = roleEntities.stream()
	                    .map(RoleEntity::getCode)
	                    .collect(Collectors.toList());
	            }
	        }

	        // Build response
	        JwtResponseVO response = new JwtResponseVO(
	            jwt, 
	            new Date(), 
	            otpValDto.getPhoneNo(),
	            String.valueOf(otpValDto.getPhoneNo()),
	            null,
	            icareRegistered,
	            employee,
	            opConsultant,
	            Collections.emptyList() // role features
	        );
	        response.setRoles(roles);
	        response.setApprovalTabs(approvalTabList); 
	        
	        return response;
		} catch (AigOneException e) {
			// Re-throw AigOneException as is since it already contains user-friendly
			// messages
			throw e;
		} catch (Exception e) {
			// Handle unexpected errors with a generic user-friendly message
			throw new AigOneException("An error occurred while validating OTP. Please try again later.");
		}
	}

	/**
	 * Resend OTP
	 * 
	 * @param optRequestDto, the DTO contain the Phone number and transaction id
	 * @return the OTP
	 */
	@Operation(summary = "Resend OTP by phone number and transaction id")
	@PostMapping("/otp/resend")
	public boolean resendOTP(@RequestBody OtpRequestDto optRequestDto) {
		return otpService.resendOTP(optRequestDto);
	}

	@Operation(summary = "login user")
	@PostMapping("/login")
	public UserLoginJwtResponseVO userLogin(@RequestBody UserLoginDto loginDto) {
		otpService.userLogin(loginDto);
		String jwt = jwtUtils.generateJwtToken(loginDto.getUserName(),
				String.valueOf(RequestedFromEnum.THIRD_PARTY_VENDOR));
		return new UserLoginJwtResponseVO(jwt, new Date());
	}

}
