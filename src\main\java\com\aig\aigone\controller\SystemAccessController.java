package com.aig.aigone.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.model.dto.SystemAccessResponse;
import com.aig.aigone.service.SystemAccessService;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/api/system-access")
@RequiredArgsConstructor
@Tag(name = "System Access", description = "APIs for managing system access and permissions")
public class SystemAccessController {

    private final SystemAccessService systemAccessService;

    @GetMapping("/user/{empId}")
    public ResponseEntity<SystemAccessResponse> getUserSystemAccess(@PathVariable String empId) {
        return ResponseEntity.ok(systemAccessService.getUserSystemAccess(empId));
    }
} 