package com.aig.aigone.daycare.repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.aig.aigone.daycare.model.Booking;
import com.aig.aigone.daycare.model.Slot;
@Repository
public interface SlotRepository extends JpaRepository<Slot, Long> {
	
	
	/**
	 * 
	 * @param bookingId
	 * @param dateTime
	 * @return this will return list of slot after current date for a patient
	 */
	@Query("SELECT s FROM Slot s WHERE s.bookingId.id = :bookingId AND s.startingTime >= :dateTime ORDER BY s.startingTime ASC")
    List<Slot> upcomingSlot(@Param("bookingId") Integer bookingId, @Param("dateTime") LocalDateTime dateTime);
	
	 @Query("SELECT DISTINCT s.bookingId FROM Slot s WHERE s.endTime >= :today ")
	    List<Booking> findAllActiveSlotBookingId(@Param("today") LocalDateTime today);
	
	@Query(value="SELECT * FROM dc_slot WHERE DATE(startingtime) = ? and is_available = false;", nativeQuery = true)
    List<Slot> findByDate(LocalDate date);
	
	
	@Query("SELECT s FROM Slot s WHERE s.bookingId = :booking ")
    List<Slot> findByBooking(@Param("booking") Booking booking);
	
	@Query("SELECT s.id FROM Slot s WHERE s.bookingId.id = :bookingId")
    List<Long> findSlotIdsByBookingId(@Param("bookingId") Integer bookingId);

	


//	
//	@Query("SELECT s FROM Slot s WHERE s.booking = :booking")
//	List<Slot> findByBooking(@Param("booking") Booking booking);

	
	@Query("SELECT COUNT(DISTINCT s.bed.id) FROM Slot s WHERE s.isAvailable=false AND "
	        + "s.bed.stationId = :stationId AND s.bed.bedtypeId = :bedTypeId AND s.bed.category = :category AND "
	        + "((s.startingTime = :startTime AND s.endTime = :endTime) "
	        + "OR "
	        + "(s.startingTime > :startTime AND s.startingTime < :endTime) "
	        + "OR "
	        + "(s.endTime > :startTime AND s.endTime < :endTime))")
	Integer getTotalOccupiedBed(@Param("stationId") Integer stationId,
	        @Param("bedTypeId") Integer bedTypeId,
	        @Param("category") Integer category,
	        @Param("startTime") LocalDateTime startTime,
	        @Param("endTime") LocalDateTime endTime);

	
	/**
	 * 
	 * @param startTime
	 * @param endTime
	 * @param bedId
	 * @return This will return all slot booked for bedId in given time frame (startTime and endTime)
	 */
    @Query("SELECT s FROM Slot s WHERE "
            + "s.bed.id = :bedId AND "
            + "s.isAvailable = false AND "
            + "((s.startingTime = :startTime AND s.endTime = :endTime) "
            + "OR "
            + "(s.startingTime > :startTime AND s.startingTime < :endTime) "
            + "OR "
            + "(s.endTime > :startTime AND s.endTime < :endTime))")
    List<Slot> findAvailableSlotsWithinTimeRange(@Param("startTime") LocalDateTime startTime,
                                                 @Param("endTime") LocalDateTime endTime,
                                                 @Param("bedId") Long bedId);
    
    

    
    
    /**
     * 
     * @param stationId
     * @param bedTypeId
     * @param startTime
     * @param endTime
     * @return list of all booking id which are in specific time frame of startTime and endTime
     */
    
    @Query("SELECT  s.bookingId.id " +
            "FROM Slot s " +
            "WHERE s.bed.stationId = :stationId " +
            "AND s.bed.bedtypeId = :bedTypeId " +
            "AND s.startingTime = :startTime "+
            "AND s.endTime = :endTime " +
            "ORDER BY s.bookingId.id ASC")
    List<Integer> getBookingIdForTimeFrame(@Param("stationId") Integer stationId,
                                @Param("bedTypeId") Integer bedTypeId,
                                @Param("startTime") LocalDateTime startTime,
                                @Param("endTime") LocalDateTime endTime);

    
    
    @Query("SELECT s FROM Slot s WHERE s.bookingId = :booking")
    List<Slot> findByBookingId(@Param("booking") Integer booking);
        
    @Query("SELECT s FROM Slot s WHERE s.isAvailable=false AND "
            + "s.bed.id = :bedId AND s.startingTime = :startTime AND s.endTime = :endTime and s.isAvailable=false")
    Optional<Slot> findSlotWithSameTime(@Param("startTime") LocalDateTime startTime,
                                        @Param("endTime") LocalDateTime endTime,
                                        @Param("bedId") Long bedId);
    @Query(value="SELECT * FROM dc_slot WHERE DATE(startingtime) >= ? and is_available = false;", nativeQuery = true)
    List<Slot> findAllActiveSlotsToday(LocalDate date);
    
    @Query("SELECT s FROM Slot s WHERE s.bookingId = :booking")
    List<Slot> findCancelledByBooking(@Param("booking") Booking booking);
    
    @Query("SELECT s FROM Slot s WHERE s.bookingId = :booking and s.isAvailable=false")
    List<Slot> findAllActiveSlotsForBookingId(@Param("booking") Booking booking);
    
    @Query(value = "SELECT EXISTS ( " +
            "    SELECT 1 " +
            "    FROM dc_slot ds " +
            "    JOIN dc_booking db ON ds.bookingid_id = db.id " +
            "    WHERE DATE(ds.startingtime) = ?1 " +
            "      AND DATE(ds.endtime) = ?2 " +
            "      AND ds.is_available = false " +
            "      AND db.uhid = ?3 " +
            "      AND ds.id <> ?4 " + // Exclude the slot being rescheduled
            ") AS record_exists;", nativeQuery = true)
    Boolean checkIfSlotExistOnSameDay(LocalDate startDate, LocalDate endDate, String uhid, Long slotId);

    @Query(value = "WITH all_beds AS (\r\n"
    		+ "    SELECT generate_series(1, 14) AS bed_id\r\n"
    		+ "),\r\n"
    		+ "slot_data AS (\r\n"
    		+ "    SELECT\r\n"
    		+ "        s.bed_id,\r\n"
    		+ "        s.startingtime,\r\n"
    		+ "        s.endtime,\r\n"
    		+ "        s.is_available,\r\n"
    		+ "        s.bookingid_id,\r\n"
    		+ "        s.id AS slotId,\r\n"
    		+ "        b.uhid\r\n"
    		+ "    FROM dc_slot s\r\n"
    		+ "    LEFT JOIN dc_booking b ON s.bookingid_id = b.id\r\n"
    		+ "    WHERE \r\n"
    		+ "        CAST(s.startingtime AS DATE) = CAST(:date AS DATE)\r\n"
    		+ "        AND CAST(s.startingtime AS TIME) = CAST(:start_time AS TIME)\r\n"
    		+ "        AND CAST(s.endtime AS TIME) = CAST(:end_time AS TIME)\r\n"
    		+ "        AND s.is_available = FALSE\r\n"
    		+ ")\r\n"
    		+ "SELECT\r\n"
    		+ "    b.bed_id,\r\n"
    		+ "    d.bedname,\r\n"
    		+ "    d.category,\r\n"
    		+ "    COALESCE(s.is_available, TRUE) AS is_available,\r\n"
    		+ "    s.bookingid_id,\r\n"
    		+ "    s.slotId,\r\n"
    		+ "    s.uhid\r\n"
    		+ "FROM all_beds b\r\n"
    		+ "LEFT JOIN dc_bed d ON b.bed_id = d.bedid\r\n"
    		+ "LEFT JOIN slot_data s ON b.bed_id = s.bed_id\r\n"
    		+ "ORDER BY b.bed_id;\r\n"
    		+ "", 
            nativeQuery = true)
	List<Map<String, Object>> getBedAndSlotAvailabilityPerSlotTiming(
	        @Param("date") String date,
	        @Param("start_time") String startTime,
	        @Param("end_time") String endTime
	);
    
    @Query(value="select * from dc_slot where date(startingtime) >= ? and date(startingtime) <= ?;",nativeQuery=true)
    List<Slot> getAppointmentsByDate(LocalDate startDate, LocalDate endDate);

    @Query(value = "select * from dc_slot ds where bookingid_id = ? and is_available = false;",nativeQuery=true)
	List<Slot> findAllActiveSlotsForBookingId(Integer bookingId);
    
    @Query(value="select * from dc_slot ds where date(startingtime) = current_date and is_available = false;",nativeQuery = true)
    List<Slot> getTodayActiveSlots();
}
