package com.aig.aigone.controller;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.model.dto.FeatureDTO;
import com.aig.aigone.model.entity.aigone.FeatureRequest;
import com.aig.aigone.service.FeatureService;

import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

//FeatureController.java
@Slf4j
@RestController
@RequestMapping("/api/features")
@RequiredArgsConstructor
@Tag(name = "Feature Management", description = "APIs for managing features and their assignments")
public class FeatureController {

    private final FeatureService featureService;

    @GetMapping
    public ResponseEntity<List<FeatureDTO>> getAllFeatures() {
        return ResponseEntity.ok(featureService.getAllFeatures());
    }

    @GetMapping("/{id}")
    public ResponseEntity<FeatureDTO> getFeatureById(@PathVariable Long id) {
        return ResponseEntity.ok(featureService.getFeatureById(id));
    }

    @PostMapping
    public ResponseEntity<FeatureDTO> createFeature(@Valid @RequestBody FeatureRequest request) {
        return new ResponseEntity<>(featureService.createFeature(request), HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    public ResponseEntity<FeatureDTO> updateFeature(
            @PathVariable Long id, 
            @Valid @RequestBody FeatureRequest request) {
        return ResponseEntity.ok(featureService.updateFeature(id, request));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteFeature(@PathVariable Long id) {
        featureService.deleteFeature(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/module/{moduleId}")
    public ResponseEntity<List<FeatureDTO>> getFeaturesByModule(@PathVariable Long moduleId) {
        return ResponseEntity.ok(featureService.getFeaturesByModule(moduleId));
    }
}