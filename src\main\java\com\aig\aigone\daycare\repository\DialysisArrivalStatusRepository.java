package com.aig.aigone.daycare.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.aig.aigone.daycare.model.DialysisArrivalStatus;
import com.aig.aigone.daycare.model.Slot;
@Repository
public interface DialysisArrivalStatusRepository extends JpaRepository<DialysisArrivalStatus, Long> {

    // Custom query to find DialysisArrivalStatus by Slot
    @Query("SELECT d FROM DialysisArrivalStatus d WHERE d.slot = :slot")
    DialysisArrivalStatus findBySlot(@Param("slot") Slot slot);

    @Query(value="select * from dc_dialysisarrivalstatus dd where status = 'COMPLETED' and is_notification_sent=false and date(endtime)=current_date ;",nativeQuery=true)
	List<DialysisArrivalStatus> getDialysisCompletedRecords();
}
