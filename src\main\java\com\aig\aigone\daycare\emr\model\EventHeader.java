package com.aig.aigone.daycare.emr.model;



import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
@Entity
@Table(name = "hp_care_events_hdr")
public class EventHeader {
	
	@Id
	@Column(name = "event_accn_no")
	private Integer accountNumber;
	
	
	@Column(name = "patient_id")
	private String uhid;
	
	
	@Column(name = "event_trigger_type")
	private String triggerType;
	
	@Column(name = "evnt_dt_time")
	private LocalDateTime eventDateTime;
	

}
