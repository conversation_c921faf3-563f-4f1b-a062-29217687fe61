package com.aig.aigone.controller;

import com.aig.aigone.model.dto.ResponseDTO;
import com.aig.aigone.model.dto.SurveyResultSummaryDTO;
import com.aig.aigone.model.dto.UserSessionVO;
import com.aig.aigone.security.SecurityUtil;
import com.aig.aigone.service.ResponseService;
import com.aig.aigone.service.SurveyService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/responses")
@RequiredArgsConstructor
@Tag(name = "Response Management", description = "APIs for managing survey responses")
public class ResponseController {

    private final ResponseService responseService;
//    private final SurveyService surveyService;

    @PostMapping
    @Operation(summary = "Submit a new response")
    public ResponseEntity<ResponseDTO> submitResponse(@RequestBody ResponseDTO responseDTO) {
        UserSessionVO currentUser = SecurityUtil.getCurrentUser();
        responseDTO.setEmployeeId(currentUser.getEmployeeId());
        return ResponseEntity.ok(responseService.submitResponse(responseDTO));
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get a response by ID")
    public ResponseEntity<ResponseDTO> getResponse(@PathVariable Long id) {
        return ResponseEntity.ok(responseService.getResponse(id));
    }

    @GetMapping("/survey/{surveyId}")
    @Operation(summary = "Get all responses for a survey")
    public ResponseEntity<List<ResponseDTO>> getResponsesBySurvey(@PathVariable Long surveyId) {
        return ResponseEntity.ok(responseService.getResponsesBySurvey(surveyId));
    }

    @GetMapping("/my-responses")
    @Operation(summary = "Get all responses for current user")
    public ResponseEntity<List<ResponseDTO>> getMyResponses() {
        UserSessionVO currentUser = SecurityUtil.getCurrentUser();
        return ResponseEntity.ok(responseService.getResponsesByEmployeeId(currentUser.getEmployeeId()));
    }

    @PostMapping("/{id}/evaluate")
    @Operation(summary = "Evaluate a response and return the result summary")
    public ResponseEntity<SurveyResultSummaryDTO> evaluateResponse(@PathVariable Long id) {
        return ResponseEntity.ok(responseService.evaluateSurvey(id));
    }

    @GetMapping("/{id}/evaluate")
    @Operation(summary = "Get the evaluation result summary for a response")
    public ResponseEntity<SurveyResultSummaryDTO> getEvaluation(@PathVariable Long id) {
        return ResponseEntity.ok(responseService.getSurveyResultSummary(id));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete a response")
    public ResponseEntity<Void> deleteResponse(@PathVariable Long id) {
        responseService.deleteResponse(id);
        return ResponseEntity.ok().build();
    }
} 