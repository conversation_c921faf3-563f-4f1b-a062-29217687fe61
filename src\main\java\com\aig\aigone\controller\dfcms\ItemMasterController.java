package com.aig.aigone.controller.dfcms;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.model.dto.dfcms.FoodItemAddRequestDto;
import com.aig.aigone.model.entity.dfcms.FoodItem;
import com.aig.aigone.service.dfcms.FoodItemService;

import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;

@Slf4j 
@RestController
@RequestMapping("/api/dfcms/itemMaster")
public class ItemMasterController {

	@Autowired
	private FoodItemService foodItemService;

	// Add a new food item
	@PreAuthorize("hasAnyRole('DC_SALUBRIS','DC_ADMIN')")
	@Operation(summary = "Add a New Food Item")
	@PostMapping("/add")
	public ResponseEntity<?> addFoodItem(@RequestBody FoodItemAddRequestDto foodItem) {
	    log.info("Received request to add a new food item: {}", foodItem);
	    FoodItem newFoodItem = foodItemService.addFoodItem(foodItem);
	    return ResponseEntity.ok(newFoodItem);
	}


	// Edit an existing food item
	@PreAuthorize("hasAnyRole('DC_SALUBRIS','DC_ADMIN')")
	@Operation(summary = "Edit an Existing Food Item")
	@PutMapping("/edit/{id}")
	public ResponseEntity<?> editFoodItem(@PathVariable Integer id, @RequestBody FoodItemAddRequestDto foodItem) {
	    log.info("Received request to edit food item with ID: {} and details: {}", id, foodItem);
	    FoodItem updatedFoodItem = foodItemService.editFoodItem(id, foodItem);
	    return ResponseEntity.ok(updatedFoodItem);
	}


	// List food items by status (active/inactive)
	@GetMapping("/getListItems")
	@Operation(summary = "List Food Items by Status (Active/Inactive)")
	public ResponseEntity<?> listFoodItems(@RequestParam(required = false) String status) {
	    log.info("Fetching food items with status: {}", status);
	    List<FoodItem> foodItems = foodItemService.listFoodItems(status);
	    return ResponseEntity.ok(foodItems);
	}

	
	// Fetch a food item by ID
	@GetMapping("/getItem/{id}")
	@Operation(summary = "Fetch a Food Item by ID")
	public ResponseEntity<?> getFoodItemById(@PathVariable Integer id) {
	    log.info("Fetching food item with ID: {}", id);
	    FoodItem foodItem = foodItemService.getFoodItemById(id);
	    return ResponseEntity.ok(foodItem);
	}



	// Delete a food item by ID
	@PreAuthorize("hasAnyRole('DC_SALUBRIS','DC_ADMIN')")
	@Operation(summary = "Delete a Food Item")
	@DeleteMapping("/delete/{id}")
	public ResponseEntity<?> deleteFoodItem(@PathVariable Integer id) {
	    log.info("Deleting food item with ID: {}", id);
	    foodItemService.deleteFoodItem(id);
	    return ResponseEntity.noContent().build();
	}

}
