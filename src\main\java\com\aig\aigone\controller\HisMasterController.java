package com.aig.aigone.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.model.dto.his.AIG_SP_AllDiagnosticServicesDto;
import com.aig.aigone.model.dto.his.AIG_SP_GetDrugDetails;
import com.aig.aigone.model.dto.his.DoctorSignDto;
import com.aig.aigone.model.dto.his.HighValueLabOrdersDto;
import com.aig.aigone.model.dto.his.OrganizationDto;
import com.aig.aigone.model.dto.his.PatientHisVisitDetailsDto;
import com.aig.aigone.model.dto.his.SurgeryDetails;
import com.aig.aigone.model.dto.hismaster.DepartmentDoctorsDto;
import com.aig.aigone.model.dto.hismaster.DiagnosisResponseDto;
import com.aig.aigone.model.dto.hismaster.DoctorResponseDto;
import com.aig.aigone.model.dto.hismaster.DrugDiagnosticApiResponseDto;
import com.aig.aigone.model.dto.hismaster.DrugDiagnosticOrdersPrism;
import com.aig.aigone.model.dto.hismaster.OpConsultNoteReqToEmrHis;
import com.aig.aigone.model.dto.hismaster.OpConsultResToPrismFromHisEmrDto;
import com.aig.aigone.model.dto.hismaster.SpecialityResponseDto;
import com.aig.aigone.service.HisMasterService;
import com.aig.aigone.service.HisService;

import io.swagger.v3.oas.annotations.Operation;

@RestController
@RequestMapping("/api/his/master")
public class HisMasterController {

	private static final Logger logger = LoggerFactory.getLogger(HisMasterController.class);

	@Autowired
	private HisMasterService hisMasterService;

	@Autowired
	private HisService hisService;

	@Operation(summary = "fetch master specialities")
	@GetMapping("/specialities")
	public List<SpecialityResponseDto> fetchMasterSpecialities(
			@RequestParam(name = "speciality", required = false) String speciality) {
		logger.info("Fetching master specialities with filter: {}", speciality);
		List<SpecialityResponseDto> response = hisMasterService.fetchMasterSpecialities(speciality);
		logger.info("Fetched {} specialities", response.size());
		return response;
	}

	@Operation(summary = "fetch master diagnosis")
	@GetMapping("/diagnosis")
	public List<DiagnosisResponseDto> fetchMasterDiagnosis(
			@RequestParam(name = "diagnosis", required = false) String diagnosis, 
			@RequestParam(name = "icd", required = false) String icd) {
		logger.info("Fetching master diagnosis with filter: {}", diagnosis);
		List<DiagnosisResponseDto> response = hisMasterService.fetchMasterDiagnosis(diagnosis, icd);
		logger.info("Fetched {} diagnoses", response.size());
		return response;
	}

	@Operation(summary = "fetch speciality consultants")
	@GetMapping("/doctor/{speciality}")
	public List<DoctorResponseDto> fetchSpecialityDoctors(
			@PathVariable(name = "speciality", required = true) String speciality) {
		logger.info("Fetching doctors for speciality: {}", speciality);
		List<DoctorResponseDto> response = hisMasterService.fetchSpecialityDoctors(speciality);
		logger.info("Fetched {} doctors for speciality {}", response.size(), speciality);
		return response;
	}

	@Operation(summary = "fetch procedures")
	@GetMapping("/procedures")
	public List<String> fetchMasterProcedures(@RequestParam(name = "procedure", required = false) String procedure) {
		logger.info("Fetching procedures with filter: {}", procedure);
		List<String> response = hisMasterService.fetchMasterProcedures(procedure);
		logger.info("Fetched {} procedures", response.size());
		return response;
	}

	@Operation(summary = "fetch doctors")
	@GetMapping("/doctors")
	public List<DoctorResponseDto> fetchMasterDoctors(
			@RequestParam(name = "doctorName", required = false) String doctorName) {
		logger.info("Fetching doctors with name filter: {}", doctorName);
		List<DoctorResponseDto> response = hisMasterService.fetchMasterDoctors(doctorName);
		logger.info("Fetched {} doctors", response.size());
		return response;
	}

	@Operation(summary = "fetch AllDiagnosticServices")
	@GetMapping("/Diagsvc")
	public List<AIG_SP_AllDiagnosticServicesDto> fetchAllDiagnosticServices() {
		logger.info("Fetching all diagnostic services");
		List<AIG_SP_AllDiagnosticServicesDto> response = hisMasterService.fetchAllDiagnosticServices();
		logger.info("Fetched {} diagnostic services", response.size());
		return response;
	}

	@Operation(summary = "fetch AIG_SP_GetDrugDetails")
	@GetMapping("/drugdetailss")
	public List<AIG_SP_GetDrugDetails> fetchAIG_SP_GetDrugDetails() {
		logger.info("Fetching drug details");
		List<AIG_SP_GetDrugDetails> response = hisMasterService.fetchAIG_SP_GetDrugDetails();
		return response;
	}

	@Operation(summary = "fetch department doctors")
	@GetMapping("/department/doctors")
	public List<DepartmentDoctorsDto> fetchDepartmentDoctors(
			@RequestParam(name = "department", required = true) String department) {
		return hisMasterService.fetchDepartmentDoctors(department);
	}

	@PostMapping("/drugDiagnosticOrder")
	public ResponseEntity<DrugDiagnosticApiResponseDto> postDrugDiagnosticOrdersPrism(
			@RequestBody DrugDiagnosticOrdersPrism drugDiagnosticOrdersPrism) {

		DrugDiagnosticApiResponseDto responseDto = hisService
				.postDrugDiagnosticOrdersPrismImpl(drugDiagnosticOrdersPrism);
		return ResponseEntity.ok(responseDto);
	}

	@PostMapping("/opConsultNoteHisEmr")
	public ResponseEntity<OpConsultResToPrismFromHisEmrDto> postOpConsultNotetoHisFromPrism(
			@RequestBody OpConsultNoteReqToEmrHis opConsultNoteReqToEmrHis) {
		OpConsultResToPrismFromHisEmrDto responseDto = hisService
				.opConsultResToPrismFromHisEmr(opConsultNoteReqToEmrHis);

		return ResponseEntity.ok(responseDto);

	}
	
	@Operation(summary = "fetch Surgeries")
	@GetMapping("/getSurgeries")
	public List<SurgeryDetails> getSurgeries() {
		logger.info("Fetching drug details");
		return hisMasterService.getSurgeries();
	}
	
	 @GetMapping("/highValueLab")
	    public List<HighValueLabOrdersDto> getHighValueLabOrders() {
	        return hisMasterService.getHighValueLabOrders();
	    }
	 @GetMapping("/doctorSign")
	 public List<DoctorSignDto> getDoctorSignDetails(@RequestParam String employeeId) {
	     return hisMasterService.getDoctorSignDetails(employeeId);
	 }
	 
	 @Operation(summary = "Get visit details of patient")
	 @GetMapping("/patient/fetchVisitDetails")
	 public List<PatientHisVisitDetailsDto> fetchHisPatientVisitDetails(@RequestParam(name="InterfaceID", required= true) String InterfaceID, @RequestParam(name="uhId",required =true)String uhId){
		 return hisMasterService.fetchHisPatientVisitDetails(InterfaceID,uhId);
	 }
	 
	 @Operation(summary = "fetch master Organization ")
		@GetMapping("/fetchAllOrganization")
		public List<OrganizationDto> fetchAllOrganization() {
			return hisMasterService.fetchAllOrganization();
		}
	 
}
