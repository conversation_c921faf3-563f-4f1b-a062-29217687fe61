package com.aig.aigone.queue.apiUtil;

public class QueueApiUtil {
	
	public static final String STAFF_AUTH_LOGIN = """
			mutation AuthTokenService($empId: String!, $superUserName: String!, $superUserPwd: String! ) {
					  authTokenService(
					    empId: $empId
					    superUserName: $superUserName
					    superUserPwd: $superUserPwd
					  ) {
					    message
					    success
					    data
					  }
					}
			""";
	
	
	public static final String STAFF_ALLOCATED_QUEUES = """
			query GetStaffUserAllocatedQueues{
			  getStaffUserAllocatedQueues {
				    success
				    message
				    data {
				      completedCount
				      lastTokenCalledAt
				      lastCalledTokenNo
				      servingToken
					  nextToken
                      expectedQueueCount
					  waitingQueueCount
					  servingQueueCount
					  queueStatus
				      allocateCounterAt
				      avgProcedureTime
				      capacity
				      cluster {
				        cluster
				        floor
				        id
				        tower
				      }
				      counters {
				        code
				        id
				        number
				        status
				      }
				      id
				      preRequesiteConditions
				      queueCode
				      queueName
				      queueType
				      serviceType
				      showPatientName
				      staffUser {
				        empId
				        id
				        name
				      }
				      status
				      supervisor
				      tests {
				        categories
				        code
				        id
				        type
				        name
				      }
				      upcomingPatients
				      updatedAt
				      waitingCapacity
				    }
				  }
				}""";
	
	public static final String GET_QUEUE_DETAILS = """
			query MyQuery($queueCode: String = "") {
  getQueueDetails(queueCode: $queueCode) {
    data {
      completedCount
				      lastTokenCalledAt
				      lastCalledTokenNo
				      servingToken
					  nextToken
                      expectedQueueCount
					  waitingQueueCount
					  servingQueueCount
					  queueStatus
				      allocateCounterAt
				      avgProcedureTime
				      capacity
				      cluster {
				        cluster
				        floor
				        id
				        tower
				      }
				      counters {
				        code
				        id
				        number
				        status
				      }
				      id
				      preRequesiteConditions
				      queueCode
				      queueName
				      queueType
				      serviceType
				      showPatientName
				      staffUser {
				        empId
				        id
				        name
				      }
				      status
				      supervisor
				      tests {
				        categories
				        code
				        id
				        type
				        name
				      }
				      upcomingPatients
				      updatedAt
				      waitingCapacity
				    }
				    message
				    success
				  }
				}""";
	
	public static final String QUEUE_WEIGHT_AGE = """
					query GetQueueQeightAge{
							  getQueueWeightage{
							    success
							    message
							    data{
							      id
							      name
							      code
							      weightage
							    }
							  }
							
							}""";
	public static final String GET_PATIENT_QUEUE = """
			query MyQuery($uhid: String = "") {
			  getPatientQueue(uhid: $uhid) {
			    data {
			      queueId
			      tokenNo
			      userQueueId
			      prerequisitesConditions
			    }
			    message
			    success
			  }
			}""";
	public static final String GET_USER_SERVICES ="""
			query GetUserServices($userQueueId: Int!) {
			getUserServices(userQueueId: $userQueueId) {
			data {
				categories
				id
				name
				status
				}
				message
				success
				}
			}""";

	public static final String ADD_USER_QUEUE = """
			mutation AddUserQueue($uhid: String = "", $tokenId: String = "", $prerequisitesConditions: [String!] = "", $queueId: Int = 0) {
				addUserQueue(qrDetails: {prerequisitesConditions: $prerequisitesConditions, uhid: $uhid, tokenId: $tokenId}
			 	queueId: $queueId) {
					data {
						counterName
						id
						prerequisitesConditions
						queueStepId
						startTime
						status
						tokenNo
						tokenId
						user {
								id
								name
								phoneNumber
								umrNo
							}
						weightage
						weightageId
						queue {
								avgProcedureTime
								capacity
								id
								queueCode
								queueName
								showPatientName
								status
								upcomingPatients
								waitingCapacity
							}
						}
					message
					success
				}
			}""";
	
	public static final String EXIT_USER_QUEUE = """
			mutation MyMutation($prerequisitesConditions: [String!] = "", $tokenId: String = "", $uhid: String = "", $queueId: Int = 0, $userServiceIds: [Int!] = null, $userQueueId: String ="") {
			  exitUserQueue(
			    qrDetails: {prerequisitesConditions: $prerequisitesConditions, tokenId: $tokenId, uhid: $uhid, userQueueId: $userQueueId}
			    queueId: $queueId
			    userServiceIds: $userServiceIds
			  ) {
			    message
			    success
			  }
			}""";
	
	public static final String UPDATE_USER_QUEUE_ASSESMENT = """
			mutation MyMutation ($updateType: String = "", $userQueueId: Int = 0){
			  updateUserQueueTimestamp(updateType: $updateType, userQueueId: $userQueueId) {
			    message
			    success
			  }
			}
			""";
	
	public static final String GET_USER_COUNTERS = """
			query MyQuery($queueId: Int = 0) {
			  getUserQueueCounters(queueId: $queueId) {
			    message
			    success
			    data {
			      counterId
			      counterName
			      counterStatus
			    }
			  }
			}
			""";
	public static final String GET_STAFF_USER_ALLOCATED_RESOURCES = """
			query MyQuery($resourceCodes: [String!] = []) {
			  getStaffUserAllocatedRescouce(resourceCodes: $resourceCodes) {
			    data
			    message
			    success
			  }
			}
			""";
	
	public static final String GET_USER_QUEUE_ALL = """
			query MyQuery($queueCode: String = "") {
			  getUserQueueAll(queueCode: $queueCode) {
			    message
			    success
			    data {
			      id
			      uhid
			      status
			      tokenNo
			      weightage
			      vitalsDatetime
			      appointmentDateTime
			      phyAssDatetime
			      userQueueId
			      queueId
			      tokenId
			      billId
				  preCheckStatus
			    }
			  }
			}
			""";
	public static final String GET_TOKEN_DETAILS = """
			query GetTokenDetailsUserServices($tokenId: Int!) {
			  getTokenDetails(tokenId: $tokenId) {
			    data {
			      billNo
			      createdAt
			      id
			      phoneNumber
			      services {
			        categories
			        id
			        serviceId
			        serviceName
			        status
			      }
			      tokenId
			      tokenNo
			      umrNo
			      userName
			      userQueue {
			        weightage
			        weightageId
			        id
			        queueId
			        prerequisitesConditions
			      }
			    }
			    message
			    success
			  }
			}
			""";
	
	public static final String UPDATE_PRE_CHECK = """
			mutation MyMutation($userQueueId: Int!, $userServiceIds: [Int!]) {
			  updatePreCheckStatus(userQueueId: $userQueueId, userServiceIds: $userServiceIds) {
			    message
			    success
			    data
			  }
			}
			""";
	
	public static final String GET_COUNTER_DETAILS = """
			query MyQuery($queueCounterId: Int = 0) {
			  getCounterDetails(queueCounterId: $queueCounterId) {
			    message
			    success
			    data {
			      counterName
			      counterId
			      counterStatus
			      queueId
			      queueName
			      userQueue {
			        tokenId
			        tokenNo
			        userQueueId
			        userQueueStatus
			        userService {
			          serviceName
			          userServiceId
			        }
			        user {
			          id
			          name
			          phoneNumber
			          umrNo
			        }
			      }
			    }
			  }
			}
			""";
	
	public static final String GET_COMMON_REMARKS = """
			query MyQuery($type: String = "") {
			  getCommonRemarks(type: $type) {
			    data
			    message
			    success
			  }
			}
			""";
	
	public static final String UPDATE_COUNTER = 
			"""
			mutation MyMutation2($counterId: Int = 0, $status: String = "") {
			  updateCounter(counterId: $counterId, status: $status) {
			    data
			    message
			    success
			  }
			}
			""";
	public static final String FETCH_TOKEN_DETAILS_BY_UHID = """
			query MyQuery($uhid: String = "") {
			  getUserQueueMsg(uhid: $uhid) {
			    data {
			      msg
			      tokenNo
			      tokenCreatedAt
			      services {
			        completedTime
			        id
			        serviceId
			        serviceName
			        serviceType
			        status
			      }
			    }
			    message
			    success
			  }
			}""";
	public static final String PRE_CHECK_CALL_NEXT = """
			mutation CallNextPreCheck($queueId: Int!) {
			    callNextPreCheck(queueId: $queueId) {
			      data {
			        id
			        tokenId
					tokenNo
			        prerequisitesConditions
			        user {
			          id
			          name
			          phoneNumber
			          umrNo
			        }
			        queueStepId
			      }
			      message
			      success
			    }
			  }""";
	
	public static final String GET_USER_QUEUE = """
			query GetUserQueue($queueId: Int = 0, $queueIds: [Int!] = null, $assignmentOption: String = "", $preCheckStatus: String = "",$queueCode: String ="") {
			getUserQueue(queueIds: $queueIds
						    queueId: $queueId
						    assignmentOption: $assignmentOption
						    preCheckStatus: $preCheckStatus
						    queueCode:$queueCode) {
					data {
						appointmentDateTime
				        counterName
				        id
				        phyAssDatetime
				        preCheckStatus
				        prerequisitesConditions
						queue {
						avgProcedureTime
						capacity
						id
						queueCode
						queueName
						showPatientName
						status
						upcomingPatients
						waitingCapacity
					}
					queueStepId
					startTime
					status
					tokenId
					tokenNo
					weightage
					weightageId
					vitalsDatetime
					appointmentDateTime
					phyAssDatetime
					preCheckStatus
					user {
							id
							name
							phoneNumber
							umrNo
						}
					}
			message
			success
			}
			}
			""";

	public static final String UPDATE_USER_QUEUE = """
			mutation UpdateUserQueue($queueWeightageAction: String, $queueWeightageActionId: Int, $userQueueId: Int!,$remarks: String) {
			updateUserQueue(
			userQueueId: $userQueueId
			queueWeightageAction: $queueWeightageAction
			queueWeightageActionId: $queueWeightageActionId
			remarks:$remarks
			) {
			message
			success
			data {
			counterName
			id
			prerequisitesConditions
			queue {
			avgProcedureTime
			capacity
			id
			queueCode
			queueName
			showPatientName
			status
			upcomingPatients
			waitingCapacity
			}
			user {
			id
			name
			phoneNumber
			umrNo
			}
			weightage
			weightageId
			queueStepId
			startTime
			status
			tokenId
			tokenNo
			}
			}
			}
			""";

		public static final String GENERATE_BAR_CODE = """
				mutation GenerateBarCode($userServiceIds: [Int!]!) {
				  generateBarCode(userServiceIds: $userServiceIds) {
				    message
				    success
				    data {
				      sampleNo
				      userServiceId
				    }
				  }
				}
				""";


}
