package com.aig.aigone.bloodbank.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.aig.aigone.model.entity.aigone.Auditable;
@Data
//@Getter
//@Setter
@Entity
@Table(name = "donors")
public class DonorEntity extends  Auditable implements Serializable {

	private static final long serialVersionUID = 1L;
	
	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
	
    private Long id;
    private String name;
    private String mobile;
    private String bloodGroup;
    private String age;
    private String gender;
    private LocalDate lastDonationDate;
    private String employeeId;
    private String donorType;
    private String coMorbities;
    private String medications;
    private LocalDate registrationDate;
    @Column(nullable = false)
    private Boolean eligibleForDonation;
    private Boolean isActive;
    private String lastDonationText;
	
    public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getBloodGroup() {
		return bloodGroup;
	}
	public void setBloodGroup(String bloodGroup) {
		this.bloodGroup = bloodGroup;
	}
	public String getAge() {
		return age;
	}
	public void setAge(String age) {
		this.age = age;
	}
	public String getGender() {
		return gender;
	}
	public void setGender(String gender) {
		this.gender = gender;
	}
	public LocalDate getLastDonationDate() {
		return lastDonationDate;
	}
	public void setLastDonationDate(LocalDate lastDonationDate) {
		this.lastDonationDate = lastDonationDate;
	}
	public String getEmployeeId() {
		return employeeId;
	}
	public void setEmployeeId(String employeeId) {
		this.employeeId = employeeId;
	}
	public String getDonorType() {
		return donorType;
	}
	public void setDonorType(String donorType) {
		this.donorType = donorType;
	}
	public String getCoMorbities() {
		return coMorbities;
	}
	public void setCoMorbities(String coMorbities) {
		this.coMorbities = coMorbities;
	}
	public String getMedications() {
		return medications;
	}
	public void setMedications(String medications) {
		this.medications = medications;
	}
	public LocalDate getRegistrationDate() {
		return registrationDate;
	}
	public void setRegistrationDate(LocalDate registrationDate) {
		this.registrationDate = registrationDate;
	}
	public Boolean getEligibleForDonation() {
		return eligibleForDonation;
	}
	public void setEligibleForDonation(Boolean eligibleForDonation) {
		this.eligibleForDonation = eligibleForDonation;
	}
	
	@PrePersist
	public void prePersist() {
		if(registrationDate == null) {
			 registrationDate = LocalDate.now(); 
		}
	}
	public Boolean getIsActive() {
		return isActive;
	}
	public void setIsActive(Boolean isActive) {
		this.isActive = isActive;
	}
    
    
    // Getters and Setters
}
