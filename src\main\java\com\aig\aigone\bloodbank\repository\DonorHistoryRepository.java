package com.aig.aigone.bloodbank.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.aig.aigone.bloodbank.model.entity.DonorHistory;

@Repository
public interface DonorHistoryRepository extends JpaRepository<DonorHistory, Long> {

	@Query(value = "SELECT COALESCE(MAX(request_id), 0) FROM donor_history", nativeQuery = true)
	Long findMaxRequestId();
}
