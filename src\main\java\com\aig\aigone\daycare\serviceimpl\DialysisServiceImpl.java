package com.aig.aigone.daycare.serviceimpl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import com.aig.aigone.daycare.dto.AvailabilityDTO;
import com.aig.aigone.daycare.dto.BookingDTO;
import com.aig.aigone.daycare.dto.BookingStatusResponseDTO;
import com.aig.aigone.daycare.dto.CancelledAppointmentsListDTO;
import com.aig.aigone.daycare.dto.DialysisPatientDTO;
import com.aig.aigone.daycare.dto.DialysisSlotDTO;
import com.aig.aigone.daycare.dto.ExistingBookingDetailsDTO;
import com.aig.aigone.daycare.dto.PatientDTO;
import com.aig.aigone.daycare.dto.RemainderDTO;
import com.aig.aigone.daycare.dto.RescheduleBookingDTO;
import com.aig.aigone.daycare.dto.TimeDTO;
import com.aig.aigone.daycare.dto.WaitingListAppointmentsDTO;
import com.aig.aigone.daycare.dto.WaitingListDTO;
import com.aig.aigone.daycare.exception.BookingNotFoundException;
import com.aig.aigone.daycare.exception.ExistingBookingDetailsException;
import com.aig.aigone.daycare.exception.InvalidBookingId;
import com.aig.aigone.daycare.model.Booking;
import com.aig.aigone.daycare.model.DailysisFrequency;
import com.aig.aigone.daycare.model.DailysisSlotTiming;
import com.aig.aigone.daycare.model.DialysisArrivalStatus;
import com.aig.aigone.daycare.model.DialysisBooking;
import com.aig.aigone.daycare.model.PaymentMethod;
import com.aig.aigone.daycare.model.Slot;
import com.aig.aigone.daycare.service.WhatsAppService;
import com.aig.aigone.daycare.repository.BedRepository;
import com.aig.aigone.daycare.repository.BookingRepository;
import com.aig.aigone.daycare.repository.DialysisArrivalStatusRepository;
import com.aig.aigone.daycare.repository.DialysisBookingRepository;
import com.aig.aigone.daycare.repository.FrequencyRepository;
import com.aig.aigone.daycare.repository.PaymentMethodRepository;
import com.aig.aigone.daycare.repository.SlotRepository;
import com.aig.aigone.daycare.repository.SlotTimingRepository;
import com.aig.aigone.daycare.service.BookingService;
import com.aig.aigone.daycare.service.DialysisService;
import com.aig.aigone.daycare.service.HisServiceDaycare;
import com.aig.aigone.daycare.util.DaycareConstants;

@Service
public class DialysisServiceImpl implements DialysisService {
	private static final Logger logger = LoggerFactory.getLogger(DialysisServiceImpl.class);

	@Autowired
	FrequencyRepository frequencyRepo;
	@Autowired
	PaymentMethodRepository paymentRepo;
	@Autowired
	SlotTimingRepository slotTimeRepo;
	@Autowired
	SlotRepository slotRepo;
	@Autowired
	BookingRepository bookingRepo;
	@Autowired
	BedRepository bedRepo;
	@Autowired
	BookingService bookingService;
	@Autowired
	HisServiceDaycare hisService;
	@Autowired
	DialysisBookingRepository dialysisBookingRepo;
	@Autowired
	DialysisArrivalStatusRepository dialysisArrivalRepo;
	@Autowired
	private WhatsAppService whatsAppService;

	@Override
	public DialysisBooking getDialysisBooking(Integer bookingId) {
		logger.info("Fetching dialysis booking for booking ID: {}", bookingId);

		List<DialysisBooking> dialysisBookingList = dialysisBookingRepo.findByBookingId(bookingId);
		if (dialysisBookingList.size() == 0) {
			logger.error("No dialysis booking found for booking ID: {}", bookingId);
			throw new InvalidBookingId("Booking not found with id " + bookingId);
		}
		DialysisBooking temp = dialysisBookingList.get(dialysisBookingList.size()-1);
		logger.info("Dialysis booking found: {}", temp);
		return temp;
	}

	@Override
	public Slot upcomingSlot(String uhid) {
		logger.info("Fetching upcoming slot for UHID: {}", uhid);

		List<Booking> bookingList = dialysisBookingRepo.findByBookingIdAndEndDateBeforeOrEqual(uhid, LocalDate.now());
		if (bookingList.size() == 0) {
			return new Slot();
		}
		Booking booking = bookingList.get(0);
		Slot upcomingSlot = slotRepo.upcomingSlot(booking.getId(), LocalDate.now().atStartOfDay()).get(0);
		logger.info("Upcoming slot found: {}", upcomingSlot);
		return upcomingSlot;

	}

	@Override
	public List<DialysisPatientDTO> getSlotByDate(LocalDate date) {
		logger.info("Fetching slots by date: {}", date);

		// getting all slot of corresponding date
		List<Slot> slots = slotRepo.findByDate(date);
		Collections.sort(slots, (s1, s2) -> s2.getBookingId().getId() - s1.getBookingId().getId());

		Set<String> uniqueUhidSet = new HashSet();
		return slots.stream().filter(slot -> {
			String uhid = slot.getBookingId().getUhid();

			if (uniqueUhidSet.contains(uhid)) {
				return false;
			} else {
				uniqueUhidSet.add(uhid);
				return true;
			}
		}).map(slot -> {
			DialysisPatientDTO bookingDetails = new DialysisPatientDTO();

			String iacode = slot.getBookingId().getUhid().split("\\.")[0];
			Integer regNo = Integer.parseInt(slot.getBookingId().getUhid().split("\\.")[1]);

			bookingDetails.setSlot(slot);
			bookingDetails.setBookingId(slot.getBookingId().getId());
			bookingDetails.setPatient(hisService.getPatientByUHID(iacode, regNo));

			bookingDetails.setInfected(getDialysisBooking(slot.getBookingId().getId()).isInfected());
			bookingDetails.setDialysisType(slot.getBed().getCategory());

			// dialysis data
			DialysisArrivalStatus dialysis = dialysisArrivalRepo.findBySlot(slot);
			if (dialysis != null) {
				bookingDetails.setDialysisStatus(dialysis.getStatus());
			}
			return bookingDetails;
		}).collect(Collectors.toList());
	}

//	@Override
//	public List<DialysisPatientDTO> getAllBooking() {
//	    logger.info("Fetching all bookings");
//
//	    List<DialysisPatientDTO> bookingDetailsList = new ArrayList<>();
//	    try {
//	    	LocalDate today = LocalDate.now();
//		    LocalDateTime todayStart = today.atStartOfDay();
//		    List<Booking> bookings = slotRepo.findAllActiveSlotBookingId(todayStart);
//
//		    for (Booking booking : bookings) {
//		        DialysisPatientDTO bookingDetails = new DialysisPatientDTO();
//		        String uhid = booking.getUhid();
//		        String iacode = uhid.split("\\.")[0];
//		        Integer regNo = Integer.parseInt(uhid.split("\\.")[1]);
//
//		        // Setting Booking ID and patient information
//		        bookingDetails.setBookingId(booking.getId());
//		        bookingDetails.setPatient(hisService.getPatientByUHID(iacode, regNo));
//
//		        // Fetching the upcoming slot
//		        Slot upcomingSlot = upcomingSlot(iacode + "." + regNo);
//		        bookingDetails.setSlot(upcomingSlot);
//
//		        // Fetching infection status
//		        bookingDetails.setInfected(getDialysisBooking(booking.getId()).isInfected());
//
//		        // Fetching dialysis data
//		        DialysisArrivalStatus dialysis = dialysisArrivalRepo.findBySlot(upcomingSlot);
//		        bookingDetails.setDialysisType(upcomingSlot.getBed().getCategory());
//
//		        // Setting dialysis status if available
//		        if (dialysis != null) {
//		            bookingDetails.setDialysisStatus(dialysis.getStatus());
//		        }
//
//		        bookingDetailsList.add(bookingDetails);
//		    }
//
//		    // Sorting the list by upcoming slot's start time
//		    Collections.sort(bookingDetailsList, new Comparator<DialysisPatientDTO>() {
//		        @Override
//		        public int compare(DialysisPatientDTO dto1, DialysisPatientDTO dto2) {
//		            return dto1.getSlot().getStartingTime().compareTo(dto2.getSlot().getStartingTime());
//		        }
//		    });
//
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//	    return bookingDetailsList;
//	}

	@Override
	public List<DialysisPatientDTO> getAllBooking() {
	    logger.info("Fetching all bookings");
	    LocalDate today = LocalDate.now();

	    // Fetch all active slots for today and future
	    List<Slot> totalSlots = slotRepo.findAllActiveSlotsToday(today);

	    // Group slots by BookingId
	    Map<Integer, List<Slot>> slotsGroupedByBookingId = totalSlots.stream()
	            .collect(Collectors.groupingBy(slot -> slot.getBookingId().getId()));

	    // Create a list of DialysisPatientDTO with the most relevant slot per booking
	    List<DialysisPatientDTO> bookingDetailsList = slotsGroupedByBookingId.entrySet().stream()
	            .map(entry -> {
	                Integer bookingId = entry.getKey();
	                List<Slot> slots = entry.getValue();

	                // Group slots by date for this booking
	                Map<LocalDate, Slot> slotPerDay = slots.stream()
	                        .collect(Collectors.toMap(
	                                slot -> slot.getStartingTime().toLocalDate(), // Key: date
	                                slot -> slot, // Value: slot
	                                (s1, s2) -> s1 // In case of duplicate dates, retain the first one
	                        ));

	                // Prioritize today's slot, otherwise find the next available slot
	                Slot recentSlot = slotPerDay.get(today); // Check explicitly for today's slot
	                if (recentSlot == null) {
	                    recentSlot = slotPerDay.entrySet().stream()
	                            .filter(entrySlot -> entrySlot.getKey().isAfter(today)) // Future slots only
	                            .sorted(Map.Entry.comparingByKey()) // Sort by date
	                            .map(Map.Entry::getValue) // Extract the slot
	                            .findFirst()
	                            .orElse(null);
	                }

	                if (recentSlot == null) {
	                    return null; // Skip if no relevant slot is found
	                }

	                // Populate DialysisPatientDTO
	                DialysisPatientDTO bookingDetails = new DialysisPatientDTO();
	                Booking booking = recentSlot.getBookingId(); // Get the booking associated with the slot

	                // Parse UHID
	                String[] uhidParts = booking.getUhid().split("\\.");
	                String iacode = uhidParts[0];
	                Integer regNo = Integer.parseInt(uhidParts[1]);

	                // Set details
	                bookingDetails.setBookingId(bookingId);
	                bookingDetails.setSlot(recentSlot); // Assign the most relevant slot
	                bookingDetails.setPatient(hisService.getPatientByUHID(iacode, regNo));
	                bookingDetails.setInfected(getDialysisBooking(bookingId).isInfected());
	                bookingDetails.setDialysisType(recentSlot.getBed().getCategory());

	                // Add dialysis status for the recent slot
	                DialysisArrivalStatus dialysis = dialysisArrivalRepo.findBySlot(recentSlot);
	                if (dialysis != null) {
	                    bookingDetails.setDialysisStatus(dialysis.getStatus());
	                }

	                return bookingDetails;
	            })
	            .filter(Objects::nonNull) // Remove null entries
	            .sorted(Comparator.comparing(dto -> dto.getSlot().getStartingTime())) // Sort by slot's starting time
	            .collect(Collectors.toList());

	    return bookingDetailsList;
	}

	@Override
	public List<Slot> getPatientBookedSlot(Integer bookingId) {
		logger.info("Fetching booked slots for booking ID: {}", bookingId);

		Optional<Booking> booking = bookingRepo.findById(bookingId);
		if (booking.isPresent()) {
			List<Slot> slotList = slotRepo.findByBooking(booking.get());
//			List<DialysisSlotDTO> list = new ArrayList<DialysisSlotDTO>();
//			slotList.forEach(slot -> {
//				DialysisSlotDTO d = new DialysisSlotDTO();
//				d.setId(slot.getId());
//				d.setStartingTime(slot.getStartingTime());
//				d.setEndTime(slot.getEndTime());
//				d.setArrivalTime(slot.getArrivalTime());
//				d.setDialysisArrival(dialysisArrivalRepo.findBySlot(slot));
//				list.add(d);
//
//			});
			return slotList;
		} else {
			logger.error("No booking found for booking ID: {}", bookingId);
			throw new InvalidBookingId("Booking not found with id " + bookingId);
		}
	}

	@Override
	public String markArrival(Long slotid) {
		logger.info("Marking arrival for slot ID: {}", slotid);

		Optional<Slot> optionalSlot = slotRepo.findById(slotid);
		if (optionalSlot.isPresent()) {
			Slot existingSlot = optionalSlot.get();
			if (existingSlot.getArrivalTime() != null) {
				throw new RuntimeException("Already marked Arrived " + slotid);
			}
			existingSlot.getBookingId().getId();
			existingSlot.setArrivalTime(LocalDateTime.now());
			slotRepo.save(existingSlot);
			return "Marked Arrived";
		} else {
			logger.error("No slot found for slot ID: {}", slotid);
			throw new RuntimeException("Slot not found with id " + slotid);
		}
	}

	@Override
	public List<AvailabilityDTO> getMonthAvailability(LocalDate startDate, LocalDate endDate, Integer category) {
		logger.info("Fetching month availability from {} to {} for category {}", startDate, endDate, category);

		int stationId = 230;
		int bedTypeId = 36;

		List<DailysisSlotTiming> slots = slotTimeRepo.findAll();
		List<AvailabilityDTO> list = new ArrayList<AvailabilityDTO>();

		LocalDate tempDate = startDate;
		while (!tempDate.isAfter(endDate)) { // looping for each day of month

			AvailabilityDTO tempAvailability = new AvailabilityDTO();
			tempAvailability.setData(tempDate);

			List<TimeDTO> timeList = new ArrayList<TimeDTO>();

			for (DailysisSlotTiming timing : slots) {
				TimeDTO tempTime = new TimeDTO();
				tempTime.setStartTime(timing.getStartTime());
				tempTime.setEndTime(timing.getEndTime());
				tempTime.setAvailability(false);
				tempTime.setId(timing.getId());
				timeList.add(tempTime);
			}
			tempAvailability.setSlot(timeList);
			list.add(tempAvailability);
			tempDate = tempDate.plusDays(1);
		}
		return bookingService.getAvailibility(list, stationId, bedTypeId, category);
	}

	@Override
	public boolean bookBed(BookingDTO bookingDetails) {
		logger.info("Booking bed for UHID: {}", bookingDetails.getUhid());

		bookingService.bookBed(bookingDetails);

		String uhid = bookingDetails.getUhid();
		String iacode = uhid.split("\\.")[0];
		Integer regNo = Integer.parseInt(uhid.split("\\.")[1]);

		PatientDTO patient = hisService.getPatientByUHID(iacode, regNo);
		String patientPhoneNumber = patient.getPhoneNo();
		String patientName = patient.getPatientName();

		String bookingDate = bookingDetails.getSlotList().get(0).getStartTime().toLocalDate()
				.format(DateTimeFormatter.ofPattern("dd-MM-yyyy"));

		String bookingTime = bookingDetails.getSlotList().get(0).getStartTime()
				.format(DateTimeFormatter.ofPattern("hh:mm a"));

		logger.info("Calling Whatsapp API - Send appointment notification to patient....");

//		// Live - whatsapp notification for appointment confirmation
//		// whatsAppService.sendDialysisNotification(DayCareConstants.APPOINTMENT_CONFIRMATION,patientPhoneNumber,patientName,bookingDate,
//		// bookingTime);
//		
//		// Test- whatsapp notification
//		// Cheryshma - **********
//		whatsAppService.sendDialysisNotification(DaycareConstants.DIALYSIS_APPOINTMENT_CONFIRMATION, "**********",
//				"John", DaycareConstants.APPOINTMENT_TYPE, bookingDate, bookingTime);
		 try {
		        // Live - whatsapp notification for appointment confirmation
		        // whatsAppService.sendDialysisNotification(DayCareConstants.APPOINTMENT_CONFIRMATION, patientPhoneNumber, patientName, bookingDate, bookingTime);

		        // Test- whatsapp notification
		        // Cheryshma - **********
		        whatsAppService.sendDialysisNotification(DaycareConstants.DIALYSIS_APPOINTMENT_CONFIRMATION, patientPhoneNumber,
		                patientName,bookingDate, bookingTime);

		        logger.info("**** Whatsapp notification sent successfully to patient. ****");
		    } catch (Exception e) {
		        logger.error("**** Failed to send Whatsapp notification: {}", e.getMessage());
		    }
		return true;
	}

	@Override
	public List<PaymentMethod> getPaymentCategory() {
		logger.info("Fetching all payment methods");
		return paymentRepo.findAll();
	}

	@Override
	public List<DailysisFrequency> getFrequency() {
		logger.info("Fetching all dialysis frequency");
		return frequencyRepo.findAll();
	}

	@Override
	public List<DailysisSlotTiming> getSlot() {
		logger.info("Fetching all dialysis slots");
		List<DailysisSlotTiming> slots = slotTimeRepo.findAll();
		
		return slots;
	}
	
	public List<ExistingBookingDetailsDTO> getExistingBookingDetails(String uhid) {
	    logger.info("Fetching existing booking details for UHID: {}", uhid);

	    // Initialize an empty list to hold the DTOs
	    List<ExistingBookingDetailsDTO> existingBookingDetailsList = new ArrayList<>();

	    try {
	        // Fetch existing booking IDs for the given UHID
	        List<Integer> existingBookingIds = bookingService.getBookingIdsByUhid(uhid);

	        // If no bookings are found, return an empty list with a log
	        if (existingBookingIds == null || existingBookingIds.isEmpty()) {
	            logger.info("No existing bookings found for UHID: {}", uhid);
	            return Collections.emptyList();
	        }

	        // Fetch dialysis booking details for all existing booking IDs
	        List<Booking> dialysisBookings = bookingRepo.findByBookingIds(existingBookingIds);

	        if (dialysisBookings == null || dialysisBookings.isEmpty()) {
	            logger.warn("No dialysis bookings found for booking IDs: {}", existingBookingIds);
	            return Collections.emptyList();
	        }

	        // Loop through each booking ID to get the patient booked slots
	        for (Integer bookingId : existingBookingIds) {
	            Booking booking = dialysisBookings.stream()
	                    .filter(b -> b.getId().equals(bookingId))
	                    .findFirst()
	                    .orElse(null);

	            if (booking == null) {
	                logger.warn("No booking found for booking ID: {}", bookingId);
	                continue; // Skip to the next iteration if booking is null
	            }

	            // Get the patient booked slots for this booking ID
	            List<Slot> patientBookedSlots = getPatientBookedSlot(bookingId);

	            if (patientBookedSlots == null || patientBookedSlots.isEmpty()) {
	                logger.warn("No patient booked slots found for booking ID: {}", bookingId);
	            }

	            // Check booking status by UHID
	            boolean exists = bookingService.existsByUhid(uhid);
	            BookingStatusResponseDTO response = new BookingStatusResponseDTO();
	            response.setUhid(uhid);
	            response.setBookingStatus(exists);

	            // Fetch dialysis booking details by booking ID
	            List<DialysisBooking> dialysisBookingList = dialysisBookingRepo.findByBookingId(bookingId);
	            
	            

	            if (dialysisBookingList == null || dialysisBookingList.isEmpty()) {
	                logger.warn("No dialysis booking details found for booking ID: {}", bookingId);
	            }

	            // Ensure all necessary data is available before creating the DTO
	            if (patientBookedSlots != null && response != null && dialysisBookingList != null) {
	                ExistingBookingDetailsDTO bookingDetails = new ExistingBookingDetailsDTO(booking, patientBookedSlots, response, dialysisBookingList);
	                existingBookingDetailsList.add(bookingDetails);
	                logger.info("Added booking details for booking ID: {}", bookingId);
	            } else {
	                logger.warn("Skipping booking ID {} due to missing data (slots, response, or dialysis booking list)", bookingId);
	            }
	        }

	    } catch (Exception e) {
	        logger.error("Error occurred while fetching existing booking details for UHID: {}", uhid, e);
	        throw new ExistingBookingDetailsException("Failed to fetch existing booking details", e); // Custom exception
	    }

	    logger.info("Successfully fetched {} booking details for UHID: {}", existingBookingDetailsList.size(), uhid);
	    return existingBookingDetailsList;
	}

	@Override
	public boolean rescheduleBooking(RescheduleBookingDTO bookingDetails) {
		
		logger.info("Reschedule booking done: {}", bookingDetails.getUhid());
		 
		List<Slot> previousSlotsList = slotRepo.findAllActiveSlotsForBookingId(bookingDetails.getBookingId());	
		DialysisBooking recentBooking =dialysisBookingRepo.getRecentDialysisBooking(bookingDetails.getBookingId());
		
		bookingService.rescheduleBooking(bookingDetails);
		
		String uhid = bookingDetails.getUhid();
		String iacode = uhid.split("\\.")[0];
		Integer regNo = Integer.parseInt(uhid.split("\\.")[1]);

		PatientDTO patient = hisService.getPatientByUHID(iacode, regNo);
		String patientPhoneNumber = patient.getPhoneNo();
		String patientName = patient.getPatientName();

		String previousStartDate = recentBooking.getStartDate()
				.format(DateTimeFormatter.ofPattern("dd-MM-yyyy"));
		
		String previousEndDate = recentBooking.getEndDate()
				.format(DateTimeFormatter.ofPattern("dd-MM-yyyy"));

		String previousSlotTime = previousSlotsList.get(0).getStartingTime()
				.format(DateTimeFormatter.ofPattern("hh:mm a"));
		
		String rescheduledStartDate = bookingDetails.getSlotList().get(0).getStartTime().toLocalDate()
				.format(DateTimeFormatter.ofPattern("dd-MM-yyyy"));
		
		String rescheduledEndDate = bookingDetails.getSlotList().get(bookingDetails.getSlotList().size()-1).getStartTime().toLocalDate()
				.format(DateTimeFormatter.ofPattern("dd-MM-yyyy"));
		
		String rescheduledSlotTime = bookingDetails.getSlotList().get(0).getStartTime().format(DateTimeFormatter.ofPattern("hh:mm a"));

		logger.info("Calling Whatsapp API - Send appointment notification to patient....");

		 try {
		        // Live - whatsapp notification for appointment confirmation
		        // whatsAppService.sendDialysisNotification(DayCareConstants.APPOINTMENT_CONFIRMATION, patientPhoneNumber, patientName, bookingDate, bookingTime);

		        // Test- whatsapp notification
		        // Cheryshma - **********
		        whatsAppService.sendDialysisNotification(DaycareConstants.WHOLE_CYCLE_RESCHEDULING, patientPhoneNumber,
		                patientName,previousStartDate,previousEndDate,previousSlotTime,rescheduledStartDate,rescheduledEndDate,rescheduledSlotTime);

		        logger.info("**** Whatsapp notification sent successfully to patient. ****");
		    } catch (Exception e) {
		        logger.error("**** Failed to send Whatsapp notification: {}", e.getMessage());
		    }
		
		return true;
	}
	
//	@Override
//	public boolean rescheduleBooking(BookingDTO bookingDetails) {
//	    // Log the rescheduling attempt for the specified UHID
//	    logger.info("Rescheduling booking for UHID: {}", bookingDetails.getUhid());
//
//	    // Fetch the existing bookings for the provided UHID
//	    List<Booking> existingBookings = bookingRepo.findByUhid(bookingDetails.getUhid());
//	    
//	    // Check if any existing bookings were found
//	    if (existingBookings.isEmpty()) {
//	        throw new BookingNotFoundException("No booking found for UHID: " + bookingDetails.getUhid());
//	    }
//
//	    // Validate the new booking slot availability
//	    List<AvailabilityDTO> availability = bookingService.getAvailibility(bookingDetails.getSlotList(), bookingDetails.getStationId(),
//	            bookingDetails.getBedTypeId(), bookingDetails.getCategory());
//
//	    boolean slotAvailable = availability.stream()
//	        .flatMap(a -> a.getSlot().stream())
//	        .anyMatch(TimeDTO::isAvailability);
//
//	    if (!slotAvailable) {
//	        throw new UnavailableSlotException("Requested slot is unavailable for rescheduling.");
//	    }
//
//	    // Proceed to update the booking with new details
//	    for (Booking existingBooking : existingBookings) {
//	        // Logic to check if the current booking state allows for rescheduling
//	        if (!canBeRescheduled(existingBooking)) {
//	            throw new InvalidBookingStateException("Booking cannot be rescheduled.");
//	        }
//
//	        // Update the booking details
//	        existingBooking.setStationId(bookingDetails.getStationId());
//	        existingBooking.setBedTypeId(bookingDetails.getBedTypeId());
//	        existingBooking.setFrequency(bookingDetails.getFrequency());
//	        existingBooking.setInfected(bookingDetails.isInfected());
//	        existingBooking.setSlotList(bookingDetails.getSlotList()); // Assuming a setter for slotList exists
//	        existingBooking.setRescheduleReason(bookingDetails.getRescheduleReason()); // Assuming a setter for rescheduleReason exists
//	        // Add any other fields that need to be updated
//
//	        // Save the updated booking back to the database
//	        bookingRepository.save(existingBooking);
//	    }
//
//	    // Send notification after rescheduling
//	    sendReschedulingNotification(bookingDetails);
//
//	    return true;
//	}
//
//	// Method to check if a booking can be rescheduled based on its current state
//	private boolean canBeRescheduled(Booking booking) {
//	    // Check the booking status and determine if it can be rescheduled
//	    String status = booking.getStatus(); // Assuming a getStatus() method exists in Booking
//
//	    // Check if the booking has been completed
//	    if ("COMPLETED".equalsIgnoreCase(status)) {
//	        logger.warn("Booking cannot be rescheduled as it is already completed. Booking ID: {}", booking.getId());
//	        return false;
//	    }
//
//	    // Check if the booking has been cancelled
//	    if ("CANCELLED".equalsIgnoreCase(status)) {
//	        logger.warn("Booking cannot be rescheduled as it has been cancelled. Booking ID: {}", booking.getId());
//	        return false;
//	    }
//
//	    // Check if the booking is already in progress
//	    if ("IN_PROGRESS".equalsIgnoreCase(status)) {
//	        logger.warn("Booking cannot be rescheduled as it is currently in progress. Booking ID: {}", booking.getId());
//	        return false;
//	    }
//
//	    // Check if the booking is within the allowed rescheduling time frame
//	    LocalDateTime bookingDateTime = booking.getDateTime(); // Assuming a getDateTime() method exists
//	    LocalDateTime now = LocalDateTime.now();
//
//	    // Assuming rescheduling is allowed up to 24 hours before the appointment
//	    if (bookingDateTime.isBefore(now.plusHours(24))) {
//	        logger.warn("Booking cannot be rescheduled as it is within 24 hours of the appointment. Booking ID: {}", booking.getId());
//	        return false;
//	    }
//
//	    // Check for maximum rescheduling limits
//	    int maxRescheduleCount = 3; // Example limit; can be adjusted as needed
//	    if (booking.getRescheduleCount() >= maxRescheduleCount) { // Assuming a getRescheduleCount() method exists
//	        logger.warn("Booking cannot be rescheduled as it has reached the maximum rescheduling limit. Booking ID: {}", booking.getId());
//	        return false;
//	    }
//
//	    // Check for dependencies
//	    List<Booking> dependentBookings = booking.getDependentBookings(); // Assuming a getDependentBookings() method exists
//	    for (Booking dependent : dependentBookings) {
//	        if (!"COMPLETED".equalsIgnoreCase(dependent.getStatus())) {
//	            logger.warn("Booking cannot be rescheduled as there are dependent bookings that are not completed. Dependent Booking ID: {}", dependent.getId());
//	            return false;
//	        }
//	    }
//
//	    // If all checks pass, the booking can be rescheduled
//	    logger.info("Booking can be rescheduled. Booking ID: {}", booking.getId());
//	    return true;
//	}
//
//	// Method to send a notification after rescheduling
//	private void sendReschedulingNotification(BookingDTO bookingDetails) {
//	    // Extract necessary details from bookingDetails for the notification
//	    String patientPhoneNumber = bookingDetails.getPatientPhoneNumber(); // Assuming this method exists
//	    String patientName = bookingDetails.getPatientName(); // Assuming this method exists
//	    LocalDateTime bookingDateTime = bookingDetails.getNewBookingDateTime(); // Assuming this method exists
//	    String bookingDate = bookingDateTime.toLocalDate().toString(); // Format as needed
//	    String bookingTime = bookingDateTime.toLocalTime().toString(); // Format as needed
//
//	    try {
//	        // Live - WhatsApp notification for appointment confirmation
//	        // whatsAppService.sendDialysisNotification(DaycareConstants.APPOINTMENT_CONFIRMATION, patientPhoneNumber, patientName, bookingDate, bookingTime);
//
//	        // Test - WhatsApp notification (for demonstration purposes)
//	        // Example: Cheryshma - **********
//	        whatsAppService.sendDialysisNotification(
//	            DaycareConstants.DIALYSIS_APPOINTMENT_CONFIRMATION,
//	            "**********", // Replace with patientPhoneNumber for production
//	            patientName,
//	            DaycareConstants.APPOINTMENT_TYPE,
//	            bookingDate,
//	            bookingTime
//	        );
//
//	        logger.info("**** WhatsApp notification sent successfully to patient. ****");
//	    } catch (Exception e) {
//	        logger.error("**** Failed to send WhatsApp notification: {}", e.getMessage());
//	    }
//	}
//
	
	
	
//	public List<DialysisBooking> getdialysisBookingDetailsByUHID(String UHID){
//		logger.info("getDialysisBookingDetailsByUHID {}", UHID);
//		List<DialysisBooking> results = new ArrayList<DialysisBooking>();
//		try {
//			List<Integer> bookingIds = bookingRepo.findBookingIdsByUhid(UHID);
//			if(!bookingIds.isEmpty()) {
//				results = dialysisBookingRepo.getDialysisBookingDetailsByUHID(bookingIds);
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return results;
//	}
	
	public List<DialysisBooking> getDialysisBookingDetailsByUHID(String UHID) {
	    logger.info("getDialysisBookingDetailsByUHID {}", UHID);
	    List<DialysisBooking> results = new ArrayList<>();
	    try {
	        List<Integer> bookingIds = bookingRepo.findBookingIdsByUhid(UHID);
	        if (!bookingIds.isEmpty()) {
	            
	            List<DialysisBooking> allBookings = dialysisBookingRepo.getDialysisBookingDetailsByUHID(bookingIds);

	            Map<Integer, DialysisBooking> filteredBookings = allBookings.stream()
	                .collect(Collectors.groupingBy(book -> book.getBooking().getId())) 
	                .entrySet().stream()
	                .collect(Collectors.toMap(
	                    Map.Entry::getKey, 
	                    entry -> entry.getValue().stream() 
	                        .sorted((b1, b2) -> b2.getId().compareTo(b1.getId())) 
	                        .filter(booking -> "RESCHEDULED".equals(booking.getStatus()) || "NEW".equals(booking.getStatus())) 
	                        .findFirst() 
	                        .orElse(entry.getValue().get(0)) 
	                ));

	            results = new ArrayList<>(filteredBookings.values());
	        }
	    } catch (Exception e) {
	        logger.error("Error fetching dialysis booking details: {}", e.getMessage(), e);
	    }
	    return results;
	}


	public boolean waitingListAppointment(@RequestBody WaitingListDTO bookingDetails) {
		
		bookingService.waitingListAppointment(bookingDetails);
		return true;
	}

	public List<WaitingListAppointmentsDTO> getWaitingListData(){
		
		List<WaitingListAppointmentsDTO> result = new ArrayList<WaitingListAppointmentsDTO>();
		List<DialysisBooking> dailysisBookings = dialysisBookingRepo.findByWaitingList();
		
		if(!dailysisBookings.isEmpty()) {
			dailysisBookings.forEach(dailysisBooking ->{
				WaitingListAppointmentsDTO waitingListData = new WaitingListAppointmentsDTO();
				waitingListData.setBookingId(dailysisBooking.getBooking().getId());
				waitingListData.setInfected(dailysisBooking.isInfected());
				waitingListData.setStartDate(dailysisBooking.getStartDate());
				waitingListData.setEndDate(dailysisBooking.getEndDate());
				String uhid = dailysisBooking.getBooking().getUhid();
		        String iacode = uhid.split("\\.")[0];
		        Integer regNo = Integer.parseInt(uhid.split("\\.")[1]);

		        waitingListData.setPatient(hisService.getPatientByUHID(iacode, regNo));
		        result.add(waitingListData);
			});
		}
		return result;
	}
	
	public List<CancelledAppointmentsListDTO> getCancelledAppointments(){
		
		List<CancelledAppointmentsListDTO> cancelledList = new ArrayList<CancelledAppointmentsListDTO>();
		
		try {
			List<Booking> bookingList = bookingRepo.findCancelledBooking();
			
			for(Booking booking : bookingList){
				CancelledAppointmentsListDTO cancelled = new CancelledAppointmentsListDTO();
				cancelled.setBooking(booking);
				Integer bookingId = booking.getId();
				DialysisBooking dialysisBooking = dialysisBookingRepo.getByBookingId(bookingId);
				cancelled.setDialysisBooking(dialysisBooking);
				List<Slot> slotList = slotRepo.findCancelledByBooking(booking);
				cancelled.setSlotsList(slotList);
				
				String iacode = booking.getUhid().split("\\.")[0];
				Integer regNo = Integer.parseInt(booking.getUhid().split("\\.")[1]);
				cancelled.setPatientDetails(hisService.getPatientByUHID(iacode, regNo));
				cancelledList.add(cancelled);
			};
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		return cancelledList;
	}

	@Override
	public void sendRemainderNotification(RemainderDTO remainderDTO) {
		
		String uhid = remainderDTO.getUhid();
		String iacode = uhid.split("\\.")[0];
		Integer regNo = Integer.parseInt(uhid.split("\\.")[1]);

		PatientDTO patient = hisService.getPatientByUHID(iacode, regNo);
		String patientPhoneNumber = patient.getPhoneNo();
		String patientName = patient.getPatientName();

		String bookingDate = remainderDTO.getStartTime().toLocalDate()
				.format(DateTimeFormatter.ofPattern("dd-MM-yyyy"));

		String bookingTime = remainderDTO.getStartTime()
				.format(DateTimeFormatter.ofPattern("hh:mm a"));

		logger.info("Calling Whatsapp API - Send remainder notification to patient....");

//		// Live - whatsapp notification for appointment confirmation
//		// whatsAppService.sendDialysisNotification(DayCareConstants.APPOINTMENT_CONFIRMATION,patientPhoneNumber,patientName,bookingDate,
//		// bookingTime);
//		
//		// Test- whatsapp notification
//		// Cheryshma - **********
//		whatsAppService.sendDialysisNotification(DaycareConstants.DIALYSIS_APPOINTMENT_CONFIRMATION, "**********",
//				"John", DaycareConstants.APPOINTMENT_TYPE, bookingDate, bookingTime);
		 try {
		        // Live - whatsapp notification for appointment confirmation
		        // whatsAppService.sendDialysisNotification(DayCareConstants.APPOINTMENT_CONFIRMATION, patientPhoneNumber, patientName, bookingDate, bookingTime);

		        // Test- whatsapp notification
		        // Cheryshma - **********
		        whatsAppService.sendDialysisNotification(DaycareConstants.DIALYSIS_APPOINTMENT_REMINDER, patientPhoneNumber,
		                patientName,bookingDate, bookingTime);

		        logger.info("**** Whatsapp notification sent successfully to patient. ****");
		    } catch (Exception e) {
		        logger.error("**** Failed to send Whatsapp notification: {}", e.getMessage());
		    }
	}

	public List<DialysisPatientDTO> getAppointmentsByDate(LocalDate startDate, LocalDate endDate) {
		logger.info("Fetching slots by date: {}");

		// getting all slot of corresponding date
		List<Slot> slots = slotRepo.getAppointmentsByDate(startDate, endDate);

//		Collections.sort(slots, (s1, s2) -> s2.getBookingId().getId() - s1.getBookingId().getId());

		// Set<Integer> uniqueUhidSet = new HashSet();
		return slots.stream().map(slot -> {
			DialysisPatientDTO bookingDetails = new DialysisPatientDTO();

			String iacode = slot.getBookingId().getUhid().split("\\.")[0];
			Integer regNo = Integer.parseInt(slot.getBookingId().getUhid().split("\\.")[1]);

			bookingDetails.setSlot(slot);
			bookingDetails.setBookingId(slot.getBookingId().getId());
			bookingDetails.setPatient(hisService.getPatientByUHID(iacode, regNo));

			bookingDetails.setInfected(getDialysisBooking(slot.getBookingId().getId()).isInfected());
			bookingDetails.setDialysisType(slot.getBed().getCategory());

			// dialysis data
			DialysisArrivalStatus dialysis = dialysisArrivalRepo.findBySlot(slot);
			if (dialysis != null) {
				bookingDetails.setDialysisStatus(dialysis.getStatus());
			}
			return bookingDetails;
		}).collect(Collectors.toList());
	}
	
	public String fetchAlternativeMobileNumber(Integer bookingId) {
		String mobileNumber = "-";
		Booking booking = bookingRepo.findByBookingId(bookingId);
		if(booking != null) {
			if(booking.getAlternativeMobileNumber() != null) {
				mobileNumber = booking.getAlternativeMobileNumber();
			}
		}
		return mobileNumber;
	}
}
