
package com.aig.aigone.bloodbank.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class BloodbankWhatsAppMsg {

    @Autowired
    private WebClient airtelWhatsAppClient;

    @Value("${aig.Blood_bank.templateId}")
    private String templateId;

    @Value("${aig.Blood_bank.from}")
    private String from;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    // For APIs that support only one phone number per request

    public Mono<String> sendWhatsAppMessage(String to, List<String> variables) throws JsonProcessingException {
	    if (variables == null || variables.size() != 3) {
	        return Mono.error(new IllegalArgumentException("Template requires exactly 2 variables: [Blood Group, Required Date]"));
	    }
	    Map<String, Object> message = Map.of(
	        "variables", variables
	    );
 
	    Map<String, Object> requestBody = Map.of(
	        "templateId", templateId,
	        "to", to,
	        "from", from,
	        "message", message
	    );
 
	    String json = objectMapper.writeValueAsString(requestBody);
	    return airtelWhatsAppClient.post()
	        .uri("") // Fill with actual URI
	        .contentType(MediaType.APPLICATION_JSON)
	        .bodyValue(requestBody)
	        .retrieve()
	        .onStatus(status -> status.is4xxClientError() || status.is5xxServerError(),
	            clientResponse -> clientResponse.bodyToMono(String.class).flatMap(errorBody -> {
	                return Mono.error(new RuntimeException("WhatsApp API error " + clientResponse.statusCode() + ": " + errorBody));
	            }))
	        .bodyToMono(String.class)
	        .defaultIfEmpty("Empty response from WhatsApp API");
	}
}
