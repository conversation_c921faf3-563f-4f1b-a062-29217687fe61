package com.aig.aigone.daycare.model;

import java.time.LocalDateTime;
import com.aig.aigone.daycare.enums.DialysisStatus;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
@Entity
@Table(name="dc_dialysisarrivalstatus")
public class DialysisArrivalStatus {
	
	@jakarta.persistence.Id
	@GeneratedValue(strategy = GenerationType.AUTO)
	private Long Id;
	
	//@NotNull
	private LocalDateTime arrivalTime ;
	
	private LocalDateTime startTime ;
	
	private LocalDateTime endTime ;
	
	private String uhid;
	
	@Column(name="is_notification_sent")
	private Boolean isNotificationSent;
	
	@Enumerated(EnumType.STRING)
	//@NotNull
	private DialysisStatus status;
	
	
	//@NotNull
	@OneToOne
	Slot slot;

}
