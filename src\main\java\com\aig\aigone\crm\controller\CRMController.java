package com.aig.aigone.crm.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.crm.model.dto.DoctorConsultationFeeDetailsDto;
import com.aig.aigone.crm.model.dto.PatientOpBillsListRequestDto;
import com.aig.aigone.crm.service.CRMService;
import com.aig.aigone.model.dto.emr.EmrVisitDetailsDto;
import com.aig.aigone.model.dto.his.PateintPreviousAppointmentsDto;
import com.aig.aigone.ref.dto.ReferenceValueResponseDTO;

import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/crm/")
public class CRMController {

	@Autowired
	private CRMService crmService;


	@GetMapping("reference/values/{referenceCode}")
	public ResponseEntity<List<ReferenceValueResponseDTO>> getValuesByReference(@PathVariable String  referenceCode) {
		return ResponseEntity.ok(crmService.getValuesByReferenceCode(referenceCode));
	}

	@Operation(summary = "fetch Patient Previous Appointments")
	@GetMapping("all-previous/appointments/{uhId}")
	public List<PateintPreviousAppointmentsDto> fetchAllPreviousAppointmentsByUhId(
			@PathVariable(name = "uhId", required = true) String uhId) {
		return crmService.fetchAllPreviousAppointmentsByUhId(uhId);
	}

	@Operation(summary = "Get Patient Total OP Bills List", description = "Get Patient Total OP Bills List")	
	@PostMapping("getOPbillListFromUHID")
	public ResponseEntity<Map<String, Object>> getOPbillListFromUHID(@RequestBody PatientOpBillsListRequestDto requestDto) {
		Map<String, Object> result = crmService.getOPbillListFromUHID(requestDto);
		return ResponseEntity.ok(result);
	}

	@Operation(summary = "fetch Patient All Op visits")
	@GetMapping("all/op-visits/{uhId}")
	public EmrVisitDetailsDto fetchPatientAllVisits(@PathVariable(name = "uhId", required = true) String uhId) {
		return crmService.fetchPatientAllVisits(uhId);
	}
	
	@GetMapping("doctor/consultation-fee/{doctorId}")
	public ResponseEntity<List<DoctorConsultationFeeDetailsDto>> getConsultationFeeDetails(@PathVariable Integer  doctorId) {
		return ResponseEntity.ok(crmService.getConsultationFeeDetails(doctorId));
	}

}
