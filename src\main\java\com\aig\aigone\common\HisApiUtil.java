package com.aig.aigone.common;

public final class HisApiUtil {
	
	public static final String POST_OP_CONSULT_NOTE_EMR_FROM_PRISM="/api/integration/scannedDocuments";
	
	public static final String POST_OP_CONSULT_NOTE_HIS_FROM_PRISM="/api/FrontOffice/SaveFOPatientDocuments";
	
	public static final String POST_DRUG_DIAGNOSTIC="/api/Physician/SaveCPOEOrders";
	
	public static final String PATIENTS_BY_PHONE_NO = "/api/FrontOffice/GetPatientDetailsfromPhoneNo";
	
	public static final String PATIENT_BY_UHID = "/api/FrontOffice/GetPatientDetailsfromUHID";
	
	public static final String LOGIN = "/api/Auth/Login";
	
	public static final String PATIENT_REGISTRATION = "/api/FrontOffice/FOPatientRegistrationBasic";
	
	public static final String PATIENT_UPCOMING_ORDERS = "/api/FrontOffice/GetUnbilledOrdersFromUHID";
	
	public static final String CREATE_ORDER = "/api/FrontOffice/CreateBillFromUHID";
	
	public static final String GET_DOCTOR_SCHEDULE_FOR_SPECIFIC_DAYS = "/api/FrontOffice/GetDoctorScheduleForSpecificDays";
	
	public static final String GET_DOCTOR_APPOINTMENT_DETAILS_DAY_WISE = "/api/FrontOffice/GetDoctorAppointmentDetailsDaywise";
	
	public static final String SAVE_PATIENT_APPOINTMENT_WITH_DOCTOR = "/api/FrontOffice/SavePatientAppointmentwithDoctor";
	
	public static final String DELETE_PATIENT_APPOINTMENT_WITH_DOCTOR = "/api/FrontOffice/DeletePatientAppointmentwithDoctor";
	
	public static final String SAVE_PATIENT_DOCUMENTS = "/api/FrontOffice/SaveFOPatientDocuments";
	
	public static final String GET_ALL_PATIENT_REPORTS ="/api/FrontOffice/GetPatientsLabDiagnosticReportList";
	
	public static final String GET_BASE64_REPORT = "/api/FrontOffice/GetPatientsLabDiagnosticReport";
	
	public static final String GET_RISORDER_ID = "/YASASII-RIS_API/API/Reporting";
	
	public static final String GET_RISORDER_REPORT = "/YASASII-RIS_API/API/RisReporting";
	
	
	
	public static final String EMR_UPLOAD_DOC_DISCHARGE_SUM="/api/integration/results";
	public static final String EMR_UPLOAD_DOC = "/api/integration/scannedDocuments";
	
	public static final String EMR_VISIT_DETAILS = "/api/integration/getOPVisits";
	
	
	public static final String GET_ALL_PATIENT_PRESCRIPTIONS = "/api/FrontOffice/GetPatientPrescriptionList";
	public static final String GET_BASE64_PATIENT_PRESCRIPTIONS = "/api/FrontOffice/GetFOPatientPrescriptionReport";
	public static final String GET_BASE64_OP_BILLS_VIEW= "/api/FrontOffice/ViewOPbillListFromUHID";
	public static final String GET_SPECIALIZATION= "/api/FrontOffice/GetFOSpecialization";
	public static final String GET_FODoctors= "/api/FrontOffice/GetFODoctors";
	
	public static final String GET_PAYMENT_RESPONSE_DETAILS= "/api/FrontOffice/GetPaymentResponseDetails";
	
	public static final String SAVE_APPOINTMENT_REQUEST= "/api/Physician/SavePatientAdmissionSurgeryRequests";
	
	public static final String PATIENT_OP_BILLS_LIST = "/api/FrontOffice/GetOPbillListFromUHID";

	
	
	
	
	
	
	
	
	
	
	

}
