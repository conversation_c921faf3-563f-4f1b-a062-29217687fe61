package com.aig.aigone.controller;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.model.dto.PatientAmnesiaDetailsDTO;
import com.aig.aigone.service.PatientAmnesiaDetailsService;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import com.aig.aigone.service.HisService;

@RestController
@RequestMapping("/api")
public class AnimeaCSVExportController {

    private static final String EXPORT_ROOT_DIR = "C:\\Users\\<USER>\\Desktop\\CouponsBackup";
    private static final String CSV_FILE_PATH = "C:\\Users\\<USER>\\Desktop\\CouponsBackup\\patients.csv";
    // Local folder containing base64 .txt files for images
    private static final String LOCAL_IMAGE_BASE64_DIR = "C:\\Users\\<USER>\\Desktop\\CouponsBackup\\patients";
 // Update the column indices based on zero-based indexing
    private static final int UHID_INDEX = 19;
    private static final int LEFT_EYE_INDEX = 7;
    private static final int LEFT_HAND_INDEX = 8;
    private static final int NAILS_INDEX = 9;
    private static final int RIGHT_EYE_INDEX = 14;
    private static final int RIGHT_HAND_INDEX = 15;
    private static final int TONGUE_INDEX = 18;
    private static final int SIGNATURE_INDEX = 12;
    
    @Autowired
    private PatientAmnesiaDetailsService patientAmnesiaDetailsService;

    @Autowired
    private HisService hisService;

    @GetMapping("/export/animea/all-patient-images")
    public ResponseEntity<FileSystemResource> exportAllPatientImages() throws IOException {
        // Process CSV and create image folders
        Path processedDir = processCSVAndCreateImages();
        
        // Create ZIP file
        Path zipFilePath = zipPatientImages(processedDir);
        
        // Cleanup temporary files
        deleteDirectory(processedDir.toFile());

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + zipFilePath.getFileName())
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(new FileSystemResource(zipFilePath.toFile()));
    }

    @GetMapping("/export/animea/patient-images-db")
    public ResponseEntity<FileSystemResource> exportPatientImagesFromDb(
            @RequestParam("from") String from,
            @RequestParam("to") String to) throws Exception {

        SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy");
        Date fromDate = sdf.parse(from);
        Date toDate = sdf.parse(to);

        List<PatientAmnesiaDetailsDTO> patients = patientAmnesiaDetailsService.getCapturedDetailsByDateRange(fromDate, toDate);

        Path outputDir = Paths.get(EXPORT_ROOT_DIR, "processed_images_" + System.currentTimeMillis());
        Files.createDirectories(outputDir);

        for (PatientAmnesiaDetailsDTO dto : patients) {
            Path patientDir = outputDir.resolve(dto.getUhid());
            Files.createDirectories(patientDir);

            saveBase64Image(dto.getLeftEye(), patientDir, "leftEye.jpg");
            saveBase64Image(dto.getRightEye(), patientDir, "rightEye.jpg");
            saveBase64Image(dto.getTongue(), patientDir, "tongue.jpg");
            saveBase64Image(dto.getNails(), patientDir, "nails.jpg");
            saveBase64Image(dto.getLeftHand(), patientDir, "leftHand.jpg");
            saveBase64Image(dto.getRightHand(), patientDir, "rightHand.jpg");
            saveBase64Image(dto.getPatientSignature(), patientDir, "signature.jpg");

            // Fetch hemoglobin value from HisService using getPatientsWithCBPCBC
            List<com.aig.aigone.model.dto.PatientTestDTO> testDTOs = hisService.getPatientsWithCBPCBC(null, dto.getUhid(), null);
            String hemoglobin = "N/A";
            if (testDTOs != null && !testDTOs.isEmpty()) {
                hemoglobin = testDTOs.get(0).getHemoglobin();
            }
            // Prepare text file content
            String textContent = "hemoglobin: " + hemoglobin + "\n" +
                                 "age: " + dto.getAge() + "\n" +
                                 "gender: " + dto.getGender() + "\n";
            // Write text file
            Path textFile = patientDir.resolve("info.txt");
            Files.write(textFile, textContent.getBytes());
        }

        Path zipFilePath = zipPatientImages(outputDir);
        deleteDirectory(outputDir.toFile());

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + zipFilePath.getFileName())
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(new FileSystemResource(zipFilePath.toFile()));
    }

    /**
     * Export patient images from local base64 .txt files for all patients in a date range,
     * using patient data from a CSV file (patients_excel) instead of the database.
     * Each image field is a path to a .txt file containing base64 data. This method reads those files,
     * decodes the images, organizes them per patient, zips the result, and returns as a download.
     * Example endpoint: /api/export/animea/patient-images-local?from=2025-06-01&to=2025-06-10
     */
    @GetMapping("/export/animea/patient-images-local")
    public ResponseEntity<FileSystemResource> exportPatientImagesFromLocal(
            @RequestParam("from") String from,
            @RequestParam("to") String to) throws Exception {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date fromDate = sdf.parse(from);
        Date toDate = sdf.parse(to);

        // Path to the CSV file containing patient data (update as needed)
        String csvFilePath = EXPORT_ROOT_DIR + File.separator + "patients_excel";

        // Read and process the CSV file
        Path outputDir = Paths.get(EXPORT_ROOT_DIR, "local_processed_images_" + System.currentTimeMillis());
        Files.createDirectories(outputDir);

        try (BufferedReader br = new BufferedReader(new FileReader(csvFilePath))) {
            String header = br.readLine(); // Skip header
            String line;
            while ((line = br.readLine()) != null) {
                String[] columns = line.split(",(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)", -1);
                // Adjust indices as per your CSV structure
                // Example indices (update as per your CSV):
                // 0: id, 1: created_by, 2: creation_date, ... , uhid, left_eye, right_eye, ...
                int uhidIdx = 13; // Example: update to actual index
                int creationDateIdx = 2; // Example: update to actual index
                int leftEyeIdx = 7, rightEyeIdx = 12, tongueIdx = 17, nailsIdx = 8, leftHandIdx = 9, rightHandIdx = 14, signatureIdx = 11;

                if (columns.length <= Math.max(uhidIdx, Math.max(signatureIdx, creationDateIdx))) continue;

                String creationDateStr = columns[creationDateIdx].replaceAll("^\"|\"$", "");
                Date creationDate;
                try {
                    creationDate = javax.xml.bind.DatatypeConverter.parseDateTime(creationDateStr).getTime();
                } catch (Exception e) {
                    // Fallback: try yyyy-MM-dd
                    try { creationDate = sdf.parse(creationDateStr); } catch (Exception ex) { continue; }
                }
                if (creationDate.before(fromDate) || creationDate.after(toDate)) continue;

                String uhid = columns[uhidIdx].replaceAll("^\"|\"$", "");
                Path patientDir = outputDir.resolve(uhid);
                Files.createDirectories(patientDir);

                saveImageFromLocalBase64Path(columns[leftEyeIdx], patientDir, "leftEye.jpg");
                saveImageFromLocalBase64Path(columns[rightEyeIdx], patientDir, "rightEye.jpg");
                saveImageFromLocalBase64Path(columns[tongueIdx], patientDir, "tongue.jpg");
                saveImageFromLocalBase64Path(columns[nailsIdx], patientDir, "nails.jpg");
                saveImageFromLocalBase64Path(columns[leftHandIdx], patientDir, "leftHand.jpg");
                saveImageFromLocalBase64Path(columns[rightHandIdx], patientDir, "rightHand.jpg");
                saveImageFromLocalBase64Path(columns[signatureIdx], patientDir, "signature.jpg");
            }
        }

        Path zipFilePath = zipPatientImages(outputDir);
        deleteDirectory(outputDir.toFile());

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + zipFilePath.getFileName())
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(new FileSystemResource(zipFilePath.toFile()));
    }

    private Path processCSVAndCreateImages() throws IOException {
        Path outputDir = Paths.get(EXPORT_ROOT_DIR, "processed_images_" + System.currentTimeMillis());
        Files.createDirectories(outputDir);

        try (BufferedReader br = new BufferedReader(new FileReader(CSV_FILE_PATH))) {
            String header = br.readLine(); // Skip header
            
            AtomicInteger counter = new AtomicInteger(0);
            br.lines().forEach(line -> {
                try {
                    String[] columns = line.split(",(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)", -1);
                    if (columns.length <= UHID_INDEX) return;

                    String rawUHID = columns[UHID_INDEX].trim().replaceAll("^\"|\"$", "");
                    String cleanUHID = rawUHID.replaceAll("(?i)^AIGG\\.?", "");
                    
                    // Create patient directory
                    Path patientDir = outputDir.resolve(cleanUHID);
                    Files.createDirectories(patientDir);

                    // Save images
                    saveBase64Image(columns[LEFT_EYE_INDEX], patientDir, "leftEye.jpg");
                    saveBase64Image(columns[RIGHT_EYE_INDEX], patientDir, "rightEye.jpg");
                    saveBase64Image(columns[TONGUE_INDEX], patientDir, "tongue.jpg");
                    saveBase64Image(columns[NAILS_INDEX], patientDir, "nails.jpg");
                    saveBase64Image(columns[LEFT_HAND_INDEX], patientDir, "leftHand.jpg");
                    saveBase64Image(columns[RIGHT_HAND_INDEX], patientDir, "rightHand.jpg");
                    saveBase64Image(columns[SIGNATURE_INDEX], patientDir, "signature.jpg");

                    counter.incrementAndGet();
                } catch (Exception e) {
                    System.err.println("Error processing line: " + line);
                    e.printStackTrace();
                }
            });
            System.out.println("Processed " + counter.get() + " patient records");
        }
        return outputDir;
    }

    private void saveBase64Image(String base64Data, Path directory, String filename) {
        if (base64Data == null || base64Data.isEmpty()) return;

        try {
            String cleanData = base64Data.replaceAll("^\"|\"$", "");
            if (cleanData.contains(",")) {
                cleanData = cleanData.split(",")[1];
            }

            byte[] imageBytes = Base64.getDecoder().decode(cleanData);
            Path imagePath = directory.resolve(filename);
            Files.write(imagePath, imageBytes);
        } catch (Exception e) {
            System.err.println("Failed to save image: " + filename);
            e.printStackTrace();
        }
    }

    private Path zipPatientImages(Path sourceDir) throws IOException {
        Path zipPath = Paths.get(EXPORT_ROOT_DIR, "PatientImages.zip");
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipPath.toFile()))) {
            Files.walk(sourceDir)
                .filter(Files::isRegularFile)
                .forEach(path -> {
                    try {
                        String zipEntryName = sourceDir.relativize(path).toString().replace("\\", "/");
                        zos.putNextEntry(new ZipEntry(zipEntryName));
                        Files.copy(path, zos);
                        zos.closeEntry();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                });
        }
        return zipPath;
    }

    private void deleteDirectory(File directory) {
        File[] files = directory.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    deleteDirectory(file);
                } else {
                    file.delete();
                }
            }
        }
        directory.delete();
    }

    // Helper: Extract UUID from a path like /home/<USER>/anemia_images_prod/uuid.txt
    private String extractUuidFromPath(String path) {
        if (path == null || path.isEmpty()) return null;
        String filename = Paths.get(path).getFileName().toString();
        return filename.replace(".txt", "");
    }

    // Helper: Read base64 from local .txt file and save as image
    private void saveImageFromLocalBase64Path(String imagePath, Path dir, String filename) {
        String uuid = extractUuidFromPath(imagePath);
        if (uuid == null) return;
        Path filePath = Paths.get(LOCAL_IMAGE_BASE64_DIR, uuid + ".txt");
        if (!Files.exists(filePath)) return;
        try {
            String base64 = Files.readString(filePath).replaceAll("^\"|\"$", "");
            if (base64.contains(",")) base64 = base64.split(",")[1];
            byte[] imageBytes = Base64.getDecoder().decode(base64);
            Files.write(dir.resolve(filename), imageBytes);
        } catch (Exception e) {
            System.err.println("Failed to save image: " + filename + " for uuid: " + uuid);
            e.printStackTrace();
        }
    }
}