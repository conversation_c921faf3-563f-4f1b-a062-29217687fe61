package com.aig.aigone.prism.forms.dto;

import java.util.List;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SectionDto {

	 private Long id;
	 private String sectionLabel;
     private Integer sortOrder;
     private String status;
     private String remarks;
     private String columnType;
     
      List<FieldDto> feilds;
 //    private Long master_section_id;
  //   private String sectionName;
    // private List<FieldDto> fields;
		
}
