package com.aig.aigone.fcm.serviceImpl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import com.aig.aigone.exceptions.dfcms.DfcmsException;
import com.aig.aigone.fcm.dto.FcmEmployeeCouponsDTO;
import com.aig.aigone.fcm.entity.CouponMaster;
import com.aig.aigone.fcm.entity.FcmEmployeeCoupons;
import com.aig.aigone.fcm.repo.FcmEmployeeCouponsRepository;
import com.aig.aigone.fcm.service.FcmEmployeeCouponsService;
import com.aig.aigone.model.dto.UserDto;
import com.aig.aigone.model.entity.aigone.SystemSettingsEntity;
import com.aig.aigone.model.entity.aigone.UserEntity;
import com.aig.aigone.repository.aigone.SystemSettingsRepository;
import com.aig.aigone.repository.aigone.UserRepository;
import com.aig.aigone.repository.dfcms.CouponRepository;
import com.aig.aigone.repository.dfcms.CouponUploadConfigRepository;
import com.aig.aigone.service.dfcms.UserRoleAssignService;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import jakarta.mail.util.ByteArrayDataSource;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class FcmEmployeeCouponsServiceImpl implements FcmEmployeeCouponsService {

//	private static final Logger LOGGER = LoggerFactory.getLogger(EmployeeCouponsServiceImpl.class);

	private final FcmEmployeeCouponsRepository employeeCouponsRepository;

	private final CouponRepository coupanRepository;

	private final UserRepository userRepository;

	private final UserRoleAssignService userRoleAssignService;

	@Autowired
	private JavaMailSender mailSender;

	@Autowired
	private SystemSettingsRepository systemRepo;

	@Value("${aig.sender.email}")
	private String senderEmail;

	@Autowired
	private CouponUploadConfigRepository couponUploadConfigRepository;

	// Constructor injection for both repositories
	public FcmEmployeeCouponsServiceImpl(FcmEmployeeCouponsRepository employeeCouponsRepository,
			CouponRepository couponRepository, UserRepository userRepository,
			UserRoleAssignService userRoleAssignService) {
		this.employeeCouponsRepository = employeeCouponsRepository;
		this.coupanRepository = couponRepository; // Correct assignment
		this.userRepository = userRepository;
		this.userRoleAssignService = userRoleAssignService;
	}

	@Override
	public List<FcmEmployeeCouponsDTO> fcmGetAllEmployeeCoupons() {
		log.info("Fetching all employee coupons.");
		try {
			return employeeCouponsRepository.findAll().stream().map(FcmEmployeeCouponsDTO::new)
					.collect(Collectors.toList());
		} catch (Exception e) {
			log.error("Error occurred while fetching employee coupons.", e);
			throw new DfcmsException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to fetch employee coupons.");
		}
	}


	@Override
	public List<FcmEmployeeCouponsDTO> fcmGetEmployeeCouponsByEmpId(String empId) {
	    log.info("Fetching active employee coupons for Employee ID: {}", empId);

	    LocalDateTime now = LocalDateTime.now(); // Current date and time

	    try {
	        // Fetch pay group from user entity
	        UserEntity userEntity = userRepository.findByEmployeeIdAndActiveTrue(empId)
	                .orElseThrow(() -> new DfcmsException(HttpStatus.NOT_FOUND, "User not found with empId: " + empId));
	        String payGroupName = userEntity.getPayGroupName();

	        // Fetch and filter coupons: valid at current moment and active status
	        return employeeCouponsRepository.findByEmpId(empId).stream()
	                .filter(coupon -> 
	                    !now.isBefore(coupon.getValidFrom()) && // Current time is after validFrom
	                    !now.isAfter(coupon.getValidTo()) &&    // Current time is before validTo
	                    coupon.getStatus()                      // Coupon is active
	                )
	                .map(coupon -> toDTO(coupon, payGroupName))
	                .collect(Collectors.toList());

	    } catch (Exception e) {
	        log.error("Error occurred while fetching coupons for Employee ID: {}", empId, e);
	        throw new DfcmsException(HttpStatus.INTERNAL_SERVER_ERROR,
	                "Failed to fetch employee coupons for Employee ID: " + empId);
	    }
	}

	@Override
	public List<FcmEmployeeCoupons> getEmployeeCouponsByEmpIdAllData(String empId) {
		log.info("Fetching all employee coupons for Employee ID: {}", empId);

		try {
			return employeeCouponsRepository.findByEmpId(empId);
		} catch (Exception e) {
			log.error("Error occurred while fetching coupons for Employee ID: {}", empId, e);
			throw new DfcmsException(HttpStatus.INTERNAL_SERVER_ERROR,
					"Failed to fetch employee coupons for Employee ID: " + empId);
		}
	}

	@Override
	public List<FcmEmployeeCoupons> getEmployeeCouponsByPhoneNumber(long phoneNumber) {
		log.info("Fetching employee coupons for mobileNumber: {}", phoneNumber);

		// Fetch the user entity using phone number
		Optional<UserEntity> userEntity = userRepository.findByPhoneNoAndActiveTrue(phoneNumber);

		// Check if the user entity is present
		if (userEntity.isEmpty()) {
			throw new DfcmsException(HttpStatus.NOT_FOUND, "User not found for phone number: " + phoneNumber);
		}

		// Fetch employee coupons for the user's ID
		List<FcmEmployeeCoupons> coupons = employeeCouponsRepository.findByEmpId(userEntity.get().getEmployeeId());

		if (coupons.isEmpty()) {
			throw new DfcmsException(HttpStatus.NOT_FOUND,
					"No EmployeeCoupons found for empId: " + userEntity.get().getId());
		}

		return coupons;
	}

	@Override
	public List<FcmEmployeeCoupons> fcmAddEmployeeCoupons(List<FcmEmployeeCouponsDTO> employeeCouponsDTOs) {
		log.info("Adding new employee coupons in bulk. Total count: {}", employeeCouponsDTOs.size());

		List<FcmEmployeeCoupons> assignedCoupons = new ArrayList<>();

		for (FcmEmployeeCouponsDTO dto : employeeCouponsDTOs) {
			try {
				// Validate if employee is active
				Optional<UserDto> userOptional = userRoleAssignService.findUserByEmployeeId(dto.getEmpId());
				if (userOptional.isEmpty() || !userOptional.get().isActive()) {
					throw new DfcmsException(HttpStatus.BAD_REQUEST, "Employee is not active: " + dto.getEmpId());
				}

				// Fetch CouponMaster by ID
				CouponMaster couponMaster = coupanRepository.findById(dto.getCouponMasterId())
						.orElseThrow(() -> new DfcmsException(HttpStatus.NOT_FOUND, "Invalid CouponMaster ID"));

				// Fetch user by empId and validate
				Optional<UserEntity> userEntity = userRepository.findByEmployeeIdAndActiveTrue(dto.getEmpId());
				if (userEntity.isEmpty()) {
					throw new DfcmsException(HttpStatus.NOT_FOUND, "User not found for employee ID: " + dto.getEmpId());
				}

				// Determine max allowed coupon quantity based on payGroupName
				String payGroupName = userEntity.get().getPayGroupName();

				int maxCoupons;

				if (payGroupName.toLowerCase().contains("consultant")) {
					maxCoupons = 1500;
				} else {
					maxCoupons = 26;
				}

				// Validate coupon quantity
				if (dto.getCouponsQty() > dto.getAllowedQty()) {
					throw new DfcmsException(HttpStatus.BAD_REQUEST,
							"Coupon quantity must not exceed allowed quantity for employee ID: " + dto.getEmpId());
				}

				if (dto.getCouponsQty() > maxCoupons) {
					throw new DfcmsException(HttpStatus.BAD_REQUEST,
							"Coupon quantity cannot exceed " + maxCoupons + " for employee ID: " + dto.getEmpId());
				}

//				// Validate date range is within the current month
//				LocalDateTime startOfCurrentMonth = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0)
//						.withSecond(0);
//				LocalDateTime endOfCurrentMonth = startOfCurrentMonth.plusMonths(1).minusSeconds(1);
//
//				if (dto.getValidFrom().isBefore(startOfCurrentMonth) || dto.getValidTo().isAfter(endOfCurrentMonth)) {
//					throw new DfcmsException(HttpStatus.BAD_REQUEST, "You can upload present month coupons only.");
//				}

		

				LocalDateTime now = LocalDateTime.now();
				LocalDateTime allowedValidFrom;
				LocalDateTime allowedValidTo;

				if (payGroupName.toLowerCase().contains("consultant")) {
				    if (now.getDayOfMonth() >= 26) {
				        // Consultant: 26th current month to 25th of next month
				        allowedValidFrom = now.withDayOfMonth(26).withHour(1).withMinute(0).withSecond(0).withNano(0);
				        allowedValidTo = allowedValidFrom.plusMonths(1)
				            .withDayOfMonth(25).withHour(22).withMinute(59).withSecond(59).withNano(999_000_000);
				    } else {
				        // Consultant: 26th previous month to 25th of current month
				        allowedValidFrom = now.minusMonths(1).withDayOfMonth(26).withHour(1).withMinute(0).withSecond(0).withNano(0);
				        allowedValidTo = now.withDayOfMonth(25).withHour(22).withMinute(59).withSecond(59).withNano(999_000_000);
				    }
				} else {
				    // Non-consultant: 1st to last day of current month
				    allowedValidFrom = now.withDayOfMonth(1).withHour(1).withMinute(0).withSecond(0).withNano(0);
				    allowedValidTo = now.with(TemporalAdjusters.lastDayOfMonth())
				        .withHour(22).withMinute(59).withSecond(59).withNano(999_000_000);
				}

				// Validate user input with detailed error message
				if (!dto.getValidFrom().isEqual(allowedValidFrom) || !dto.getValidTo().isEqual(allowedValidTo)) {
				    log.error("Invalid validity period for employee {}: Expected {} to {}, Got {} to {}", 
				        dto.getEmpId(), allowedValidFrom, allowedValidTo, dto.getValidFrom(), dto.getValidTo());
				    throw new DfcmsException(HttpStatus.BAD_REQUEST, 
				        String.format("Invalid validity period. Expected: %s to %s, Got: %s to %s", 
				            allowedValidFrom, allowedValidTo, dto.getValidFrom(), dto.getValidTo()));
				}

				// Check for existing coupons for same range
				boolean overlappingCouponExists = employeeCouponsRepository
						.existsByEmpIdAndValidFromLessThanEqualAndValidToGreaterThanEqual(dto.getEmpId(),
								dto.getValidTo(), dto.getValidFrom());

				if (overlappingCouponExists) {
					throw new DfcmsException(HttpStatus.CONFLICT,
							"Employee already has coupons for the given date range: " + dto.getEmpId());
				}

				// Create and populate new coupon entity
				FcmEmployeeCoupons newCoupon = new FcmEmployeeCoupons();
				newCoupon.setCouponsQty(dto.getCouponsQty());
				newCoupon.setAllowedQty(dto.getAllowedQty());
//				newCoupon.setPayGroupName(payGroupName);
				newCoupon.setExceededQty(0);
				newCoupon.setIsDesclaimed(false);
				newCoupon.setEmpId(dto.getEmpId());
				newCoupon.setCouponMaster(couponMaster);
				newCoupon.setValidFrom(dto.getValidFrom());
				newCoupon.setValidTo(dto.getValidTo());
				newCoupon.setOpeningQty(dto.getOpeningQty());
				newCoupon.setAvailedQty(dto.getAvailedQty());
				newCoupon.setBalanceQty(dto.getBalanceQty());
				newCoupon.setAdditionalQty(dto.getAdditionalQty());
				newCoupon.setStatus(dto.getStatus());
				newCoupon.setRemarks(dto.getRemarks());
                newCoupon.setAllowedQty(dto.getAllowedQty());
				employeeCouponsRepository.save(newCoupon);
				assignedCoupons.add(newCoupon);

			} catch (Exception e) {
				log.error("Error processing employee ID {}: {}", dto.getEmpId(), e.getMessage(), e);
			}
		}

		// Generate and send report if coupons were assigned
		if (!assignedCoupons.isEmpty()) {
			try {
				byte[] excelData = generateExcelReport(assignedCoupons);
				sendEmailWithAttachment(excelData);
				log.info("Completed bulk coupon assignment and sent email.");
			} catch (Exception e) {
				log.error("Failed to generate/send email report: {}", e.getMessage(), e);
			}
		} else {
			log.info("No coupons assigned. Skipping report generation and email.");
		}

		return assignedCoupons;
	}

	@Override
	public FcmEmployeeCoupons fcmUpdateEmployeeCoupon(Integer id, FcmEmployeeCouponsDTO employeeCouponsDTO) {
		log.info("Updating employee coupon with ID: {}", id);

		FcmEmployeeCoupons existingCoupon = employeeCouponsRepository.findById(id).orElseThrow(
				() -> new DfcmsException(HttpStatus.NOT_FOUND, "Employee coupon with ID " + id + " not found."));

		// Convert empId and fetch userEntity

		UserEntity userEntity = userRepository.findByEmployeeIdAndActiveTrue(employeeCouponsDTO.getEmpId())
				.orElseThrow(() -> new DfcmsException(HttpStatus.NOT_FOUND,
						"User not found with empId: " + employeeCouponsDTO.getEmpId()));

		String payGroupName = userEntity.getPayGroupName();
		if (payGroupName == null) {
			throw new DfcmsException(HttpStatus.BAD_REQUEST, "Pay group not found for user.");
		}

		String inferredUserType;
		int expectedQty;
		if (payGroupName.toLowerCase().contains("staff")) {
			inferredUserType = "STAFF";
			expectedQty = 26;
		} else if (payGroupName.toLowerCase().contains("consultant")) {
			inferredUserType = "CONSULTANT";
			expectedQty = 1500;
		} else {
			throw new DfcmsException(HttpStatus.BAD_REQUEST, "Invalid pay group: " + payGroupName);
		}

		// Validate coupon quantity
		if (!(employeeCouponsDTO.getCouponsQty() <= employeeCouponsDTO.getAllowedQty())) {
			throw new DfcmsException(HttpStatus.BAD_REQUEST,
					"Coupon quantity must match allowed quantity for userType based on payGroup: " + inferredUserType);
		}

//		if (employeeCouponsDTO.getCouponsQty() != expectedQty) {
//			throw new DfcmsException(HttpStatus.BAD_REQUEST,
//					"Coupon quantity must remain " + expectedQty + " for " + inferredUserType);
//		}

		// For STAFF validations
		if ("STAFF".equalsIgnoreCase(inferredUserType)) {
			int totalQty = employeeCouponsDTO.getAvailedQty() + employeeCouponsDTO.getBalanceQty();
			if (totalQty > 26) {
				throw new DfcmsException(HttpStatus.BAD_REQUEST, "Availed + Balance cannot exceed 26 for Staff.");
			}
		} else {
			// For CONSULTANT
			int allowed = 1500 + existingCoupon.getAdditionalQty();
			int availed = employeeCouponsDTO.getAvailedQty();
			int exceeded = Math.max(availed - allowed, 0);
			existingCoupon.setExceededQty(exceeded);
			existingCoupon.setIsDesclaimed(exceeded > 0);
		}

		// Capture previous values
		int previousBalanceQty = existingCoupon.getBalanceQty();
		int previousAdditionalQty = existingCoupon.getAdditionalQty();
		String previousModifiedBy = existingCoupon.getLastModifiedBy();
		Date previousModifiedDate = existingCoupon.getLastModifiedDate();

		// Update the fields
		CouponMaster couponMaster = coupanRepository.findById(employeeCouponsDTO.getCouponMasterId())
				.orElseThrow(() -> new DfcmsException(HttpStatus.NOT_FOUND, "Invalid CouponMaster ID"));

		existingCoupon.setEmpId(employeeCouponsDTO.getEmpId());
//		existingCoupon.setPayGroupName(payGroupName);
		existingCoupon.setCouponMaster(couponMaster);
		existingCoupon.setValidFrom(employeeCouponsDTO.getValidFrom());
		existingCoupon.setValidTo(employeeCouponsDTO.getValidTo());
		existingCoupon.setCouponsQty(employeeCouponsDTO.getCouponsQty());
		existingCoupon.setOpeningQty(employeeCouponsDTO.getOpeningQty());
		existingCoupon.setAvailedQty(employeeCouponsDTO.getAvailedQty());
		existingCoupon.setBalanceQty(employeeCouponsDTO.getBalanceQty());
		existingCoupon.setAdditionalQty(employeeCouponsDTO.getAdditionalQty());
		existingCoupon.setStatus(employeeCouponsDTO.getStatus());
		existingCoupon.setRemarks(employeeCouponsDTO.getRemarks());
		existingCoupon.setLastModifiedBy(employeeCouponsDTO.getLastModifiedBy());
		existingCoupon.setLastModifiedDate(new Date());

		FcmEmployeeCoupons updatedCoupon = employeeCouponsRepository.save(existingCoupon);

		try {
			List<FcmEmployeeCoupons> updatedCoupons = Collections.singletonList(updatedCoupon);
			byte[] excelData = generateExcelReport(updatedCoupons);

//			sendEmailWithAttachment(excelData, previousBalanceQty, previousAdditionalQty, previousModifiedBy,
//					previousModifiedDate, updatedCoupon);

			log.info("Email notification sent for updated employee coupon.");
		} catch (Exception e) {
			log.error("Failed to generate/send email report for update: {}", e.getMessage(), e);
		}

		return updatedCoupon;
	}

	@Override
	public FcmEmployeeCoupons fcmUpdateAdditionalCoupons(Integer id, Long additionalQty) {
		log.info("Updating additional coupons for employee with ID: {}", id);

		FcmEmployeeCoupons existingCoupon = employeeCouponsRepository.findById(id).orElseThrow(
				() -> new DfcmsException(HttpStatus.NOT_FOUND, "Employee coupon with ID " + id + " not found."));

		// Fetch userEntity using empId to determine payGroupName
		Optional<UserEntity> userEntityOptional = userRepository.findByEmployeeIdAndActiveTrue(existingCoupon.getEmpId());
		if (userEntityOptional.isEmpty()) {
			throw new DfcmsException(HttpStatus.NOT_FOUND,
					"User not found for employee ID: " + existingCoupon.getEmpId());
		}

		String payGroupName = userEntityOptional.get().getPayGroupName();
		if (payGroupName == null) {
			throw new DfcmsException(HttpStatus.BAD_REQUEST,
					"Missing pay group for employee ID: " + existingCoupon.getEmpId());
		}

		// Determine if the user is Consultant
		boolean isConsultant = payGroupName.toLowerCase().contains("consultant");

		// Capture previous values before updating
		int previousBalanceQty = existingCoupon.getBalanceQty();
		int previousAdditionalQty = existingCoupon.getAdditionalQty();
		String previousModifiedBy = existingCoupon.getLastModifiedBy();
		Date previousModifiedDate = existingCoupon.getLastModifiedDate();

		// Update additionalQty and balanceQty
		existingCoupon.setAdditionalQty(existingCoupon.getAdditionalQty() + additionalQty.intValue());
		existingCoupon.setBalanceQty(existingCoupon.getBalanceQty() + additionalQty.intValue());
		existingCoupon.setLastModifiedBy("System"); // Modify as per context
		existingCoupon.setLastModifiedDate(new Date());

		// Recalculate exceededQty for Consultants
		if (isConsultant) {
			int allowed = 1500 + existingCoupon.getAdditionalQty();
			int availed = existingCoupon.getAvailedQty();
			int exceeded = Math.max(availed - allowed, 0);
			existingCoupon.setExceededQty(exceeded);
			existingCoupon.setIsDesclaimed(exceeded > 0);
		}

		FcmEmployeeCoupons updatedCoupon = employeeCouponsRepository.save(existingCoupon);

		try {
			List<FcmEmployeeCoupons> updatedCoupons = Collections.singletonList(updatedCoupon);
			byte[] excelData = generateExcelReport(updatedCoupons);

			// Send email with detailed changes
			sendEmailWithAttachment(excelData, previousBalanceQty, previousAdditionalQty, previousModifiedBy,
					previousModifiedDate, updatedCoupon);

			log.info("Email notification sent for updated additional coupons.");
		} catch (Exception e) {
			log.error("Failed to generate/send email report for additional coupons update: {}", e.getMessage(), e);
		}

		return updatedCoupon;
	}

	@Override
	public void fcmDeleteEmployeeCoupon(Integer id) {
		log.info("Deleting employee coupon with ID: {}", id);

		// Check if the coupon exists
		if (!employeeCouponsRepository.existsById(id)) {
			throw new DfcmsException(HttpStatus.NOT_FOUND, "Employee coupon with ID " + id + " not found.");
		}

		// Perform the delete operation
		employeeCouponsRepository.deleteById(id);
		log.info("Employee coupon with ID: {} successfully deleted.", id);
	}

//	@Override
//	public List<FcmEmployeeCouponsDTO> fcmGetEmployeeCouponsByMonth(int year, int month, String empId) {
//	    log.info("Fetching employee coupons for Year: {}, Month: {}, empId: {}", year, month, empId);
//	    try {
//	        LocalDate date = LocalDate.of(year, month, 1);
//
//	        // Fetch coupons by empId and month
//	        List<FcmEmployeeCoupons> coupons = getEmployeeCoupons(empId, date);
//
//	        // Convert each coupon to DTO with individual payGroupName
//	        return coupons.stream()
//	            .map(coupon -> {
//	                String couponEmpId = coupon.getEmpId();
//	                String payGroupName = userRepository.findByEmployeeId(couponEmpId)
//	                        .map(UserEntity::getPayGroupName)
//	                        .orElse(null);
//	                return toDTO(coupon, payGroupName);
//	            })
//	            .collect(Collectors.toList());
//
//	    } catch (Exception e) {
//	        log.error("Error fetching coupons", e);
//	        throw new DfcmsException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to fetch employee coupons");
//	    }
//	}

	@Override
	public List<FcmEmployeeCouponsDTO> fcmGetEmployeeCouponsByMonth(int year, int month, String empId) {
	    log.info("Fetching employee coupons for Year: {}, Month: {}, empId: {}", year, month, empId);
	    try {
	        LocalDate date = LocalDate.of(year, month, 1);
	        LocalDateTime[] period = calculateValidityPeriod(date);
	        LocalDateTime startDate = period[0];
	        LocalDateTime endDate = period[1];

	        // Fetch coupons along with payGroupName using the new query
	        List<Object[]> results = employeeCouponsRepository.findEmployeeCouponsWithPayGroup(
	            empId, startDate, endDate
	        );

	        
	  
	        // Convert the results to DTOs
	        return results.stream()
	            .map(row -> {
	            	return  convertRowToCoupon(row);
//	                String payGroupName = (String) row[20]; // paygroup_name is the last column (index 20)
//	                return toDTO(coupon, payGroupName);
	            })
	            .collect(Collectors.toList());

	    } catch (Exception e) {
	        log.error("Error fetching coupons", e);
	        throw new DfcmsException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to fetch employee coupons");
	    }
	}

	// Helper method to map Object[] to FcmEmployeeCoupons
//	private FcmEmployeeCoupons convertRowToCoupon(Object[] row) {
//	    FcmEmployeeCoupons coupon = new FcmEmployeeCoupons();
//
//	    coupon.setId((Integer) row[0]);
//	    coupon.setCreatedBy((String) row[1]);
//	    coupon.setCreationDate(convertToDate(row[2]));
//	    coupon.setLastModifiedBy((String) row[3]);
//	    coupon.setLastModifiedDate(convertToDate(row[4]));
//
//	    coupon.setAdditionalQty((Integer) row[5]);
//	    coupon.setAvailedQty((Integer) row[6]);
//	    coupon.setBalanceQty((Integer) row[7]);
//	    coupon.setCouponsQty((Integer) row[8]);
//	    coupon.setEmpId((String) row[9]);
//	    coupon.setExceededQty((Integer) row[10]);
//	    coupon.setIsDesclaimed((Boolean) row[11]);
//	    coupon.setOpeningQty((Integer) row[12]);
//	    coupon.setRemarks((String) row[13]);
//	    coupon.setStatus((Boolean) row[14]);
//	    coupon.setValidFrom(convertToLocalDateTime(row[15]));
//	    coupon.setValidTo(convertToLocalDateTime(row[16]));
//
//	    CouponMaster master = new CouponMaster();
//	    master.setId((Integer) row[17]);
//	    coupon.setCouponMaster(master);
//
//	    coupon.setAllowedQty((Integer) row[18]);
//
//	    return coupon;
//	}
	
	private FcmEmployeeCouponsDTO convertRowToCoupon(Object[] row) {
	    FcmEmployeeCouponsDTO coupon = new FcmEmployeeCouponsDTO();

	    coupon.setId((Integer) row[0]);
	    coupon.setCreatedBy((String) row[1]);
	    coupon.setCreationDate(convertToDate(row[2]));
	    coupon.setLastModifiedBy((String) row[3]);
	    coupon.setLastModifiedDate(convertToDate(row[4]));

	    coupon.setAdditionalQty((Integer) row[5]);
	    coupon.setAvailedQty((Integer) row[6]);
	    coupon.setBalanceQty((Integer) row[7]);
	    coupon.setCouponsQty((Integer) row[8]);
	    coupon.setEmpId((String) row[9]);
	    coupon.setOpeningQty((Integer) row[10]);
	    coupon.setRemarks((String) row[11]);
	    coupon.setStatus((Boolean) row[12]);
	    coupon.setValidFrom(convertToLocalDateTime(row[13]));
	    coupon.setValidTo(convertToLocalDateTime(row[14]));
	    coupon.setCouponMasterId((Integer) row[15]);
	    coupon.setExceededQty((Integer) row[16]);
	    coupon.setIsDesclaimed(Boolean.parseBoolean(String.valueOf(row[17])));
	    coupon.setAllowedQty((Integer) row[18]);
	    coupon.setPayGroupName(row[19] != null ? String.valueOf(row[19]) : null);

	    return coupon;
	}


	// Utility methods to handle date conversions
	private Date convertToDate(Object timestamp) {
	    return timestamp != null ? new Date(((Timestamp) timestamp).getTime()) : null;
	}

	private LocalDateTime convertToLocalDateTime(Object timestamp) {
	    return timestamp != null ? ((Timestamp) timestamp).toLocalDateTime() : null;
	}


	@Override
	public List<FcmEmployeeCoupons> getEmployeeCoupons(String empId, LocalDate date) {
		try {
			// Calculate validity period (current month or specific date)
			LocalDateTime[] period = calculateValidityPeriod(date);
			LocalDateTime startDate = period[0];
			LocalDateTime endDate = period[1];

			// Build dynamic query
			Specification<FcmEmployeeCoupons> spec = Specification
					.where((root, query, cb) -> cb.and(cb.lessThanOrEqualTo(root.get("validFrom"), endDate),
							cb.greaterThanOrEqualTo(root.get("validTo"), startDate)));

			// Add empId filter if provided
			if (empId != null && !empId.isEmpty()) {
				spec = spec.and((root, query, cb) -> cb.equal(root.get("empId"), empId));
			}

			return employeeCouponsRepository.findAll(spec);
		} catch (Exception e) {
			throw new DfcmsException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to fetch coupons: " + e.getMessage());
		}
	}

	private LocalDateTime[] calculateValidityPeriod(LocalDate date) {
		if (date == null) {
			// Default to current month
			LocalDate now = LocalDate.now();
			return new LocalDateTime[] { now.with(TemporalAdjusters.firstDayOfMonth()).atStartOfDay(),
					now.with(TemporalAdjusters.lastDayOfMonth()).atTime(23, 59, 59) };
		} else {
			// Use specific date (whole day)
			return new LocalDateTime[] { date.atStartOfDay(), date.atTime(23, 59, 59) };
		}
	}

	private byte[] generateExcelReport(List<FcmEmployeeCoupons> assignedCoupons) throws IOException {
		Workbook workbook = new XSSFWorkbook();
		Sheet sheet = workbook.createSheet("Monthly Coupons");

		// Create header row
		Row headerRow = sheet.createRow(0);
		String[] columns = { "Employee ID", "Valid From", "Valid To", "Coupons Assigned" };
		for (int i = 0; i < columns.length; i++) {
			Cell cell = headerRow.createCell(i);
			cell.setCellValue(columns[i]);
			cell.setCellStyle(getHeaderCellStyle(workbook));
		}

		// Populate data rows
		int rowIdx = 1;
		for (FcmEmployeeCoupons coupon : assignedCoupons) {
			Row row = sheet.createRow(rowIdx++);
			row.createCell(0).setCellValue(coupon.getEmpId());
			row.createCell(1).setCellValue(coupon.getValidFrom().toString());
			row.createCell(2).setCellValue(coupon.getValidTo().toString());
			row.createCell(3).setCellValue(coupon.getCouponsQty());
		}

		// Auto-size columns
		for (int i = 0; i < columns.length; i++) {
			sheet.autoSizeColumn(i);
		}

		// Write to byte array
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		workbook.write(outputStream);
		workbook.close();
		return outputStream.toByteArray();
	}

	private CellStyle getHeaderCellStyle(Workbook workbook) {
		CellStyle style = workbook.createCellStyle();
		Font font = workbook.createFont();
		font.setBold(true);
		style.setFont(font);
		return style;
	}

	private void sendEmailWithAttachment(byte[] attachmentData) throws MessagingException {
		MimeMessage message = mailSender.createMimeMessage();
		MimeMessageHelper helper = new MimeMessageHelper(message, true);

		SystemSettingsEntity receiverEmails = systemRepo.findByKey("COUPONS_RECEIVER_EMAIL");
		String toEmails = receiverEmails.getValue();
		helper.setFrom(senderEmail);
		helper.setTo(toEmails.split(","));

		SystemSettingsEntity ccEmails = systemRepo.findByKey("COUPONS_CC_RECEIVER_EMAIL");
		String ccMails = ccEmails.getValue();
		if (ccMails != null && !ccMails.isEmpty()) {
			helper.setCc(ccMails.split(","));
		}

		helper.setSubject("Monthly Coupon Assignment Report");
		helper.setText("Dear Team,\n\nPlease find attached the monthly coupon assignment report.\n\nRegards,\nOne AIG");

		// Attach Excel file
		helper.addAttachment("Monthly_Coupon_Assignment.xlsx", new ByteArrayDataSource(attachmentData,
				"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"));

		try {
			mailSender.send(message);
			log.info("Email with coupon report sent successfully.");
		} catch (Exception e) {
			log.error("Failed to send email with coupon report: {}", e.getMessage());
		}
	}

	private void sendEmailWithAttachment(byte[] excelData, int previousBalanceQty, int previousAdditionalQty,
			String previousModifiedBy, Date previousModifiedDate, FcmEmployeeCoupons updatedCoupon)
			throws MessagingException {
		MimeMessage message = mailSender.createMimeMessage();
		MimeMessageHelper helper = new MimeMessageHelper(message, true);

// Fetch recipient email addresses from system settings
		SystemSettingsEntity receiverEmails = systemRepo.findByKey("COUPONS_RECEIVER_EMAIL");
		String toEmails = receiverEmails.getValue();
		helper.setFrom(senderEmail);
		helper.setTo(toEmails.split(","));

// Fetch CC email addresses if available
		SystemSettingsEntity ccEmails = systemRepo.findByKey("COUPONS_CC_RECEIVER_EMAIL");
		String ccMails = ccEmails.getValue();
		if (ccMails != null && !ccMails.isEmpty()) {
			helper.setCc(ccMails.split(","));
		}

// Set email subject and body with detailed changes
		String subject = "Employee Coupon Update Report";
		String body = String.format(
				"Dear Team,\n\n" + "The following changes have been made to the employee coupon:\n\n"
						+ "Employee ID: %s\n" + "Previous Balance Qty: %d\n" + "Updated Balance Qty: %d\n"
						+ "Previous Additional Qty: %d\n" + "Updated Additional Qty: %d\n"
						+ "Last Modified By: %s (Previous: %s)\n" + "Last Modified Date: %s (Previous: %s)\n\n"
						+ "Please find the attached report for more details.\n\n" + "Best Regards,\nYour Team",
				updatedCoupon.getEmpId(), previousBalanceQty, updatedCoupon.getBalanceQty(), previousAdditionalQty,
				updatedCoupon.getAdditionalQty(), updatedCoupon.getLastModifiedBy(), previousModifiedBy,
				updatedCoupon.getLastModifiedDate(), previousModifiedDate);

		helper.setSubject(subject);
		helper.setText(body);

// Attach Excel report
		helper.addAttachment("Employee_Coupon_Update_Report.xlsx", new ByteArrayDataSource(excelData,
				"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"));

// Send email
		try {
			mailSender.send(message);
			log.info("Email with employee coupon update report sent successfully.");
		} catch (Exception e) {
			log.error("Failed to send email with employee coupon update report: {}", e.getMessage());
		}
	}

	private FcmEmployeeCouponsDTO toDTO(FcmEmployeeCoupons entity, String payGroupName) {
		
		log.info(entity.getBalanceQty()+"***********");
		FcmEmployeeCouponsDTO dto = new FcmEmployeeCouponsDTO();
		dto.setId(entity.getId());
		dto.setEmpId(entity.getEmpId());
		dto.setPayGroupName(payGroupName);
		dto.setCouponMasterId(entity.getCouponMaster().getId());
		dto.setCouponsQty(entity.getCouponsQty());
		dto.setOpeningQty(entity.getOpeningQty());
		dto.setAvailedQty(entity.getAvailedQty());
		dto.setBalanceQty(entity.getBalanceQty());
		dto.setAdditionalQty(entity.getAdditionalQty());
		dto.setExceededQty(entity.getExceededQty());
		dto.setIsDesclaimed(entity.getIsDesclaimed());
		dto.setAllowedQty(entity.getAllowedQty());
		dto.setStatus(entity.getStatus());
		dto.setRemarks(entity.getRemarks());
		dto.setValidFrom(entity.getValidFrom());
		dto.setValidTo(entity.getValidTo());

		dto.setCreatedBy(entity.getCreatedBy());
		dto.setCreationDate(entity.getCreationDate());
		dto.setLastModifiedBy(entity.getLastModifiedBy());
		dto.setLastModifiedDate(entity.getLastModifiedDate());

		return dto;
	}

}