package com.aig.aigone.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.aig.aigone.exception.AigOneException;
import com.aig.aigone.exception.PrismApiError;
import com.aig.aigone.exception.PrismApiSuccess;
import com.aig.aigone.images.InsuranceCardPdf;
import com.aig.aigone.model.dto.BedRequestDto;
import com.aig.aigone.model.dto.CTFieldDto;
import com.aig.aigone.model.dto.CTSubmitRequestDto;
import com.aig.aigone.model.dto.ChecklistTemplateDetailsDto;
import com.aig.aigone.model.dto.ChecklistTemplatesDto;
import com.aig.aigone.model.dto.DoctorConsultTemplateConfigDto;
import com.aig.aigone.model.dto.DoctorTemplateDto;
import com.aig.aigone.model.dto.EmployeeRosterDto;
import com.aig.aigone.model.dto.FavMedicationDto;
import com.aig.aigone.model.dto.METPatientRequestDto;
import com.aig.aigone.model.dto.OpConsultationDto;
import com.aig.aigone.model.dto.PatientRequestAppointmentDto;
import com.aig.aigone.model.dto.PatientRequestAppointmentLogDto;
import com.aig.aigone.model.dto.UserDto;
import com.aig.aigone.model.dto.UserSessionVO;
import com.aig.aigone.model.dto.emr.METPatientDto;
import com.aig.aigone.model.dto.his.AppointmentsDetailsDto;
import com.aig.aigone.model.dto.his.AppointmentsRequestDto;
import com.aig.aigone.model.dto.his.AppointmentsSummaryDto;
import com.aig.aigone.model.dto.his.DoctorAppointmentDetailsDaywiseRequestDto;
import com.aig.aigone.model.dto.his.DoctorAvailableSlotsDto;
import com.aig.aigone.model.dto.his.DoctorScheduleRequestDto;
import com.aig.aigone.model.dto.his.PateintPreviousAppointmentsDto;
import com.aig.aigone.model.dto.his.PatientFlaggedDetailsDto;
import com.aig.aigone.model.dto.his.PatientIPDetailsDto;
import com.aig.aigone.model.dto.his.PatientOPDetailsDto;
import com.aig.aigone.model.dto.his.PatientPlannedDetailsDto;
import com.aig.aigone.model.dto.his.ReferralIPPatientDetailsDto;
import com.aig.aigone.model.dto.his.ReportStatusDto;
import com.aig.aigone.model.entity.aigone.ChecklistSubmittedEntity;
import com.aig.aigone.model.entity.aigone.LookupOptionsMasterEntity;
import com.aig.aigone.model.entity.aigone.LookupValuesMasterEntity;
import com.aig.aigone.model.entity.aigone.PAFavorite;
import com.aig.aigone.model.entity.aigone.UserEntity;
import com.aig.aigone.repository.aigone.UserRepository;
import com.aig.aigone.security.SecurityUtil;
import com.aig.aigone.service.Bed360Service;
import com.aig.aigone.service.EmrService;
import com.aig.aigone.service.FileUploadService;
import com.aig.aigone.service.HisService;
import com.aig.aigone.service.OneAigService;
import com.aig.aigone.service.PAFavoriteService;
import com.aig.aigone.service.SystemSettingService;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.constraints.NotBlank;

import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

@RestController
@RequestMapping("/api/his/employee")
public class EmployeeController {

	@Autowired
	private HisService hisService;

	@Autowired
	private EmrService emrService;

	@Autowired
	private OneAigService oneAigService;

	@Autowired
	private Bed360Service bed360Service;

	@Autowired
	private FileUploadService fileUploadService;

	@Autowired
	private UserRepository userRepo;

	@Autowired
	private InsuranceCardPdf insuranceCardPdf;

	@Autowired
	private PAFavoriteService paFavoriteService;
	
	

	@Operation(summary = "employedd insurance card info")
	@GetMapping(value = "/view", produces = MediaType.APPLICATION_PDF_VALUE)
	public ResponseEntity<byte[]> viewPdf() {

		byte[] pdfBytes = insuranceCardPdf.getPdfFileFromRemote();

		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_PDF);
		headers.setContentDisposition(ContentDisposition.inline().filename("document.pdf").build());

		return new ResponseEntity<>(pdfBytes, headers, HttpStatus.OK);
	}

	@Operation(summary = "fetch profile details ")
	@GetMapping("/profile")
	public UserDto profile() {

		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return hisService.fetchUserByPhoneNo(currentUser.getPhoneNo());
	}

	@Operation(summary = "fetch profile details ")
	@GetMapping("/userDetails/{employeeId}")
	public UserDto userDetails(@PathVariable(name = "employeeId", required = true) @NotBlank String employeeId) {
		return hisService.fetchUserDetailsByEmployeeId(employeeId);
	}
	
	@Operation(summary = "delete User ")
	@DeleteMapping("/delete")
	public String delete() {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		String message = null;
		UserEntity user = userRepo.findByPhoneNoAndActiveTrue(currentUser.getPhoneNo()).orElseThrow(
				() -> new UsernameNotFoundException("User Not Found with Phone Number: " + currentUser.getPhoneNo()));
		if (StringUtils.isEmpty(user.getEmployeeId())) {
			user.setActive(false);
			userRepo.save(user);
			message = "User successfully deleted";
		} else {
//			message = "Employee will be deleted automatically when the employee has resigned.";
//			throw new AigOneException("Employee will be deleted automatically when the employee has resigned.");
			throw new AigOneException("For Employee Deletion, please send an email to your HR.");
		}
		return message;
	}

	@Operation(summary = "fetch mapped doctors ")
	@GetMapping("/mappedDoctors")
	public List<UserDto> mappedDoctors() {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return hisService.fetchMappedDoctorsByEmployeeId(currentUser.getEmployeeId());
	}

	@Operation(summary = "fetch doctor appointments summary for given date ")
	@GetMapping("/appointments/summary/{employeeId}/{date}")
	public List<AppointmentsSummaryDto> appointmentsSummary(
			@PathVariable(name = "employeeId", required = true) @NotBlank String employeeId,
			@PathVariable(name = "date", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
		return hisService.fetchAppointmentsSummary(employeeId, date);
	}

	@Operation(summary = "fetch doctor appointments details for given date ")
	@GetMapping("/appointments/details/{employeeId}/{date}")
	public List<AppointmentsDetailsDto> appointmentsDetails(
			@PathVariable(name = "employeeId", required = true) @NotBlank String employeeId,
			@PathVariable(name = "date", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
		return hisService.fetchAppointmentDetails(employeeId, date);
	}

	@Operation(summary = "uodate doctor appointments details for given date ")
	@PostMapping("/appointments/details/{employeeId}/{date}")
	public List<AppointmentsDetailsDto> updateAppointments(@RequestBody AppointmentsRequestDto appointmentsRequestDto) {
		return hisService.updateAppointmentDetails(appointmentsRequestDto);
	}

	@Operation(summary = "fetch doctor appointments summary for given date ")
	@GetMapping("/{employeeId}/ip-details")
	public List<PatientIPDetailsDto> ipPatientsDetails(
			@PathVariable(name = "employeeId", required = true) @NotBlank String employeeId) {
		return hisService.fetchPatiennIpDetailsByDoctorId(Long.valueOf(employeeId));
	}

	@Operation(summary = "fetch IP Patients by doctor employee id")
	@GetMapping("/getIpPatients/{doctorId}")
	public List<PatientIPDetailsDto> getIpPatients(
			@PathVariable(name = "doctorId", required = true) @NotBlank String doctorId) {
		return hisService.fetchPatiennIpDetailsByDoctorId(doctorId);
	}

	@Operation(summary = "fetch doctor appointments summary for given date ")
	@GetMapping("/{employeeId}/op-details/{date}")
	public List<PatientOPDetailsDto> opPatientsDetails(
			@PathVariable(name = "employeeId", required = true) @NotBlank String employeeId,
			@PathVariable(name = "date", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
		return hisService.fetchPatientOpDetailsByDoctorId(employeeId, date);
	}

	@Operation(summary = "update employee attendance ")
	@PostMapping("/attendance/{employeeId}/{type}")
	public String updateAttendance(@PathVariable(name = "employeeId", required = true) String employeeId,
			@PathVariable(name = "type", required = true) String type) {
		return hisService.updateAttendance(employeeId, type);
	}

	@Operation(summary = "fetch DoctorSchedulesForSpecificDays")
	@PostMapping("/appointments")
	public List<String> fetchDoctorSchedulesForSpecificDays(@RequestBody DoctorScheduleRequestDto requestDto) {
		return hisService.fetchDoctorSchedulesForSpecificDays(requestDto);
	}

	@Operation(summary = "fetch DoctorAppointmentDetailsDaywise")
	@PostMapping("/appointments/details")
	public List<DoctorAvailableSlotsDto> fetchDoctorAppointmentDetailsDaywise(
			@RequestBody DoctorAppointmentDetailsDaywiseRequestDto requestDto) {
		return hisService.fetchDoctorAvailableSlotsDaywise(requestDto);
	}

	@Operation(summary = "fetch Employee Rosters")
	@GetMapping("/roster/{date}")
	public List<EmployeeRosterDto> fetchEmployeeRosterDateWise(
			@PathVariable(name = "date", required = true) String date,
			@RequestParam(name = "department", required = true) String department,
			@RequestParam(name = "sub_department", required = false) String subDepartment) {
		return hisService.fetchEmployeeRosterDateWise(date, department, subDepartment);
	}

	@Operation(summary = "fetch Specialities")
	@GetMapping("/specialities")
	public List<String> fetchSpecialities() {
		return hisService.fetchSpecialities();
	}

	@Operation(summary = "create checklist")
	@PostMapping("/checklist/create")
	public List<ChecklistTemplatesDto> createChecklist(@RequestBody ChecklistTemplateDetailsDto createTempDto) {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return hisService.createChecklist(createTempDto, currentUser.getPhoneNo());

	}

	@Operation(summary = "submit checklist")
	@PostMapping("/checklist/submit")
	public List<ChecklistSubmittedEntity> submitChecklist(@RequestBody CTSubmitRequestDto ctSubmitDto) {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return hisService.submitChecklist(ctSubmitDto, currentUser.getPhoneNo());

	}

	@Operation(summary = "fetch lookup options")
	@GetMapping("/lookup/options")
	public List<LookupOptionsMasterEntity> fetchLookupOptions() {
		return hisService.fetchLookupOptions();
	}

	@Operation(summary = "fetch lookup values")
	@GetMapping("/lookup/values/{type}")
	public List<LookupValuesMasterEntity> fetchLookupValues(@PathVariable(name = "type", required = true) String type) {
		return hisService.fetchLookupValues(type);
	}

	@Operation(summary = "fetch all checklist templates")
	@GetMapping("/checklist")
	public List<ChecklistTemplatesDto> fetchChecklistTemplates() {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return hisService.fetchChecklistTemplates(currentUser.getPhoneNo());
	}

	@Operation(summary = "fetch checklist field details")
	@GetMapping("/checklist/{templateId}/{fieldId}")
	public CTFieldDto fetchChecklistFieldDetails(@PathVariable(name = "templateId", required = true) int templateId,
			@PathVariable(name = "fieldId", required = true) int fieldId) {
		return hisService.fetchChecklistFieldDetails(templateId, fieldId);
	}

	@Operation(summary = "fetch check list details")
	@GetMapping("/checklist/details/{templateId}")
	public ChecklistTemplateDetailsDto fetchChecklistTemplateDetails(
			@PathVariable(name = "templateId", required = true) int templateId) {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return hisService.fetchChecklistTemplateDetails(templateId, currentUser.getPhoneNo());

	}

	@Operation(summary = "fetch Submitted Checklists")
	@GetMapping("/checklist/submitted")
	public List<ChecklistTemplateDetailsDto> fetchSubmittedChecklists() {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return hisService.fetchSubmittedChecklists(currentUser.getPhoneNo());
	}

	@Operation(summary = "fetch check list details")
	@PostMapping("uploadFile")
	public String uploadFIle(@RequestBody CTSubmitRequestDto dto) {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return fileUploadService.upload(dto.getStatus(),
				String.valueOf(System.currentTimeMillis()) + "_" + dto.getFileName(), null);
	}

	@Operation(summary = "checklist review")
	@PostMapping("/checklist/review/{checklistSubmittedId}/{status}")
	public String reviewChecklist(
			@PathVariable(name = "checklistSubmittedId", required = true) String checklistSubmittedId,
			@PathVariable(name = "status", required = true) String status) {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return hisService.reviewChecklist(checklistSubmittedId, status, currentUser.getPhoneNo());
	}

	@Operation(summary = "upload employee roster ")
	@PostMapping("/roster/upload")
	public String uploadRoster(@RequestParam("file") MultipartFile file) {
		return hisService.uploadRoster(file);
	}

	@Operation(summary = "fetch doctor request appointments")
	@GetMapping("/doctorRequestAppointments/{doctorId}/{date}")
	public List<PatientRequestAppointmentDto> fetchDoctorRequestedAppointments(
			@PathVariable(name = "doctorId", required = true) int doctorId,
			@PathVariable(name = "date", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
		return hisService.fetchDoctorRequestedAppointments(doctorId, date);
	}

	@Operation(summary = "request appointment approval")
	@PostMapping("/requestAppointment/review")
	public String reviewRequestedAppointment(@RequestBody PatientRequestAppointmentLogDto dto) {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return hisService.reviewRequestedAppointment(dto, currentUser.getPhoneNo());
	}

	@GetMapping("/metPatients")
	public List<METPatientDto> fetchMETPatients(@RequestParam(name = "mewsScore", required = false) Integer mewsScore) {
		return hisService.fetchMETPatients(mewsScore);
	}

	@PostMapping("/addMetPatient")
	public String addMETPatient(@RequestBody METPatientDto patientDto) {
		return hisService.addMETPatient(patientDto);
	}

	@PostMapping("/met/acknowledge")
	public String acknowledgePatientCondition(@RequestBody METPatientRequestDto dto) {
		return hisService.acknowledgePatientCondition(dto);
	}

	@Operation(summary = "fetch doctor Previous Appointments")
	@GetMapping("/previousAppointments/{doctorId}")
	public List<PateintPreviousAppointmentsDto> fetchPreviousAppointmentsOfDotor(
			@PathVariable(name = "doctorId", required = true) String doctorId,
			@RequestParam(name = "fromDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date fromDate,
			@RequestParam(name = "toDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date toDate) {
		return hisService.fetchPreviousAppointmentsOfDotor(doctorId, fromDate, toDate);
	}
	
	@Operation(summary = "Fetch previous appointments for given doctor(s)")
	@GetMapping("/previousAppointments/my-teams")
	public List<PateintPreviousAppointmentsDto> fetchPreviousAppointmentsOfDotorByTeam(
	        @RequestParam(name = "fromDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date fromDate,
	        @RequestParam(name = "toDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date toDate) {
	    
	    return hisService.fetchPreviousAppointmentsOfDotorByTeam( fromDate, toDate);
	}


	@Operation(summary = "fetch doctor planned Appointments")
	@GetMapping("/plannedAppointments/{doctorId}")
	public List<PatientPlannedDetailsDto> fetchPlannedAppointmentsOfDotor(
			@PathVariable(name = "doctorId", required = true) String doctorId,
			@RequestParam(name = "fromDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date fromDate,
			@RequestParam(name = "action", required = true) String action) {
		return hisService.fetchPlannedAppointmentsOfDotor(doctorId, fromDate, action);
	}

	@Operation(summary = "fetch patient consultation notes by doctor")
	@GetMapping("/appointments/{doctorEmployeeId}/consultationNote/{uhId}")
	public List<OpConsultationDto> fetchPatientConsultationNotes(
			@PathVariable(name = "doctorEmployeeId", required = false) String doctorEmployeeId,
			@PathVariable(name = "uhId", required = true) String uhId) {
		return hisService.fetchPatientConsultationNotes(doctorEmployeeId, uhId);
	}

	@Operation(summary = "Update employee roster data")
	@PostMapping("/roster/update")
	public String updateEmployeeRoster(@RequestBody EmployeeRosterDto rosterDto) {
		return hisService.updateEmployeeRoster(rosterDto);
	}

	@Operation(summary = "Create favourite patients")
	@PostMapping("/favourite/patient/{uhId}/{doctorId}/{follow}")
	public void saveFavouritePatients(@PathVariable(name = "uhId", required = true) String uhId,
			@PathVariable(name = "doctorId", required = false) String doctorId,
			@PathVariable(name = "follow", required = true) boolean follow) {
		hisService.saveFavouritePatient(uhId, doctorId, follow);
	}

	@Operation(summary = "get all favourite patients")
	@GetMapping("/favourite/patients")
	public List<PatientIPDetailsDto> geAllFavouritePatients() {
		return hisService.fetchFavouritePatientsByDoctorId();
	}

	@Operation(summary = "get all favourite patients")
	@GetMapping("/favourite/patients_by_doctorId/{doctorId}")
	public List<PatientFlaggedDetailsDto> getAllFavouritePatients(
			@PathVariable(name = "doctorId", required = true) String doctorId) {
		return hisService.fetchAllFavouritePatientsByDoctorId(doctorId);
	}

	@Operation(summary = "get all favourite patients")
	@GetMapping("/search/patients/{doctorId}/{searchString}")
	public List<PatientFlaggedDetailsDto> getAllFavouritePatients(
			@PathVariable(name = "searchString", required = true) String searchString,
			@PathVariable(name = "doctorId", required = true) String doctorId) {
		return hisService.searchPatients(searchString, doctorId);
	}

	@Operation(summary = "get all referral patients")
	@GetMapping("/referral/patients")
	public List<PatientIPDetailsDto> fetchAllReferralPatientsByDoctorId() {
		String employeeId = SecurityUtil.getCurrentUser().getEmployeeId();
//		employeeId = "20033";
		return emrService.fetchAllReferralPatientsByDoctorId(employeeId);
	}

	@Operation(summary = "get all referral patients")
	@GetMapping("/referral/patients/{doctorId}")
	public List<ReferralIPPatientDetailsDto> fetchAllReferralPatientsByDoctorEmpId(
			@PathVariable(name = "doctorId", required = true) String doctorId) {
		return emrService.fetchAllReferralPatientsByDoctorEmpId(doctorId);
	}

	@Operation(summary = "get report status")
	@GetMapping("/orders/getReportStatus/{billId}")
	public List<ReportStatusDto> getReportStatus(@PathVariable(name = "billId", required = true) String billId) {
		return hisService.getReportStatus(billId);
	}

	@PostMapping("/saveTemplate")
	public void saveTemplate(@RequestBody DoctorTemplateDto request) {
		emrService.saveTemplate(request);
	}

	@GetMapping("/getTemplates/{doctorId}")
	public List<DoctorTemplateDto> getTemplates(@PathVariable(name = "doctorId", required = true) String doctorId) {
		return emrService.getDoctorTemplates(doctorId);
	}

	@GetMapping("/getTemplateConfig/{doctorId}")
	public List<DoctorConsultTemplateConfigDto> getConsultTemplate(
			@PathVariable(name = "doctorId", required = true) String doctorId) {
		return oneAigService.getConsultTemplate(doctorId);
	}

	@PostMapping("/saveConsultTemplateConfig/{doctorId}")
	public void saveConsultTemplateConfig(@PathVariable(name = "doctorId", required = true) String doctorId,
			@RequestBody List<DoctorConsultTemplateConfigDto> configList) {
		System.out.println("test");
		oneAigService.saveConsultTemplateConfig(doctorId, configList);
	}

	@PostMapping("/saveFavMedication/{doctorId}/{type}")
	@Operation(summary = "Save or update favorite medications")
	@ApiResponses(value = {
	    @ApiResponse(responseCode = "200", description = "Medications saved or updated successfully"),
	    @ApiResponse(responseCode = "400", description = "Invalid input")
	})
	public ResponseEntity<String> saveOrUpdateFavMedication(
	        @PathVariable(name = "doctorId") String doctorId,
	        @PathVariable(name = "type") String type,
	        @RequestBody List<FavMedicationDto> favMedList) {

	    String message = oneAigService.saveOrUpdateFavMedication(doctorId, type, favMedList);
	    return ResponseEntity.ok(message);
	}


	@GetMapping("/getFavMedication/{doctorId}")
	public List<FavMedicationDto> getFavMedication(@PathVariable(name = "doctorId", required = true) String doctorId) {
		return oneAigService.getFavMedication(doctorId);
	}

	@GetMapping("/getFavMedication/{doctorId}/{code}")
	public FavMedicationDto getFavMedication(@PathVariable(name = "doctorId", required = true) String doctorId,
			@PathVariable(name = "code", required = true) String code) {
		return oneAigService.getFavMedicationAndCode(doctorId, code);
	}

	@PostMapping("/initiateBedRequest")
	public String initiateBedRequest(@RequestBody BedRequestDto bedRequest) {
		System.out.println("test");
		return bed360Service.initiateBedRequest(bedRequest);
	}
	
	@GetMapping("/getPAFavorite/{doctorId}")
	public List<PAFavorite> getPAFavoritesByDoctorId(@PathVariable String doctorId){
		return paFavoriteService.findAllByDoctorEmployeeId(doctorId);
	}
	
	@PostMapping("/savePAFavMedication/{doctorId}/{name}")
	@Operation(summary = "Save or update PA favorites")
	@ApiResponses(value = {
	    @ApiResponse(responseCode = "200", description = "Favorites processed"),
	    @ApiResponse(responseCode = "400", description = "Bad request"),
	    @ApiResponse(responseCode = "404", description = "Record not found")
	})
	public ResponseEntity<String> savePAFavMedication(
	        @PathVariable String doctorId,
	        @PathVariable String name,
	        @RequestBody List<PAFavorite> paFavMedList) {

	    String resultMessage = paFavoriteService.saveOrUpdatePAFavMedication(doctorId, name, paFavMedList);
	    return ResponseEntity.ok(resultMessage);
	}

	
	@DeleteMapping("/favorites/{id}")
	@Operation(
	    summary = "Delete a favorite medication",
	    description = "Deletes a single favorite medication by its ID, doctor ID, and type (e.g., 'PA')"
	)
//	@ApiResponses(value = {
//	    @ApiResponse(responseCode = "200", description = "Medication deleted successfully"),
//	    @ApiResponse(responseCode = "404", description = "Medication not found"),
//	    @ApiResponse(responseCode = "400", description = "Invalid input")
//	})
	public ResponseEntity<String> deleteFavorite(
	    @Parameter(description = "Medication ID to delete") @PathVariable Long id,
	    @Parameter(description = "Doctor Employee ID") @RequestParam String doctorId,
	    @Parameter(description = "Type of favorite (e.g., 'PA''Drug')") @RequestParam String type) {
	    
		oneAigService.deleteFavMedicationById(id, doctorId, type);
	    return ResponseEntity.ok("Favorite "+type+" deleted with id: " + id);
	}
	
	
	@DeleteMapping("/pa-favorites/{id}")
	@Operation(
	    summary = "Delete a PA favorite Present complaints, Allergies, Medical history, Surgical history",
	    description = "Deletes a single PA favorite Present complaints Or Allergies Or Medical history Or Surgical history using ID, doctor employee ID, and name"
	)
	@ApiResponses(value = {
	    @ApiResponse(responseCode = "200", description = "PA Favorite deleted successfully"),
	    @ApiResponse(responseCode = "404", description = "PA Favorite not found"),
	    @ApiResponse(responseCode = "400", description = "Invalid input")
	})
	public ResponseEntity<String> deletePAFavorite(
	        @Parameter(description = "PA Favorite ID to delete", required = true)
	        @PathVariable Long id,

	        @Parameter(description = "Doctor Employee ID", required = true)
	        @RequestParam String doctorId,

	        @Parameter(description = "PA Favorite name", required = true)
	        @RequestParam String name) {

	    paFavoriteService.deleteByIdDoctorAndName(id, doctorId, name);
	    return ResponseEntity.ok("PA Favorite deleted with id: " + id);
	}
	
	
	@Operation(
	        summary = "Deactivate a Doctor Template",
	        description = "Deactivates a template if it exists and is currently active. Requires both template ID and doctorEmployeeId.",
	        responses = {
	            @ApiResponse(responseCode = "200", description = "Template deactivated successfully"),
	            @ApiResponse(responseCode = "404", description = "Template not found", content = @Content(schema = @Schema(implementation = PrismApiError.class))),
	            @ApiResponse(responseCode = "400", description = "Template already inactive", content = @Content(schema = @Schema(implementation = PrismApiError.class)))
	        }
	    )
	@PutMapping("/{id}/deactivate-doctor-template")
	public ResponseEntity<PrismApiSuccess> deactivateTemplate(
	        @PathVariable Long id,
	        @RequestParam String doctorEmployeeId) {
	    
	    emrService.deactivateDoctorTemplate(id, doctorEmployeeId);
	    
	    PrismApiSuccess response = new PrismApiSuccess(HttpStatus.OK, " Template deactivated ");
	    return ResponseEntity.ok(response);
	}
	
	
	

}



