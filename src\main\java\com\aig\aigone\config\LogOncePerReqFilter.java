package com.aig.aigone.config;

//
//@Component
//@Slf4j
public class LogOncePerReqFilter {
	
//	@Autowired
//	EventLogRepository eventLogRepo;
//
//	@Override
//	protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
//			throws ServletException, IOException {
//		ContentCachingRequestWrapper cachingRequestWrapper = new ContentCachingRequestWrapper(request);
//		ContentCachingResponseWrapper cachingResponseWrapper = new ContentCachingResponseWrapper(response);
//		
//		filterChain.doFilter(cachingRequestWrapper, cachingResponseWrapper);
//		
//		String requestBody = getValueAsString(cachingRequestWrapper.getContentAsByteArray(),cachingRequestWrapper.getCharacterEncoding());
//		String responseBody = getValueAsString(cachingResponseWrapper.getContentAsByteArray(),cachingResponseWrapper.getCharacterEncoding());
//		
//		logReqRes(requestBody, responseBody,cachingRequestWrapper.getRequestURI(),cachingRequestWrapper.getMethod());
//		
//		cachingResponseWrapper.copyBodyToResponse();
//	}
//
//	private String getValueAsString(byte[] contentAsByteArray, String characterEncoding) {
//		String dataAsString = "";
//		try {
//			dataAsString = new String(contentAsByteArray, characterEncoding);
//		}catch (Exception e) {
//			log.error("Exception occurred while converting byte into an array: {}",e.getMessage());
//			e.printStackTrace();
//		}
//		return dataAsString;
//	}
//	
//	@Async
//	private void logReqRes(String request,String response,String uri, String httpMethod) {
//		EventLogEntity logReqRes = new EventLogEntity();
//		logReqRes.setRequest(request);
//		logReqRes.setResponse(response);
//		logReqRes.setUri(uri);
//		logReqRes.setHttpMethod(httpMethod);
//		
//		
//		
//		eventLogRepo.save(logReqRes);
//	}

}