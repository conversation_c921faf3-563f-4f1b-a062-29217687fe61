#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x000001df43410fa4, pid=13316, tid=22496
#
# JRE version: Java(TM) SE Runtime Environment (21.0.3+7) (build 21.0.3+7-LTS-152)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (21.0.3+7-LTS-152, mixed mode, emulated-client, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# Problematic frame:
# v  ~BufferBlob::MethodHandles adapters 0x000001df43410fa4
#
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   https://bugreport.java.com/bugreport/crash.jsp
#

---------------  S U M M A R Y ------------

Command Line: -XX:+ShowCodeDetailsInExceptionMessages -agentlib:jdwp=transport=dt_socket,suspend=y,address=localhost:49933 -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=49882 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Djava.rmi.server.hostname=localhost -Dspring.jmx.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -Dspring.application.admin.enabled=true -XX:TieredStopAtLevel=1 -Dspring.boot.project.name=AIG-ONE -javaagent:D:\sts-4.22.1.RELEASE\configuration\org.eclipse.osgi\266\0\.cp\lib\javaagent-shaded.jar -Dfile.encoding=UTF-8 -Dstdout.encoding=UTF-8 -Dstderr.encoding=UTF-8 com.aig.aigone.AigOneApplication --spring.output.ansi.enabled=always

Host: 12th Gen Intel(R) Core(TM) i7-1260P, 16 cores, 15G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5438)
Time: Tue Mar 11 14:24:54 2025 India Standard Time elapsed time: 308.937680 seconds (0d 0h 5m 8s)

---------------  T H R E A D  ---------------

Current thread (0x000001df985f3bb0):  JavaThread "http-nio-8080-exec-4" daemon [_thread_in_Java, id=22496, stack(0x0000005c84200000,0x0000005c84300000) (1024K)]

Stack: [0x0000005c84200000,0x0000005c84300000],  sp=0x0000005c842f98b8,  free space=998k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
C  0x000001df43410fa4

The last pc belongs to _linkToVirtual (printed below).

siginfo: EXCEPTION_ACCESS_VIOLATION (0xc0000005), reading address 0x000001df9e72b76b


Registers:
RAX=0x000001df433e9086, RBX=0x0000000713655a70, RCX=0x0000005c842f98c8, RDX=0x0000000000000003
RSP=0x0000005c842f98b8, RBP=0x0000005c842f9910, RSI=0x000001df94c81548, RDI=0x000001df9e22df40
R8 =0x0000000000000004, R9 =0x0000000000000004, R10=0x000001df9e72b5a3, R11=0x0000000000000000
R12=0x0000000000000000, R13=0x0000005c842f98b8, R14=0x0000005c842f9938, R15=0x000001df985f3bb0
RIP=0x000001df43410fa4, EFLAGS=0x0000000000010206


Register to memory mapping:

RAX=0x000001df433e9086 is at code_begin+3078 in an Interpreter codelet
return entry points  [0x000001df433e8480, 0x000001df433ea298]  7704 bytes
RBX=0x0000000713655a70 is an oop: com.aig.aigone.model.dto.OpConsultationDto 
{0x0000000713655a70} - klass: 'com/aig/aigone/model/dto/OpConsultationDto'
 - ---- fields (total size 11 words):
 - protected 'createdBy' 'Ljava/lang/String;' @12  null (0x00000000)
 - protected 'creationDate' 'Ljava/util/Date;' @16  null (0x00000000)
 - protected 'lastModifiedBy' 'Ljava/lang/String;' @20  null (0x00000000)
 - protected 'lastModifiedDate' 'Ljava/util/Date;' @24  null (0x00000000)
 - private 'id' 'Ljava/lang/Long;' @28  null (0x00000000)
 - private 'uhId' 'Ljava/lang/String;' @32  "AIGG.20809098"{0x0000000713655cc0} (0xe26cab98)
 - private 'consultationNotes' 'Ljava/lang/String;' @36  "{"op_consult":{"diagnosisList":[],"MedicationsList":[{"itemCode":"1000007659","itemName":"DOLO-T TAB","duration":5,"durationType":"day","frequency":"Four Times a Day (F4D)","route":"Oral","type":"Tablet","quantity":"20","serviceGrp":"N/A","orgGen":"PARACETAMOL/ACETAMINOPHEN (325MG) + TRAMADOL (37.5MG)","dosageList":["1-1-1-1"],"dosage":"1-1-1-1"}],"labOrdersList":[{"itemCode":"APCP040","itemName":"CBP (COMPLETE BLOOD PICTURE)","serviceGrp":"N/A"},{"itemCode":"AOPP020","itemName":"LFT (LIVER FUNCTION TEST)","serviceGrp":"N/A"},{"itemCode":"ACCE508","itemName":"RFT-RENAL FUNCTION TESTS (UREA, CREAT, ELECT)","serviceGrp":"N/A"}],"Review/Admission":{"action":"Admission / Surgery","managementType":"surgical","surgeryName":"ZYGOMA CLOSED REPAIR","implantsDone":"yes","paymentMethod":"Corporate-Credit","lengthOfStay":"6 Days","bedClass":"General","plannedDate":"2025-03-12","remarks":"test","implants":"AIGG.2054"},"referralsList":[]},"patient_details":{"patientName":"Mr. MANIKANTA  B","gender":"M","age":25,"encounterId":"1100.OP.768467","practitionerId":"10009303","practitionerName":"Dr. RAKESH KALAPALA (Med.Gastro)","visitDate":"2025-03-11T10:25:37.000+05:30","temp":null,"pulse":null,"respiratoryRate":null,"systolicBP":null,"diastolicBP":null,"spo2":null,"height":null,"weight":null,"address":"2U3183U,Hyderabad,Telangana,India.","uhid":"AIGG.20809098","visitId":768467,"hospCode":"1100"}}"{0x0000000713656e90} (0xe26cadd2)
 - private 'doctorEmployeeId' 'Ljava/lang/String;' @40  "10009303"{0x0000000713655d10} (0xe26caba2)
 - private 'encounterId' 'Ljava/lang/String;' @44  "1100.OP.768467"{0x0000000713655d58} (0xe26cabab)
 - private 'billId' 'Ljava/lang/Integer;' @48  a 'java/lang/Integer'{0x0000000713655c98} = 1318201 (0xe26cab93)
 - private 'creationTime' 'Ljava/util/Date;' @52  null (0x00000000)
 - private 'doctorName' 'Ljava/lang/String;' @56  null (0x00000000)
 - private 'action' 'Ljava/lang/String;' @60  "Admission / Surgery"{0x0000000713659908} (0xe26cb321)
 - private 'plannedDate' 'Ljava/util/Date;' @64  a 'java/util/Date'{0x000000071367f900} (0xe26cff20)
 - private 'plannedTime' 'Ljava/lang/String;' @68  ""{0x00000007059deb30} (0xe0b3bd66)
 - private 'status' 'Lcom/aig/aigone/model/entity/aigone/ConsultationStatusEnum;' @72  a 'com/aig/aigone/model/entity/aigone/ConsultationStatusEnum'{0x000000070626fb78} (0xe0c4df6f)
 - private 'type' 'Lcom/aig/aigone/model/entity/aigone/ConsultationTypeEnum;' @76  a 'com/aig/aigone/model/entity/aigone/ConsultationTypeEnum'{0x000000070626a9c0} (0xe0c4d538)
 - private 'visitId' 'Ljava/lang/Integer;' @80  a 'java/lang/Integer'{0x0000000713655e40} = 768467 (0xe26cabc8)
 - private 'hospCode' 'Ljava/lang/String;' @84  "1100"{0x0000000713655e68} (0xe26cabcd)
RCX=0x0000005c842f98c8 is pointing into the stack for thread: 0x000001df985f3bb0
RDX=0x0000000000000003 is an unknown value
RSP=0x0000005c842f98b8 is pointing into the stack for thread: 0x000001df985f3bb0
RBP=0x0000005c842f9910 is pointing into the stack for thread: 0x000001df985f3bb0
RSI=0x000001df94c81548 points into unknown readable memory: 0x80000bc9020b285e | 5e 28 0b 02 c9 0b 00 80
RDI=0x000001df9e22df40 points into unknown readable memory: 0x0000002000000070 | 70 00 00 00 20 00 00 00
R8 =0x0000000000000004 is an unknown value
R9 =0x0000000000000004 is an unknown value
R10=0x000001df9e72b5a3 is an unknown value
R11=0x0 is null
R12=0x0 is null
R13=0x0000005c842f98b8 is pointing into the stack for thread: 0x000001df985f3bb0
R14=0x0000005c842f9938 is pointing into the stack for thread: 0x000001df985f3bb0
R15=0x000001df985f3bb0 is a thread

Top of Stack: (sp=0x0000005c842f98b8)
0x0000005c842f98b8:   000001df433e9086 000000070b318a30
0x0000005c842f98c8:   0000005c842f98c8 000001df4f72b5a3
0x0000005c842f98d8:   0000000000000005 000001df4f0b2128
0x0000005c842f98e8:   000001df445669f6 00000007059ed5f0
0x0000005c842f98f8:   000001df4f0b4c20 0000005c842f98b8
0x0000005c842f9908:   0000005c842f9940 0000005c842f9ca8
0x0000005c842f9918:   000001df43d4f4bf 000000070d63b0a0
0x0000005c842f9928:   0000000713655a70 000000070b318a30
0x0000005c842f9938:   000000070d63b038 000000070662ea70
0x0000005c842f9948:   000001df437d7c1e 000000070d452290
0x0000005c842f9958:   000000070662ea70 000000070d63b008
0x0000005c842f9968:   000000070b318a30 000000070d63b150
0x0000005c842f9978:   0000000713655a70 0000000713655a70
0x0000005c842f9988:   000000070d63b128 000000070d63b038
0x0000005c842f9998:   000000070b318a30 000001df43d4f3d4
0x0000005c842f99a8:   0000000000000001 0000005c842f9ca8
0x0000005c842f99b8:   000001df443b71b2 000000070d452290
0x0000005c842f99c8:   0000000705f8ab08 000001df985f3bb0
0x0000005c842f99d8:   0000000000000000 000001df4497d6a0
0x0000005c842f99e8:   000001df50ae0430 000001df00000000
0x0000005c842f99f8:   0000000000000000 0000005c842f9ca8
0x0000005c842f9a08:   000001df438761cc 00002d1fcd14e569
0x0000005c842f9a18:   000001df44566890 000001df38c9e7b0
0x0000005c842f9a28:   0000000000000c00 0000000000000000
0x0000005c842f9a38:   0000000000000000 0000000000000000
0x0000005c842f9a48:   0000000000000000 0000000000000000
0x0000005c842f9a58:   0000000000000000 0000000000000000
0x0000005c842f9a68:   0000000000000000 000000070d63afe8
0x0000005c842f9a78:   0000000713657b78 000000070b318a30
0x0000005c842f9a88:   000000070b931e38 0000000705f8ab08
0x0000005c842f9a98:   000000070662ea70 000000070662ea70
0x0000005c842f9aa8:   000001df00000001 000000070d452290 

Instructions: (pc=0x000001df43410fa4)
0x000001df43410ea4:   df 01 00 00 00 0f 41 43 df 01 00 00 80 1f 41 43
0x000001df43410eb4:   df 01 00 00 f0 0e 41 43 df 01 00 00 f0 0e 41 43
0x000001df43410ec4:   df 01 00 00 00 00 00 00 00 00 00 00 00 bf 6f 52
0x000001df43410ed4:   fd 7f 00 00 f0 10 00 00 60 00 00 00 ff ff ff ff
0x000001df43410ee4:   f0 10 00 00 00 00 00 00 00 00 00 00 00 00 00 00
0x000001df43410ef4:   00 00 00 00 00 00 00 00 00 00 00 00 f4 66 66 66
0x000001df43410f04:   0f 1f 84 00 00 00 00 00 66 66 66 90 66 66 66 0f
0x000001df43410f14:   1f 84 00 00 00 00 00 66 66 66 90 90 48 8b 53 08
0x000001df43410f24:   0f b7 52 2e 48 8b 0c d4 8b 59 14 48 c1 e3 03 8b
0x000001df43410f34:   5b 28 48 c1 e3 03 8b 5b 24 48 c1 e3 03 48 8b 5b
0x000001df43410f44:   10 48 85 db 0f 84 10 00 00 00 41 80 bf e0 05 00
0x000001df43410f54:   00 00 74 03 ff 63 38 ff 63 50 e9 9d 16 00 00 66
0x000001df43410f64:   66 66 0f 1f 84 00 00 00 00 00 66 66 66 90 66 66
0x000001df43410f74:   0f 1f 84 00 00 00 00 00 66 66 66 90 48 8b 53 08
0x000001df43410f84:   0f b7 52 2e 48 8b 0c d4 58 5b 50 44 8b 51 08 49
0x000001df43410f94:   bb 00 00 00 4f df 01 00 00 4d 03 d3 4c 8b 5b 10
0x000001df43410fa4:   4b 8b 9c da c8 01 00 00 48 85 db 0f 84 10 00 00
0x000001df43410fb4:   00 41 80 bf e0 05 00 00 00 74 03 ff 63 38 ff 63
0x000001df43410fc4:   50 e9 36 16 00 00 66 66 66 0f 1f 84 00 00 00 00
0x000001df43410fd4:   00 66 66 66 90 0f 1f 80 00 00 00 00 58 5b 50 8b
0x000001df43410fe4:   5b 24 48 c1 e3 03 48 8b 5b 10 48 85 db 0f 84 10
0x000001df43410ff4:   00 00 00 41 80 bf e0 05 00 00 00 74 03 ff 63 38
0x000001df43411004:   ff 63 50 e9 f4 15 00 00 66 66 66 0f 1f 84 00 00
0x000001df43411014:   00 00 00 66 66 66 90 0f 1f 44 00 00 48 8b 53 08
0x000001df43411024:   0f b7 52 2e 48 8b 0c d4 58 5b 50 48 3b 01 8b 5b
0x000001df43411034:   24 48 c1 e3 03 48 8b 5b 10 48 85 db 0f 84 10 00
0x000001df43411044:   00 00 41 80 bf e0 05 00 00 00 74 03 ff 63 38 ff
0x000001df43411054:   63 50 e9 a5 15 00 00 0f 1f 44 00 00 48 8b 53 08
0x000001df43411064:   0f b7 52 2e 48 8b 0c d4 58 5b 50 44 8b 51 08 49
0x000001df43411074:   bb 00 00 00 4f df 01 00 00 4d 03 d3 8b 43 18 48
0x000001df43411084:   c1 e0 03 48 8b 40 10 48 8b 5b 10 45 8b 9a a0 00
0x000001df43411094:   00 00 4f 8d 9c da c8 01 00 00 4d 8d 14 da 49 8b 


Stack slot to memory mapping:

stack at sp + 0 slots: 0x000001df433e9086 is at code_begin+3078 in an Interpreter codelet
return entry points  [0x000001df433e8480, 0x000001df433ea298]  7704 bytes
stack at sp + 1 slots: 0x000000070b318a30 is an oop: com.aig.aigone.service.impl.EmrServiceImpl 
{0x000000070b318a30} - klass: 'com/aig/aigone/service/impl/EmrServiceImpl'
 - ---- fields (total size 8 words):
 - private 'emrDbRepo' 'Lcom/aig/aigone/repository/emr/EmrDbRepository;' @12  a 'com/aig/aigone/repository/emr/EmrDbRepository$$SpringCGLIB$$0'{0x000000070a74ec80} (0xe14e9d90)
 - private 'metPatientRepo' 'Lcom/aig/aigone/repository/aigone/METPatientRepository;' @16  a 'jdk/proxy2/$Proxy277'{0x000000070b0ec928} (0xe161d925)
 - private 'aigOneMapper' 'Lcom/aig/aigone/mapper/AigOneMapper;' @20  a 'com/aig/aigone/mapper/AigOneMapperImpl'{0x00000007080326d8} (0xe10064db)
 - private 'emrClient' 'Lorg/springframework/web/reactive/function/client/WebClient;' @24  a 'org/springframework/web/reactive/function/client/DefaultWebClient'{0x000000070a750408} (0xe14ea081)
 - private 'systemRepo' 'Lcom/aig/aigone/repository/aigone/SystemSettingsRepository;' @28  a 'jdk/proxy2/$Proxy209'{0x0000000708003b70} (0xe100076e)
 - private 'opConsultationRepo' 'Lcom/aig/aigone/repository/aigone/OpConsultationRepository;' @32  a 'jdk/proxy2/$Proxy278'{0x000000070b1e5490} (0xe163ca92)
 - private 'opConsultationLogRepo' 'Lcom/aig/aigone/repository/aigone/OpConsultationLogRepository;' @36  a 'jdk/proxy2/$Proxy279'{0x000000070b1d89f0} (0xe163b13e)
 - private 'hisDbRepo' 'Lcom/aig/aigone/repository/his/HisDbRepository;' @40  a 'com/aig/aigone/repository/his/HisDbRepository$$SpringCGLIB$$0'{0x0000000709b97e68} (0xe1372fcd)
 - private 'isaveRepo' 'Lcom/aig/aigone/repository/aigone/ISaveBedsRepository;' @44  a 'jdk/proxy2/$Proxy280'{0x00000007077051b8} (0xe0ee0a37)
 - private 'hisService' 'Lcom/aig/aigone/service/HisService;' @48  a 'com/aig/aigone/service/impl/HisServiceImpl$$SpringCGLIB$$0'{0x000000070723f1d8} (0xe0e47e3b)
 - private 'mdtRepo' 'Lcom/aig/aigone/repository/aigone/MdtRepository;' @52  a 'jdk/proxy2/$Proxy259'{0x0000000708affad0} (0xe115ff5a)
 - private 'userRepo' 'Lcom/aig/aigone/repository/aigone/UserRepository;' @56  a 'jdk/proxy2/$Proxy197'{0x00000007095d66f0} (0xe12bacde)
 - private 'docTempRepo' 'Lcom/aig/aigone/repository/aigone/DoctorTemplateRepository;' @60  a 'jdk/proxy2/$Proxy281'{0x000000070b08e9e8} (0xe1611d3d)
stack at sp + 2 slots: 0x0000005c842f98c8 is pointing into the stack for thread: 0x000001df985f3bb0
stack at sp + 3 slots: 0x000001df4f72b5a3 is pointing into metadata
stack at sp + 4 slots: 0x0000000000000005 is an unknown value
stack at sp + 5 slots: 0x000001df4f0b2128 is pointing into metadata
stack at sp + 6 slots: 0x000001df445669f6 is at entry_point+-138 in (nmethod*)0x000001df44566890
Compiled method (c1)  308979 16293       1       org.hibernate.sql.results.internal.SqlSelectionImpl::resolve (51 bytes)
 total in heap  [0x000001df44566890,0x000001df445671a8] = 2328
 relocation     [0x000001df445669f0,0x000001df44566a78] = 136
 main code      [0x000001df44566a80,0x000001df44566e38] = 952
 stub code      [0x000001df44566e38,0x000001df44566ea0] = 104
 oops           [0x000001df44566ea0,0x000001df44566ea8] = 8
 metadata       [0x000001df44566ea8,0x000001df44566f58] = 176
 scopes data    [0x000001df44566f58,0x000001df44567018] = 192
 scopes pcs     [0x000001df44567018,0x000001df44567138] = 288
 dependencies   [0x000001df44567138,0x000001df44567178] = 64
 nul chk table  [0x000001df44567178,0x000001df445671a8] = 48
stack at sp + 7 slots: 0x00000007059ed5f0 is an oop: java.lang.Class 
{0x00000007059ed5f0} - klass: 'java/lang/Class'
 - ---- fields (total size 14 words):
 - private volatile transient 'classRedefinedCount' 'I' @12  0 (0x00000000)
 - injected 'klass' 'J' @16  2058615463160 (0x000001df4f0b1cf8)
 - injected 'array_klass' 'J' @24  0 (0x0000000000000000)
 - injected 'oop_size' 'I' @32  14 (0x0000000e)
 - injected 'static_oop_field_count' 'I' @36  0 (0x00000000)
 - private volatile transient 'cachedConstructor' 'Ljava/lang/reflect/Constructor;' @40  null (0x00000000)
 - private transient 'name' 'Ljava/lang/String;' @44  null (0x00000000)
 - private transient 'module' 'Ljava/lang/Module;' @48  a 'java/lang/Module'{0x0000000705a1a3a0} (0xe0b43474)
 - private final 'classLoader' 'Ljava/lang/ClassLoader;' @52  null (0x00000000)
 - private transient 'classData' 'Ljava/lang/Object;' @56  null (0x00000000)
 - private transient 'packageName' 'Ljava/lang/String;' @60  null (0x00000000)
 - private final 'componentType' 'Ljava/lang/Class;' @64  null (0x00000000)
 - private volatile transient 'reflectionData' 'Ljava/lang/ref/SoftReference;' @68  null (0x00000000)
 - private volatile transient 'genericInfo' 'Lsun/reflect/generics/repository/ClassRepository;' @72  null (0x00000000)
 - private volatile transient 'enumConstants' '[Ljava/lang/Object;' @76  null (0x00000000)
 - private volatile transient 'enumConstantDirectory' 'Ljava/util/Map;' @80  null (0x00000000)
 - private volatile transient 'annotationData' 'Ljava/lang/Class$AnnotationData;' @84  null (0x00000000)
 - private volatile transient 'annotationType' 'Lsun/reflect/annotation/AnnotationType;' @88  null (0x00000000)
 - transient 'classValueMap' 'Ljava/lang/ClassValue$ClassValueMap;' @92  null (0x00000000)
 - injected 'protection_domain' 'Ljava/lang/Object;' @96  null (0x00000000)
 - injected 'signers_name' 'Ljava/lang/Object;' @100  null (0x00000000)
 - injected 'source_file' 'Ljava/lang/Object;' @104  null (0x00000000)
 - signature: Ljava/lang/invoke/DirectMethodHandle$Holder;
 - ---- static fields (0):

MethodHandle::interpreter_entry::_linkToVirtual [0x000001df43410f63, 0x000001df43410fca] (103 bytes)
[MachCode]
  0x000001df43410f63: 6666 660f | 1f84 0000 | 0000 0066 | 6666 9066 | 660f 1f84 | 0000 0000 | 0066 6666 | 9048 8b53 
  0x000001df43410f83: 080f b752 | 2e48 8b0c | d458 5b50 | 448b 5108 | 49bb 0000 | 004f df01 | 0000 4d03 | d34c 8b5b 
  0x000001df43410fa3: 104b 8b9c | dac8 0100 | 0048 85db | 0f84 1000 | 0000 4180 | bfe0 0500 | 0000 7403 | ff63 38ff 
  0x000001df43410fc3: 6350 e936 | 
[/MachCode]

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001df9d7c7ad0, length=63, elements={
0x000001df4e40ddd0, 0x000001df4e40eaf0, 0x000001df4e412220, 0x000001df4e412c80,
0x000001df4e414100, 0x000001df4e415910, 0x000001df4e41a620, 0x000001df4e3d0ec0,
0x000001df4ec549d0, 0x000001df4ec5d600, 0x000001df4ec667e0, 0x000001df94c1c730,
0x000001df94e476a0, 0x000001df94e6b1e0, 0x000001df94ec9200, 0x000001df953aff90,
0x000001df953d62d0, 0x000001df953f3e10, 0x000001df9705fa20, 0x000001df97061460,
0x000001df9705f390, 0x000001df97060dd0, 0x000001df970600b0, 0x000001df985f2e90,
0x000001df985f48d0, 0x000001df985f4240, 0x000001df985f55f0, 0x000001df985f6310,
0x000001df985f5c80, 0x000001df9d6c05d0, 0x000001df9d6bbda0, 0x000001df9d6c1980,
0x000001df9d6beb90, 0x000001df9d6bff40, 0x000001df9d6c0c60, 0x000001df9d6be500,
0x000001df9d6bf8b0, 0x000001df9d6bd7e0, 0x000001df9d6c2010, 0x000001df9d6bf220,
0x000001df9d6c12f0, 0x000001df9d6bb080, 0x000001df9d6c26a0, 0x000001df9d6bb710,
0x000001df9d6bc430, 0x000001df9d6bde70, 0x000001df9d804f20, 0x000001df9d802e50,
0x000001df9d807680, 0x000001df985f3520, 0x000001df985f3bb0, 0x000001df985f4f60,
0x000001df97060740, 0x000001df9d6bcac0, 0x000001df9d50d210, 0x000001df9d513480,
0x000001df9d50f970, 0x000001df9d50df30, 0x000001df9d50e5c0, 0x000001df9d511a40,
0x000001df9d5120d0, 0x000001df9d513b10, 0x000001df9d510690
}

Java Threads: ( => current thread )
  0x000001df4e40ddd0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=28496, stack(0x0000005cff900000,0x0000005cffa00000) (1024K)]
  0x000001df4e40eaf0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=27392, stack(0x0000005cffa00000,0x0000005cffb00000) (1024K)]
  0x000001df4e412220 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=11200, stack(0x0000005cffb00000,0x0000005cffc00000) (1024K)]
  0x000001df4e412c80 JavaThread "Attach Listener"            daemon [_thread_blocked, id=22096, stack(0x0000005cffc00000,0x0000005cffd00000) (1024K)]
  0x000001df4e414100 JavaThread "Service Thread"             daemon [_thread_blocked, id=18528, stack(0x0000005cffd00000,0x0000005cffe00000) (1024K)]
  0x000001df4e415910 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=28404, stack(0x0000005cffe00000,0x0000005cfff00000) (1024K)]
  0x000001df4e41a620 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=25840, stack(0x0000005cfff00000,0x0000005d00000000) (1024K)]
  0x000001df4e3d0ec0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=12824, stack(0x0000005c80100000,0x0000005c80200000) (1024K)]
  0x000001df4ec549d0 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_blocked, id=3976, stack(0x0000005c80400000,0x0000005c80500000) (1024K)]
  0x000001df4ec5d600 JavaThread "JDWP Event Helper Thread"   daemon [_thread_blocked, id=17084, stack(0x0000005c80500000,0x0000005c80600000) (1024K)]
  0x000001df4ec667e0 JavaThread "JDWP Command Reader"        daemon [_thread_in_native, id=24168, stack(0x0000005c80600000,0x0000005c80700000) (1024K)]
  0x000001df94c1c730 JavaThread "Notification Thread"        daemon [_thread_blocked, id=7728, stack(0x0000005c80700000,0x0000005c80800000) (1024K)]
  0x000001df94e476a0 JavaThread "RMI TCP Accept-0"           daemon [_thread_in_native, id=27244, stack(0x0000005c80e00000,0x0000005c80f00000) (1024K)]
  0x000001df94e6b1e0 JavaThread "RMI TCP Accept-49882"       daemon [_thread_in_native, id=27100, stack(0x0000005c80f00000,0x0000005c81000000) (1024K)]
  0x000001df94ec9200 JavaThread "RMI TCP Accept-0"           daemon [_thread_in_native, id=26648, stack(0x0000005c81000000,0x0000005c81100000) (1024K)]
  0x000001df953aff90 JavaThread "RMI Scheduler(0)"           daemon [_thread_blocked, id=22012, stack(0x0000005c81600000,0x0000005c81700000) (1024K)]
  0x000001df953d62d0 JavaThread "RMI TCP Connection(8)-127.0.0.1" daemon [_thread_in_native, id=3796, stack(0x0000005c81700000,0x0000005c81800000) (1024K)]
  0x000001df953f3e10 JavaThread "JMX server connection timeout 49" daemon [_thread_blocked, id=24464, stack(0x0000005c81800000,0x0000005c81900000) (1024K)]
  0x000001df9705fa20 JavaThread "Jndi-Dns-address-change-listener" daemon [_thread_in_native, id=11808, stack(0x0000005c81e00000,0x0000005c81f00000) (1024K)]
  0x000001df97061460 JavaThread "lettuce-timer-3-1"          daemon [_thread_blocked, id=24192, stack(0x0000005c82500000,0x0000005c82600000) (1024K)]
  0x000001df9705f390 JavaThread "PostgreSQL-JDBC-Cleaner"    daemon [_thread_blocked, id=28648, stack(0x0000005c82700000,0x0000005c82800000) (1024K)]
  0x000001df97060dd0 JavaThread "HikariPool-1 housekeeper"   daemon [_thread_blocked, id=24740, stack(0x0000005c82800000,0x0000005c82900000) (1024K)]
  0x000001df970600b0 JavaThread "HikariPool-2 housekeeper"   daemon [_thread_blocked, id=25296, stack(0x0000005c81f00000,0x0000005c82000000) (1024K)]
  0x000001df985f2e90 JavaThread "mssql-jdbc-shared-timer-core-0" daemon [_thread_blocked, id=1072, stack(0x0000005c82100000,0x0000005c82200000) (1024K)]
  0x000001df985f48d0 JavaThread "HikariPool-3 housekeeper"   daemon [_thread_blocked, id=22276, stack(0x0000005c82200000,0x0000005c82300000) (1024K)]
  0x000001df985f4240 JavaThread "HikariPool-4 housekeeper"   daemon [_thread_blocked, id=16792, stack(0x0000005c82900000,0x0000005c82a00000) (1024K)]
  0x000001df985f55f0 JavaThread "Catalina-utility-1"                [_thread_blocked, id=18360, stack(0x0000005c82c00000,0x0000005c82d00000) (1024K)]
  0x000001df985f6310 JavaThread "Catalina-utility-2"                [_thread_blocked, id=5264, stack(0x0000005c82d00000,0x0000005c82e00000) (1024K)]
  0x000001df985f5c80 JavaThread "container-0"                       [_thread_blocked, id=16016, stack(0x0000005c82e00000,0x0000005c82f00000) (1024K)]
  0x000001df9d6c05d0 JavaThread "reactor-http-nio-1"         daemon [_thread_in_native, id=26760, stack(0x0000005c83000000,0x0000005c83100000) (1024K)]
  0x000001df9d6bbda0 JavaThread "reactor-http-nio-2"         daemon [_thread_in_native, id=19468, stack(0x0000005c83100000,0x0000005c83200000) (1024K)]
  0x000001df9d6c1980 JavaThread "reactor-http-nio-3"         daemon [_thread_in_native, id=26780, stack(0x0000005c83200000,0x0000005c83300000) (1024K)]
  0x000001df9d6beb90 JavaThread "reactor-http-nio-4"         daemon [_thread_in_native, id=11300, stack(0x0000005c83300000,0x0000005c83400000) (1024K)]
  0x000001df9d6bff40 JavaThread "reactor-http-nio-5"         daemon [_thread_in_native, id=24324, stack(0x0000005c83400000,0x0000005c83500000) (1024K)]
  0x000001df9d6c0c60 JavaThread "reactor-http-nio-6"         daemon [_thread_in_native, id=19704, stack(0x0000005c83500000,0x0000005c83600000) (1024K)]
  0x000001df9d6be500 JavaThread "reactor-http-nio-7"         daemon [_thread_in_native, id=21108, stack(0x0000005c83600000,0x0000005c83700000) (1024K)]
  0x000001df9d6bf8b0 JavaThread "reactor-http-nio-8"         daemon [_thread_in_native, id=18936, stack(0x0000005c83700000,0x0000005c83800000) (1024K)]
  0x000001df9d6bd7e0 JavaThread "reactor-http-nio-9"         daemon [_thread_in_native, id=23968, stack(0x0000005c83800000,0x0000005c83900000) (1024K)]
  0x000001df9d6c2010 JavaThread "reactor-http-nio-10"        daemon [_thread_in_native, id=15196, stack(0x0000005c83900000,0x0000005c83a00000) (1024K)]
  0x000001df9d6bf220 JavaThread "reactor-http-nio-11"        daemon [_thread_in_native, id=24532, stack(0x0000005c83a00000,0x0000005c83b00000) (1024K)]
  0x000001df9d6c12f0 JavaThread "reactor-http-nio-12"        daemon [_thread_in_native, id=21248, stack(0x0000005c83b00000,0x0000005c83c00000) (1024K)]
  0x000001df9d6bb080 JavaThread "reactor-http-nio-13"        daemon [_thread_in_native, id=24116, stack(0x0000005c83c00000,0x0000005c83d00000) (1024K)]
  0x000001df9d6c26a0 JavaThread "reactor-http-nio-14"        daemon [_thread_in_native, id=26336, stack(0x0000005c83d00000,0x0000005c83e00000) (1024K)]
  0x000001df9d6bb710 JavaThread "reactor-http-nio-15"        daemon [_thread_in_native, id=20140, stack(0x0000005c83e00000,0x0000005c83f00000) (1024K)]
  0x000001df9d6bc430 JavaThread "reactor-http-nio-16"        daemon [_thread_in_native, id=21372, stack(0x0000005c83f00000,0x0000005c84000000) (1024K)]
  0x000001df9d6bde70 JavaThread "boundedElastic-evictor-1"   daemon [_thread_blocked, id=23116, stack(0x0000005c84000000,0x0000005c84100000) (1024K)]
  0x000001df9d804f20 JavaThread "scheduling-1"                      [_thread_blocked, id=24816, stack(0x0000005c82000000,0x0000005c82100000) (1024K)]
  0x000001df9d802e50 JavaThread "http-nio-8080-exec-1"       daemon [_thread_blocked, id=25156, stack(0x0000005c82300000,0x0000005c82400000) (1024K)]
  0x000001df9d807680 JavaThread "http-nio-8080-exec-2"       daemon [_thread_blocked, id=27184, stack(0x0000005c82a00000,0x0000005c82b00000) (1024K)]
  0x000001df985f3520 JavaThread "http-nio-8080-exec-3"       daemon [_thread_blocked, id=11512, stack(0x0000005c84100000,0x0000005c84200000) (1024K)]
=>0x000001df985f3bb0 JavaThread "http-nio-8080-exec-4"       daemon [_thread_in_Java, id=22496, stack(0x0000005c84200000,0x0000005c84300000) (1024K)]
  0x000001df985f4f60 JavaThread "http-nio-8080-exec-5"       daemon [_thread_blocked, id=27996, stack(0x0000005c84300000,0x0000005c84400000) (1024K)]
  0x000001df97060740 JavaThread "http-nio-8080-exec-6"       daemon [_thread_blocked, id=26144, stack(0x0000005c84400000,0x0000005c84500000) (1024K)]
  0x000001df9d6bcac0 JavaThread "http-nio-8080-exec-7"       daemon [_thread_blocked, id=22492, stack(0x0000005c84500000,0x0000005c84600000) (1024K)]
  0x000001df9d50d210 JavaThread "http-nio-8080-exec-8"       daemon [_thread_blocked, id=24416, stack(0x0000005c84600000,0x0000005c84700000) (1024K)]
  0x000001df9d513480 JavaThread "http-nio-8080-exec-9"       daemon [_thread_blocked, id=7228, stack(0x0000005c84700000,0x0000005c84800000) (1024K)]
  0x000001df9d50f970 JavaThread "http-nio-8080-exec-10"      daemon [_thread_blocked, id=27348, stack(0x0000005c84800000,0x0000005c84900000) (1024K)]
  0x000001df9d50df30 JavaThread "http-nio-8080-Poller"       daemon [_thread_in_native, id=22860, stack(0x0000005c84900000,0x0000005c84a00000) (1024K)]
  0x000001df9d50e5c0 JavaThread "http-nio-8080-Acceptor"     daemon [_thread_in_native, id=25536, stack(0x0000005c84a00000,0x0000005c84b00000) (1024K)]
  0x000001df9d511a40 JavaThread "DestroyJavaVM"                     [_thread_blocked, id=22480, stack(0x0000005cff100000,0x0000005cff200000) (1024K)]
  0x000001df9d5120d0 JavaThread "RMI TCP Connection(4)-127.0.0.1" daemon [_thread_in_native, id=25852, stack(0x0000005c84b00000,0x0000005c84c00000) (1024K)]
  0x000001df9d513b10 JavaThread "JMX server connection timeout 104" daemon [_thread_blocked, id=26572, stack(0x0000005c84c00000,0x0000005c84d00000) (1024K)]
  0x000001df9d510690 JavaThread "lettuce-nioEventLoop-4-1"   daemon [_thread_in_native, id=27080, stack(0x0000005c81b00000,0x0000005c81c00000) (1024K)]
Total: 63

Other Threads:
  0x000001df4e3eb4b0 VMThread "VM Thread"                           [id=27836, stack(0x0000005cff800000,0x0000005cff900000) (1024K)]
  0x000001df4e3cf5e0 WatcherThread "VM Periodic Task Thread"        [id=27380, stack(0x0000005cff700000,0x0000005cff800000) (1024K)]
  0x000001df3affad60 WorkerThread "GC Thread#0"                     [id=9668, stack(0x0000005cff200000,0x0000005cff300000) (1024K)]
  0x000001df94c8a000 WorkerThread "GC Thread#1"                     [id=26120, stack(0x0000005c80800000,0x0000005c80900000) (1024K)]
  0x000001df94c8a3a0 WorkerThread "GC Thread#2"                     [id=1844, stack(0x0000005c80900000,0x0000005c80a00000) (1024K)]
  0x000001df94c8a950 WorkerThread "GC Thread#3"                     [id=13996, stack(0x0000005c80a00000,0x0000005c80b00000) (1024K)]
  0x000001df94c8f160 WorkerThread "GC Thread#4"                     [id=27756, stack(0x0000005c80b00000,0x0000005c80c00000) (1024K)]
  0x000001df94c8ffe0 WorkerThread "GC Thread#5"                     [id=20804, stack(0x0000005c80c00000,0x0000005c80d00000) (1024K)]
  0x000001df94c90380 WorkerThread "GC Thread#6"                     [id=14176, stack(0x0000005c80d00000,0x0000005c80e00000) (1024K)]
  0x000001df94c90ac0 WorkerThread "GC Thread#7"                     [id=21136, stack(0x0000005c81100000,0x0000005c81200000) (1024K)]
  0x000001df94c8f500 WorkerThread "GC Thread#8"                     [id=28384, stack(0x0000005c81200000,0x0000005c81300000) (1024K)]
  0x000001df94c8f8a0 WorkerThread "GC Thread#9"                     [id=10332, stack(0x0000005c81300000,0x0000005c81400000) (1024K)]
  0x000001df94c8fc40 WorkerThread "GC Thread#10"                    [id=26872, stack(0x0000005c81400000,0x0000005c81500000) (1024K)]
  0x000001df950cf4b0 WorkerThread "GC Thread#11"                    [id=22984, stack(0x0000005c81900000,0x0000005c81a00000) (1024K)]
  0x000001df950d2b10 WorkerThread "GC Thread#12"                    [id=23144, stack(0x0000005c81a00000,0x0000005c81b00000) (1024K)]
  0x000001df3b00bfa0 ConcurrentGCThread "G1 Main Marker"            [id=23160, stack(0x0000005cff300000,0x0000005cff400000) (1024K)]
  0x000001df3b00c8b0 WorkerThread "G1 Conc#0"                       [id=27360, stack(0x0000005cff400000,0x0000005cff500000) (1024K)]
  0x000001df950cf110 WorkerThread "G1 Conc#1"                       [id=22940, stack(0x0000005c81c00000,0x0000005c81d00000) (1024K)]
  0x000001df950cf850 WorkerThread "G1 Conc#2"                       [id=19168, stack(0x0000005c81d00000,0x0000005c81e00000) (1024K)]
  0x000001df4e2a40d0 ConcurrentGCThread "G1 Refine#0"               [id=20484, stack(0x0000005cff500000,0x0000005cff600000) (1024K)]
  0x000001df4e2a4a50 ConcurrentGCThread "G1 Service"                [id=12244, stack(0x0000005cff600000,0x0000005cff700000) (1024K)]
Total: 21

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000705000000, size: 4016 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x000001df4f000000-0x000001df4fc90000-0x000001df4fc90000), size 13172736, SharedBaseAddress: 0x000001df4f000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001df50000000-0x000001df90000000, reserved size: 1073741824
Narrow klass base: 0x000001df4f000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096
 CPUs: 16 total, 16 available
 Memory: 16059M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 252M
 Heap Max Capacity: 4016M
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 245760K, used 133298K [0x0000000705000000, 0x0000000800000000)
  region size 2048K, 11 young (22528K), 5 survivors (10240K)
 Metaspace       used 125551K, committed 126656K, reserved 1179648K
  class space    used 17286K, committed 17792K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000705000000, 0x0000000705200000, 0x0000000705200000|100%| O|  |TAMS 0x0000000705000000| PB 0x0000000705000000| Untracked 
|   1|0x0000000705200000, 0x0000000705400000, 0x0000000705400000|100%| O|  |TAMS 0x0000000705200000| PB 0x0000000705200000| Untracked 
|   2|0x0000000705400000, 0x0000000705600000, 0x0000000705600000|100%| O|  |TAMS 0x0000000705400000| PB 0x0000000705400000| Untracked 
|   3|0x0000000705600000, 0x0000000705800000, 0x0000000705800000|100%| O|  |TAMS 0x0000000705600000| PB 0x0000000705600000| Untracked 
|   4|0x0000000705800000, 0x0000000705a00000, 0x0000000705a00000|100%| O|  |TAMS 0x0000000705800000| PB 0x0000000705800000| Untracked 
|   5|0x0000000705a00000, 0x0000000705c00000, 0x0000000705c00000|100%| O|  |TAMS 0x0000000705a00000| PB 0x0000000705a00000| Untracked 
|   6|0x0000000705c00000, 0x0000000705e00000, 0x0000000705e00000|100%| O|  |TAMS 0x0000000705c00000| PB 0x0000000705c00000| Untracked 
|   7|0x0000000705e00000, 0x0000000706000000, 0x0000000706000000|100%| O|  |TAMS 0x0000000705e00000| PB 0x0000000705e00000| Untracked 
|   8|0x0000000706000000, 0x0000000706200000, 0x0000000706200000|100%| O|  |TAMS 0x0000000706000000| PB 0x0000000706000000| Untracked 
|   9|0x0000000706200000, 0x0000000706400000, 0x0000000706400000|100%| O|  |TAMS 0x0000000706200000| PB 0x0000000706200000| Untracked 
|  10|0x0000000706400000, 0x0000000706600000, 0x0000000706600000|100%| O|  |TAMS 0x0000000706400000| PB 0x0000000706400000| Untracked 
|  11|0x0000000706600000, 0x0000000706800000, 0x0000000706800000|100%| O|  |TAMS 0x0000000706600000| PB 0x0000000706600000| Untracked 
|  12|0x0000000706800000, 0x0000000706a00000, 0x0000000706a00000|100%| O|  |TAMS 0x0000000706800000| PB 0x0000000706800000| Untracked 
|  13|0x0000000706a00000, 0x0000000706c00000, 0x0000000706c00000|100%| O|  |TAMS 0x0000000706a00000| PB 0x0000000706a00000| Untracked 
|  14|0x0000000706c00000, 0x0000000706e00000, 0x0000000706e00000|100%| O|  |TAMS 0x0000000706c00000| PB 0x0000000706c00000| Untracked 
|  15|0x0000000706e00000, 0x0000000707000000, 0x0000000707000000|100%| O|  |TAMS 0x0000000706e00000| PB 0x0000000706e00000| Untracked 
|  16|0x0000000707000000, 0x0000000707200000, 0x0000000707200000|100%| O|  |TAMS 0x0000000707000000| PB 0x0000000707000000| Untracked 
|  17|0x0000000707200000, 0x0000000707400000, 0x0000000707400000|100%| O|  |TAMS 0x0000000707200000| PB 0x0000000707200000| Untracked 
|  18|0x0000000707400000, 0x0000000707600000, 0x0000000707600000|100%| O|  |TAMS 0x0000000707400000| PB 0x0000000707400000| Untracked 
|  19|0x0000000707600000, 0x0000000707800000, 0x0000000707800000|100%| O|  |TAMS 0x0000000707600000| PB 0x0000000707600000| Untracked 
|  20|0x0000000707800000, 0x0000000707a00000, 0x0000000707a00000|100%| O|  |TAMS 0x0000000707800000| PB 0x0000000707800000| Untracked 
|  21|0x0000000707a00000, 0x0000000707c00000, 0x0000000707c00000|100%| O|  |TAMS 0x0000000707a00000| PB 0x0000000707a00000| Untracked 
|  22|0x0000000707c00000, 0x0000000707e00000, 0x0000000707e00000|100%| O|  |TAMS 0x0000000707c00000| PB 0x0000000707c00000| Untracked 
|  23|0x0000000707e00000, 0x0000000708000000, 0x0000000708000000|100%| O|  |TAMS 0x0000000707e00000| PB 0x0000000707e00000| Untracked 
|  24|0x0000000708000000, 0x0000000708200000, 0x0000000708200000|100%| O|  |TAMS 0x0000000708000000| PB 0x0000000708000000| Untracked 
|  25|0x0000000708200000, 0x0000000708400000, 0x0000000708400000|100%| O|  |TAMS 0x0000000708200000| PB 0x0000000708200000| Untracked 
|  26|0x0000000708400000, 0x0000000708600000, 0x0000000708600000|100%| O|  |TAMS 0x0000000708400000| PB 0x0000000708400000| Untracked 
|  27|0x0000000708600000, 0x0000000708800000, 0x0000000708800000|100%| O|  |TAMS 0x0000000708600000| PB 0x0000000708600000| Untracked 
|  28|0x0000000708800000, 0x0000000708a00000, 0x0000000708a00000|100%| O|  |TAMS 0x0000000708800000| PB 0x0000000708800000| Untracked 
|  29|0x0000000708a00000, 0x0000000708c00000, 0x0000000708c00000|100%| O|  |TAMS 0x0000000708a00000| PB 0x0000000708a00000| Untracked 
|  30|0x0000000708c00000, 0x0000000708e00000, 0x0000000708e00000|100%| O|  |TAMS 0x0000000708c00000| PB 0x0000000708c00000| Untracked 
|  31|0x0000000708e00000, 0x0000000709000000, 0x0000000709000000|100%| O|  |TAMS 0x0000000708e00000| PB 0x0000000708e00000| Untracked 
|  32|0x0000000709000000, 0x0000000709200000, 0x0000000709200000|100%| O|  |TAMS 0x0000000709000000| PB 0x0000000709000000| Untracked 
|  33|0x0000000709200000, 0x0000000709400000, 0x0000000709400000|100%| O|  |TAMS 0x0000000709200000| PB 0x0000000709200000| Untracked 
|  34|0x0000000709400000, 0x0000000709600000, 0x0000000709600000|100%| O|  |TAMS 0x0000000709400000| PB 0x0000000709400000| Untracked 
|  35|0x0000000709600000, 0x0000000709800000, 0x0000000709800000|100%| O|  |TAMS 0x0000000709600000| PB 0x0000000709600000| Untracked 
|  36|0x0000000709800000, 0x0000000709a00000, 0x0000000709a00000|100%| O|  |TAMS 0x0000000709800000| PB 0x0000000709800000| Untracked 
|  37|0x0000000709a00000, 0x0000000709c00000, 0x0000000709c00000|100%| O|  |TAMS 0x0000000709a00000| PB 0x0000000709a00000| Untracked 
|  38|0x0000000709c00000, 0x0000000709c00000, 0x0000000709e00000|  0%| F|  |TAMS 0x0000000709c00000| PB 0x0000000709c00000| Untracked 
|  39|0x0000000709e00000, 0x000000070a000000, 0x000000070a000000|100%| O|  |TAMS 0x0000000709e00000| PB 0x0000000709e00000| Untracked 
|  40|0x000000070a000000, 0x000000070a200000, 0x000000070a200000|100%| O|  |TAMS 0x000000070a000000| PB 0x000000070a000000| Untracked 
|  41|0x000000070a200000, 0x000000070a400000, 0x000000070a400000|100%| O|  |TAMS 0x000000070a200000| PB 0x000000070a200000| Untracked 
|  42|0x000000070a400000, 0x000000070a600000, 0x000000070a600000|100%| O|  |TAMS 0x000000070a400000| PB 0x000000070a400000| Untracked 
|  43|0x000000070a600000, 0x000000070a800000, 0x000000070a800000|100%| O|  |TAMS 0x000000070a600000| PB 0x000000070a600000| Untracked 
|  44|0x000000070a800000, 0x000000070a800000, 0x000000070aa00000|  0%| F|  |TAMS 0x000000070a800000| PB 0x000000070a800000| Untracked 
|  45|0x000000070aa00000, 0x000000070ac00000, 0x000000070ac00000|100%| O|  |TAMS 0x000000070aa00000| PB 0x000000070aa00000| Untracked 
|  46|0x000000070ac00000, 0x000000070ae00000, 0x000000070ae00000|100%| O|  |TAMS 0x000000070ac00000| PB 0x000000070ac00000| Untracked 
|  47|0x000000070ae00000, 0x000000070b000000, 0x000000070b000000|100%| O|  |TAMS 0x000000070ae00000| PB 0x000000070ae00000| Untracked 
|  48|0x000000070b000000, 0x000000070b200000, 0x000000070b200000|100%| O|  |TAMS 0x000000070b000000| PB 0x000000070b000000| Untracked 
|  49|0x000000070b200000, 0x000000070b400000, 0x000000070b400000|100%| O|  |TAMS 0x000000070b200000| PB 0x000000070b200000| Untracked 
|  50|0x000000070b400000, 0x000000070b600000, 0x000000070b600000|100%| O|  |TAMS 0x000000070b400000| PB 0x000000070b400000| Untracked 
|  51|0x000000070b600000, 0x000000070b800000, 0x000000070b800000|100%| O|  |TAMS 0x000000070b600000| PB 0x000000070b600000| Untracked 
|  52|0x000000070b800000, 0x000000070ba00000, 0x000000070ba00000|100%| O|  |TAMS 0x000000070b800000| PB 0x000000070b800000| Untracked 
|  53|0x000000070ba00000, 0x000000070bc00000, 0x000000070bc00000|100%| O|  |TAMS 0x000000070ba00000| PB 0x000000070ba00000| Untracked 
|  54|0x000000070bc00000, 0x000000070be00000, 0x000000070be00000|100%| O|  |TAMS 0x000000070bc00000| PB 0x000000070bc00000| Untracked 
|  55|0x000000070be00000, 0x000000070c000000, 0x000000070c000000|100%| O|  |TAMS 0x000000070be00000| PB 0x000000070be00000| Untracked 
|  56|0x000000070c000000, 0x000000070c200000, 0x000000070c200000|100%| O|  |TAMS 0x000000070c000000| PB 0x000000070c000000| Untracked 
|  57|0x000000070c200000, 0x000000070c248828, 0x000000070c400000| 14%| O|  |TAMS 0x000000070c200000| PB 0x000000070c200000| Untracked 
|  58|0x000000070c400000, 0x000000070c400000, 0x000000070c600000|  0%| F|  |TAMS 0x000000070c400000| PB 0x000000070c400000| Untracked 
|  59|0x000000070c600000, 0x000000070c600000, 0x000000070c800000|  0%| F|  |TAMS 0x000000070c600000| PB 0x000000070c600000| Untracked 
|  60|0x000000070c800000, 0x000000070c800000, 0x000000070ca00000|  0%| F|  |TAMS 0x000000070c800000| PB 0x000000070c800000| Untracked 
|  61|0x000000070ca00000, 0x000000070ca00000, 0x000000070cc00000|  0%| F|  |TAMS 0x000000070ca00000| PB 0x000000070ca00000| Untracked 
|  62|0x000000070cc00000, 0x000000070cc00000, 0x000000070ce00000|  0%| F|  |TAMS 0x000000070cc00000| PB 0x000000070cc00000| Untracked 
|  63|0x000000070ce00000, 0x000000070ce00000, 0x000000070d000000|  0%| F|  |TAMS 0x000000070ce00000| PB 0x000000070ce00000| Untracked 
|  64|0x000000070d000000, 0x000000070d000000, 0x000000070d200000|  0%| F|  |TAMS 0x000000070d000000| PB 0x000000070d000000| Untracked 
|  65|0x000000070d200000, 0x000000070d3e4198, 0x000000070d400000| 94%| S|CS|TAMS 0x000000070d200000| PB 0x000000070d200000| Complete 
|  66|0x000000070d400000, 0x000000070d600000, 0x000000070d600000|100%| S|CS|TAMS 0x000000070d400000| PB 0x000000070d400000| Complete 
|  67|0x000000070d600000, 0x000000070d800000, 0x000000070d800000|100%| S|CS|TAMS 0x000000070d600000| PB 0x000000070d600000| Complete 
|  68|0x000000070d800000, 0x000000070da00000, 0x000000070da00000|100%| S|CS|TAMS 0x000000070d800000| PB 0x000000070d800000| Complete 
|  69|0x000000070da00000, 0x000000070dc00000, 0x000000070dc00000|100%| S|CS|TAMS 0x000000070da00000| PB 0x000000070da00000| Complete 
|  70|0x000000070dc00000, 0x000000070dc00000, 0x000000070de00000|  0%| F|  |TAMS 0x000000070dc00000| PB 0x000000070dc00000| Untracked 
|  71|0x000000070de00000, 0x000000070de00000, 0x000000070e000000|  0%| F|  |TAMS 0x000000070de00000| PB 0x000000070de00000| Untracked 
|  72|0x000000070e000000, 0x000000070e000000, 0x000000070e200000|  0%| F|  |TAMS 0x000000070e000000| PB 0x000000070e000000| Untracked 
|  73|0x000000070e200000, 0x000000070e200000, 0x000000070e400000|  0%| F|  |TAMS 0x000000070e200000| PB 0x000000070e200000| Untracked 
|  74|0x000000070e400000, 0x000000070e400000, 0x000000070e600000|  0%| F|  |TAMS 0x000000070e400000| PB 0x000000070e400000| Untracked 
|  75|0x000000070e600000, 0x000000070e600000, 0x000000070e800000|  0%| F|  |TAMS 0x000000070e600000| PB 0x000000070e600000| Untracked 
|  76|0x000000070e800000, 0x000000070e800000, 0x000000070ea00000|  0%| F|  |TAMS 0x000000070e800000| PB 0x000000070e800000| Untracked 
|  77|0x000000070ea00000, 0x000000070ea00000, 0x000000070ec00000|  0%| F|  |TAMS 0x000000070ea00000| PB 0x000000070ea00000| Untracked 
|  78|0x000000070ec00000, 0x000000070ec00000, 0x000000070ee00000|  0%| F|  |TAMS 0x000000070ec00000| PB 0x000000070ec00000| Untracked 
|  79|0x000000070ee00000, 0x000000070ee00000, 0x000000070f000000|  0%| F|  |TAMS 0x000000070ee00000| PB 0x000000070ee00000| Untracked 
|  80|0x000000070f000000, 0x000000070f000000, 0x000000070f200000|  0%| F|  |TAMS 0x000000070f000000| PB 0x000000070f000000| Untracked 
|  81|0x000000070f200000, 0x000000070f200000, 0x000000070f400000|  0%| F|  |TAMS 0x000000070f200000| PB 0x000000070f200000| Untracked 
|  82|0x000000070f400000, 0x000000070f400000, 0x000000070f600000|  0%| F|  |TAMS 0x000000070f400000| PB 0x000000070f400000| Untracked 
|  83|0x000000070f600000, 0x000000070f600000, 0x000000070f800000|  0%| F|  |TAMS 0x000000070f600000| PB 0x000000070f600000| Untracked 
|  84|0x000000070f800000, 0x000000070f800000, 0x000000070fa00000|  0%| F|  |TAMS 0x000000070f800000| PB 0x000000070f800000| Untracked 
|  85|0x000000070fa00000, 0x000000070fa00000, 0x000000070fc00000|  0%| F|  |TAMS 0x000000070fa00000| PB 0x000000070fa00000| Untracked 
|  86|0x000000070fc00000, 0x000000070fc00000, 0x000000070fe00000|  0%| F|  |TAMS 0x000000070fc00000| PB 0x000000070fc00000| Untracked 
|  87|0x000000070fe00000, 0x000000070fe00000, 0x0000000710000000|  0%| F|  |TAMS 0x000000070fe00000| PB 0x000000070fe00000| Untracked 
|  88|0x0000000710000000, 0x0000000710000000, 0x0000000710200000|  0%| F|  |TAMS 0x0000000710000000| PB 0x0000000710000000| Untracked 
|  89|0x0000000710200000, 0x0000000710200000, 0x0000000710400000|  0%| F|  |TAMS 0x0000000710200000| PB 0x0000000710200000| Untracked 
|  90|0x0000000710400000, 0x0000000710400000, 0x0000000710600000|  0%| F|  |TAMS 0x0000000710400000| PB 0x0000000710400000| Untracked 
|  91|0x0000000710600000, 0x0000000710600000, 0x0000000710800000|  0%| F|  |TAMS 0x0000000710600000| PB 0x0000000710600000| Untracked 
|  92|0x0000000710800000, 0x0000000710800000, 0x0000000710a00000|  0%| F|  |TAMS 0x0000000710800000| PB 0x0000000710800000| Untracked 
|  93|0x0000000710a00000, 0x0000000710a00000, 0x0000000710c00000|  0%| F|  |TAMS 0x0000000710a00000| PB 0x0000000710a00000| Untracked 
|  94|0x0000000710c00000, 0x0000000710c00000, 0x0000000710e00000|  0%| F|  |TAMS 0x0000000710c00000| PB 0x0000000710c00000| Untracked 
|  95|0x0000000710e00000, 0x0000000710e00000, 0x0000000711000000|  0%| F|  |TAMS 0x0000000710e00000| PB 0x0000000710e00000| Untracked 
|  96|0x0000000711000000, 0x0000000711000000, 0x0000000711200000|  0%| F|  |TAMS 0x0000000711000000| PB 0x0000000711000000| Untracked 
|  97|0x0000000711200000, 0x0000000711200000, 0x0000000711400000|  0%| F|  |TAMS 0x0000000711200000| PB 0x0000000711200000| Untracked 
|  98|0x0000000711400000, 0x0000000711400000, 0x0000000711600000|  0%| F|  |TAMS 0x0000000711400000| PB 0x0000000711400000| Untracked 
|  99|0x0000000711600000, 0x0000000711600000, 0x0000000711800000|  0%| F|  |TAMS 0x0000000711600000| PB 0x0000000711600000| Untracked 
| 100|0x0000000711800000, 0x0000000711800000, 0x0000000711a00000|  0%| F|  |TAMS 0x0000000711800000| PB 0x0000000711800000| Untracked 
| 101|0x0000000711a00000, 0x0000000711a00000, 0x0000000711c00000|  0%| F|  |TAMS 0x0000000711a00000| PB 0x0000000711a00000| Untracked 
| 102|0x0000000711c00000, 0x0000000711c00000, 0x0000000711e00000|  0%| F|  |TAMS 0x0000000711c00000| PB 0x0000000711c00000| Untracked 
| 103|0x0000000711e00000, 0x0000000711e00000, 0x0000000712000000|  0%| F|  |TAMS 0x0000000711e00000| PB 0x0000000711e00000| Untracked 
| 104|0x0000000712000000, 0x0000000712000000, 0x0000000712200000|  0%| F|  |TAMS 0x0000000712000000| PB 0x0000000712000000| Untracked 
| 105|0x0000000712200000, 0x0000000712200000, 0x0000000712400000|  0%| F|  |TAMS 0x0000000712200000| PB 0x0000000712200000| Untracked 
| 106|0x0000000712400000, 0x0000000712400000, 0x0000000712600000|  0%| F|  |TAMS 0x0000000712400000| PB 0x0000000712400000| Untracked 
| 107|0x0000000712600000, 0x0000000712600000, 0x0000000712800000|  0%| F|  |TAMS 0x0000000712600000| PB 0x0000000712600000| Untracked 
| 108|0x0000000712800000, 0x0000000712800000, 0x0000000712a00000|  0%| F|  |TAMS 0x0000000712800000| PB 0x0000000712800000| Untracked 
| 109|0x0000000712a00000, 0x0000000712a00000, 0x0000000712c00000|  0%| F|  |TAMS 0x0000000712a00000| PB 0x0000000712a00000| Untracked 
| 110|0x0000000712c00000, 0x0000000712c00000, 0x0000000712e00000|  0%| F|  |TAMS 0x0000000712c00000| PB 0x0000000712c00000| Untracked 
| 111|0x0000000712e00000, 0x0000000712e00000, 0x0000000713000000|  0%| F|  |TAMS 0x0000000712e00000| PB 0x0000000712e00000| Untracked 
| 112|0x0000000713000000, 0x0000000713000000, 0x0000000713200000|  0%| F|  |TAMS 0x0000000713000000| PB 0x0000000713000000| Untracked 
| 113|0x0000000713200000, 0x0000000713200000, 0x0000000713400000|  0%| F|  |TAMS 0x0000000713200000| PB 0x0000000713200000| Untracked 
| 114|0x0000000713400000, 0x0000000713500800, 0x0000000713600000| 50%| E|  |TAMS 0x0000000713400000| PB 0x0000000713400000| Complete 
| 115|0x0000000713600000, 0x0000000713800000, 0x0000000713800000|100%| E|CS|TAMS 0x0000000713600000| PB 0x0000000713600000| Complete 
| 116|0x0000000713800000, 0x0000000713a00000, 0x0000000713a00000|100%| E|CS|TAMS 0x0000000713800000| PB 0x0000000713800000| Complete 
| 117|0x0000000713a00000, 0x0000000713c00000, 0x0000000713c00000|100%| E|CS|TAMS 0x0000000713a00000| PB 0x0000000713a00000| Complete 
| 119|0x0000000713e00000, 0x0000000714000000, 0x0000000714000000|100%| E|CS|TAMS 0x0000000713e00000| PB 0x0000000713e00000| Complete 
| 125|0x0000000714a00000, 0x0000000714c00000, 0x0000000714c00000|100%| E|CS|TAMS 0x0000000714a00000| PB 0x0000000714a00000| Complete 

Card table byte_map: [0x000001df47220000,0x000001df47a00000] _byte_map_base: 0x000001df439f8000

Marking Bits: (CMBitMap*) 0x000001df3affb270
 Bits: [0x000001df47a00000, 0x000001df4b8c0000)

Polling page: 0x000001df38eb0000

Metaspace:

Usage:
  Non-class:    105.73 MB used.
      Class:     16.88 MB used.
       Both:    122.61 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,     106.31 MB ( 83%) committed,  2 nodes.
      Class space:        1.00 GB reserved,      17.38 MB (  2%) committed,  1 nodes.
             Both:        1.12 GB reserved,     123.69 MB ( 11%) committed. 

Chunk freelists:
   Non-Class:  4.78 MB
       Class:  14.63 MB
        Both:  19.41 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 183.38 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 1746.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1979.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 6.
num_chunks_taken_from_freelist: 6307.
num_chunk_merges: 6.
num_chunk_splits: 4607.
num_chunks_enlarged: 3640.
num_inconsistent_stats: 0.

CodeCache: size=49152Kb used=18924Kb max_used=24440Kb free=30227Kb
 bounds [0x000001df433e0000, 0x000001df44bc0000, 0x000001df463e0000]
 total_blobs=10060 nmethods=9194 adapters=789
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 301.428 Thread 0x000001df97d4d5a0 nmethod 16417 0x000001df44778b90 code [0x000001df44778d60, 0x000001df44778fe0]
Event: 301.428 Thread 0x000001df97d4d5a0 16421       1       sun.rmi.server.MarshalOutputStream$1::run (11 bytes)
Event: 301.428 Thread 0x000001df97d4d5a0 nmethod 16421 0x000001df44778790 code [0x000001df44778940, 0x000001df44778a58]
Event: 301.428 Thread 0x000001df4e41a620 nmethod 16420 0x000001df44778390 code [0x000001df44778540, 0x000001df44778658]
Event: 301.428 Thread 0x000001df4e41a620 16423       1       java.io.ObjectOutputStream::enableReplaceObject (47 bytes)
Event: 301.428 Thread 0x000001df97d4d5a0 16422       1       sun.rmi.server.MarshalOutputStream::access$000 (6 bytes)
Event: 301.429 Thread 0x000001df97d4d5a0 nmethod 16422 0x000001df44778010 code [0x000001df447781c0, 0x000001df447782a8]
Event: 301.429 Thread 0x000001df97d4d5a0 16424   !   1       sun.rmi.transport.StreamRemoteCall::releaseOutputStream (60 bytes)
Event: 301.429 Thread 0x000001df4e41a620 nmethod 16423 0x000001df44824710 code [0x000001df448248a0, 0x000001df448249c8]
Event: 301.429 Thread 0x000001df4e41a620 16425       1       sun.rmi.transport.ConnectionOutputStream::done (15 bytes)
Event: 301.429 Thread 0x000001df4e41a620 nmethod 16425 0x000001df44824390 code [0x000001df44824540, 0x000001df44824648]
Event: 301.429 Thread 0x000001df4e41a620 16426  s    1       sun.rmi.transport.Target::decrementCallCount (50 bytes)
Event: 301.429 Thread 0x000001df4e41a620 nmethod 16426 0x000001df44823e10 code [0x000001df44823fe0, 0x000001df44824268]
Event: 301.429 Thread 0x000001df97d4d5a0 nmethod 16424 0x000001df44936d90 code [0x000001df44936fa0, 0x000001df44937318]
Event: 303.753 Thread 0x000001df4e41a620 16427       1       org.apache.coyote.AbstractProtocol$$Lambda/0x000001df50fc7800::run (8 bytes)
Event: 303.753 Thread 0x000001df4e41a620 nmethod 16427 0x000001df44823a90 code [0x000001df44823c40, 0x000001df44823d38]
Event: 303.753 Thread 0x000001df4e41a620 16428       1       org.apache.coyote.AbstractProtocol::lambda$startAsyncTimeout$1 (46 bytes)
Event: 303.753 Thread 0x000001df4e41a620 nmethod 16428 0x000001df44936790 code [0x000001df44936980, 0x000001df44936be8]
Event: 307.357 Thread 0x000001df4e41a620 16429       1       sun.nio.ch.SelectorImpl::keys (9 bytes)
Event: 307.357 Thread 0x000001df4e41a620 nmethod 16429 0x000001df44823590 code [0x000001df44823740, 0x000001df448238a0]

GC Heap History (20 events):
Event: 40.063 GC heap before
{Heap before GC invocations=39 (full 0):
 garbage-first heap   total 204800K, used 185099K [0x0000000705000000, 0x0000000800000000)
  region size 2048K, 54 young (110592K), 4 survivors (8192K)
 Metaspace       used 100006K, committed 100864K, reserved 1179648K
  class space    used 13804K, committed 14208K, reserved 1048576K
}
Event: 40.071 GC heap after
{Heap after GC invocations=40 (full 0):
 garbage-first heap   total 204800K, used 85393K [0x0000000705000000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 4 survivors (8192K)
 Metaspace       used 100006K, committed 100864K, reserved 1179648K
  class space    used 13804K, committed 14208K, reserved 1048576K
}
Event: 41.053 GC heap before
{Heap before GC invocations=40 (full 0):
 garbage-first heap   total 204800K, used 183697K [0x0000000705000000, 0x0000000800000000)
  region size 2048K, 52 young (106496K), 4 survivors (8192K)
 Metaspace       used 100752K, committed 101632K, reserved 1179648K
  class space    used 13882K, committed 14272K, reserved 1048576K
}
Event: 41.061 GC heap after
{Heap after GC invocations=41 (full 0):
 garbage-first heap   total 204800K, used 90026K [0x0000000705000000, 0x0000000800000000)
  region size 2048K, 6 young (12288K), 6 survivors (12288K)
 Metaspace       used 100752K, committed 101632K, reserved 1179648K
  class space    used 13882K, committed 14272K, reserved 1048576K
}
Event: 41.364 GC heap before
{Heap before GC invocations=42 (full 0):
 garbage-first heap   total 204800K, used 180138K [0x0000000705000000, 0x0000000800000000)
  region size 2048K, 50 young (102400K), 6 survivors (12288K)
 Metaspace       used 100771K, committed 101632K, reserved 1179648K
  class space    used 13883K, committed 14272K, reserved 1048576K
}
Event: 41.374 GC heap after
{Heap after GC invocations=43 (full 0):
 garbage-first heap   total 204800K, used 93580K [0x0000000705000000, 0x0000000800000000)
  region size 2048K, 5 young (10240K), 5 survivors (10240K)
 Metaspace       used 100771K, committed 101632K, reserved 1179648K
  class space    used 13883K, committed 14272K, reserved 1048576K
}
Event: 42.003 GC heap before
{Heap before GC invocations=43 (full 0):
 garbage-first heap   total 204800K, used 181644K [0x0000000705000000, 0x0000000800000000)
  region size 2048K, 48 young (98304K), 5 survivors (10240K)
 Metaspace       used 101618K, committed 102528K, reserved 1179648K
  class space    used 14049K, committed 14464K, reserved 1048576K
}
Event: 42.011 GC heap after
{Heap after GC invocations=44 (full 0):
 garbage-first heap   total 204800K, used 96477K [0x0000000705000000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 4 survivors (8192K)
 Metaspace       used 101618K, committed 102528K, reserved 1179648K
  class space    used 14049K, committed 14464K, reserved 1048576K
}
Event: 42.947 GC heap before
{Heap before GC invocations=44 (full 0):
 garbage-first heap   total 204800K, used 182493K [0x0000000705000000, 0x0000000800000000)
  region size 2048K, 47 young (96256K), 4 survivors (8192K)
 Metaspace       used 103992K, committed 104896K, reserved 1179648K
  class space    used 14358K, committed 14784K, reserved 1048576K
}
Event: 42.953 GC heap after
{Heap after GC invocations=45 (full 0):
 garbage-first heap   total 245760K, used 98428K [0x0000000705000000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 4 survivors (8192K)
 Metaspace       used 103992K, committed 104896K, reserved 1179648K
  class space    used 14358K, committed 14784K, reserved 1048576K
}
Event: 44.367 GC heap before
{Heap before GC invocations=46 (full 0):
 garbage-first heap   total 245760K, used 221308K [0x0000000705000000, 0x0000000800000000)
  region size 2048K, 65 young (133120K), 4 survivors (8192K)
 Metaspace       used 105550K, committed 106432K, reserved 1179648K
  class space    used 14491K, committed 14912K, reserved 1048576K
}
Event: 44.376 GC heap after
{Heap after GC invocations=47 (full 0):
 garbage-first heap   total 245760K, used 104838K [0x0000000705000000, 0x0000000800000000)
  region size 2048K, 7 young (14336K), 7 survivors (14336K)
 Metaspace       used 105550K, committed 106432K, reserved 1179648K
  class space    used 14491K, committed 14912K, reserved 1048576K
}
Event: 45.608 GC heap before
{Heap before GC invocations=47 (full 0):
 garbage-first heap   total 245760K, used 217478K [0x0000000705000000, 0x0000000800000000)
  region size 2048K, 62 young (126976K), 7 survivors (14336K)
 Metaspace       used 108842K, committed 109760K, reserved 1179648K
  class space    used 14929K, committed 15360K, reserved 1048576K
}
Event: 45.618 GC heap after
{Heap after GC invocations=48 (full 0):
 garbage-first heap   total 245760K, used 110485K [0x0000000705000000, 0x0000000800000000)
  region size 2048K, 7 young (14336K), 7 survivors (14336K)
 Metaspace       used 108842K, committed 109760K, reserved 1179648K
  class space    used 14929K, committed 15360K, reserved 1048576K
}
Event: 46.797 GC heap before
{Heap before GC invocations=48 (full 0):
 garbage-first heap   total 245760K, used 216981K [0x0000000705000000, 0x0000000800000000)
  region size 2048K, 59 young (120832K), 7 survivors (14336K)
 Metaspace       used 111712K, committed 112640K, reserved 1179648K
  class space    used 15374K, committed 15808K, reserved 1048576K
}
Event: 46.806 GC heap after
{Heap after GC invocations=49 (full 0):
 garbage-first heap   total 245760K, used 114521K [0x0000000705000000, 0x0000000800000000)
  region size 2048K, 5 young (10240K), 5 survivors (10240K)
 Metaspace       used 111712K, committed 112640K, reserved 1179648K
  class space    used 15374K, committed 15808K, reserved 1048576K
}
Event: 48.495 GC heap before
{Heap before GC invocations=50 (full 0):
 garbage-first heap   total 245760K, used 223065K [0x0000000705000000, 0x0000000800000000)
  region size 2048K, 58 young (118784K), 5 survivors (10240K)
 Metaspace       used 115421K, committed 116480K, reserved 1179648K
  class space    used 15960K, committed 16448K, reserved 1048576K
}
Event: 48.507 GC heap after
{Heap after GC invocations=51 (full 0):
 garbage-first heap   total 245760K, used 118734K [0x0000000705000000, 0x0000000800000000)
  region size 2048K, 5 young (10240K), 5 survivors (10240K)
 Metaspace       used 115421K, committed 116480K, reserved 1179648K
  class space    used 15960K, committed 16448K, reserved 1048576K
}
Event: 144.625 GC heap before
{Heap before GC invocations=51 (full 0):
 garbage-first heap   total 245760K, used 219086K [0x0000000705000000, 0x0000000800000000)
  region size 2048K, 55 young (112640K), 5 survivors (10240K)
 Metaspace       used 125024K, committed 126208K, reserved 1179648K
  class space    used 17219K, committed 17792K, reserved 1048576K
}
Event: 144.633 GC heap after
{Heap after GC invocations=52 (full 0):
 garbage-first heap   total 245760K, used 123058K [0x0000000705000000, 0x0000000800000000)
  region size 2048K, 5 young (10240K), 5 survivors (10240K)
 Metaspace       used 125024K, committed 126208K, reserved 1179648K
  class space    used 17219K, committed 17792K, reserved 1048576K
}

Dll operation events (12 events):
Event: 0.033 Loaded shared library C:\Program Files\Java\jdk-21\bin\java.dll
Event: 0.298 Loaded shared library C:\Program Files\Java\jdk-21\bin\zip.dll
Event: 0.301 Loaded shared library C:\Program Files\Java\jdk-21\bin\instrument.dll
Event: 0.306 Loaded shared library C:\Program Files\Java\jdk-21\bin\net.dll
Event: 0.308 Loaded shared library C:\Program Files\Java\jdk-21\bin\nio.dll
Event: 0.311 Loaded shared library C:\Program Files\Java\jdk-21\bin\zip.dll
Event: 0.553 Loaded shared library C:\Program Files\Java\jdk-21\bin\jimage.dll
Event: 0.614 Loaded shared library C:\Program Files\Java\jdk-21\bin\management.dll
Event: 0.637 Loaded shared library C:\Program Files\Java\jdk-21\bin\management_ext.dll
Event: 0.988 Loaded shared library C:\Program Files\Java\jdk-21\bin\extnet.dll
Event: 1.026 Loaded shared library C:\Program Files\Java\jdk-21\bin\verify.dll
Event: 2.309 Loaded shared library C:\Program Files\Java\jdk-21\bin\sunmscapi.dll

Deoptimization events (20 events):
Event: 144.768 Thread 0x000001df9d802e50 DEOPT PACKING pc=0x000001df4395b28c sp=0x0000005c823f95b0
Event: 144.768 Thread 0x000001df9d802e50 DEOPT UNPACKING pc=0x000001df43434e42 sp=0x0000005c823f8a88 mode 1
Event: 144.768 Thread 0x000001df9d802e50 DEOPT PACKING pc=0x000001df43975d59 sp=0x0000005c823f8a10
Event: 144.768 Thread 0x000001df9d802e50 DEOPT UNPACKING pc=0x000001df43434e42 sp=0x0000005c823f7e68 mode 1
Event: 144.768 Thread 0x000001df9d802e50 DEOPT PACKING pc=0x000001df43961c2c sp=0x0000005c823f8aa0
Event: 144.768 Thread 0x000001df9d802e50 DEOPT UNPACKING pc=0x000001df43434e42 sp=0x0000005c823f7f48 mode 1
Event: 144.768 Thread 0x000001df9d802e50 DEOPT PACKING pc=0x000001df4395af14 sp=0x0000005c823f9520
Event: 144.768 Thread 0x000001df9d802e50 DEOPT UNPACKING pc=0x000001df43434e42 sp=0x0000005c823f89e8 mode 1
Event: 144.768 Thread 0x000001df9d802e50 DEOPT PACKING pc=0x000001df4395b28c sp=0x0000005c823f95b0
Event: 144.768 Thread 0x000001df9d802e50 DEOPT UNPACKING pc=0x000001df43434e42 sp=0x0000005c823f8a88 mode 1
Event: 308.778 Thread 0x000001df985f3bb0 DEOPT PACKING pc=0x000001df443b6904 sp=0x0000005c842f8c60
Event: 308.778 Thread 0x000001df985f3bb0 DEOPT UNPACKING pc=0x000001df43434e42 sp=0x0000005c842f8148 mode 0
Event: 308.779 Thread 0x000001df985f3bb0 DEOPT PACKING pc=0x000001df43ed4f64 sp=0x0000005c842f8e00
Event: 308.779 Thread 0x000001df985f3bb0 DEOPT UNPACKING pc=0x000001df43434e42 sp=0x0000005c842f82a0 mode 0
Event: 308.780 Thread 0x000001df985f3bb0 DEOPT PACKING pc=0x000001df443b6904 sp=0x0000005c842f9140
Event: 308.780 Thread 0x000001df985f3bb0 DEOPT UNPACKING pc=0x000001df43434e42 sp=0x0000005c842f8628 mode 0
Event: 308.781 Thread 0x000001df985f3bb0 DEOPT PACKING pc=0x000001df43ed4f64 sp=0x0000005c842f92e0
Event: 308.781 Thread 0x000001df985f3bb0 DEOPT UNPACKING pc=0x000001df43434e42 sp=0x0000005c842f8780 mode 0
Event: 308.784 Thread 0x000001df985f3bb0 DEOPT PACKING pc=0x000001df44a1dc3c sp=0x0000005c842f98d0
Event: 308.784 Thread 0x000001df985f3bb0 DEOPT UNPACKING pc=0x000001df43434e42 sp=0x0000005c842f8d78 mode 0

Classes loaded (20 events):
Event: 83.529 Loading class java/nio/channels/UnsupportedAddressTypeException
Event: 83.529 Loading class java/nio/channels/UnsupportedAddressTypeException done
Event: 83.532 Loading class java/util/concurrent/CompletableFuture$UniAccept
Event: 83.532 Loading class java/util/concurrent/CompletableFuture$UniAccept done
Event: 83.646 Loading class java/util/concurrent/CompletableFuture$UniCompose
Event: 83.646 Loading class java/util/concurrent/CompletableFuture$UniCompose done
Event: 141.214 Loading class java/lang/CharacterDataUndefined
Event: 141.220 Loading class java/lang/CharacterDataUndefined done
Event: 144.328 Loading class java/nio/charset/IllegalCharsetNameException
Event: 144.328 Loading class java/nio/charset/IllegalCharsetNameException done
Event: 144.363 Loading class sun/nio/ch/IOVecWrapper
Event: 144.369 Loading class sun/nio/ch/IOVecWrapper done
Event: 144.369 Loading class sun/nio/ch/AllocatedNativeObject
Event: 144.370 Loading class sun/nio/ch/NativeObject
Event: 144.370 Loading class sun/nio/ch/NativeObject done
Event: 144.370 Loading class sun/nio/ch/AllocatedNativeObject done
Event: 144.370 Loading class sun/nio/ch/IOVecWrapper$Deallocator
Event: 144.371 Loading class sun/nio/ch/IOVecWrapper$Deallocator done
Event: 269.894 Loading class sun/net/ConnectionResetException
Event: 269.900 Loading class sun/net/ConnectionResetException done

Classes unloaded (0 events):
No events

Classes redefined (1 events):
Event: 308.828 Thread 0x000001df4e3eb4b0 redefined class name=com.aig.aigone.service.impl.EmrServiceImpl, count=1

Internal exceptions (20 events):
Event: 54.611 Thread 0x000001df9d807680 Exception <a 'java/lang/NoSuchMethodError'{0x00000007132df1f0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object, long, java.lang.Object)'> (0x00000007132df1f0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 54.669 Thread 0x000001df9d807680 Exception <a 'java/lang/NoSuchMethodError'{0x0000000712f36bf8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, int)'> (0x0000000712f36bf8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 54.893 Thread 0x000001df9d802e50 Exception <a 'java/lang/NoSuchMethodError'{0x0000000712695578}: 'void java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x0000000712695578) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 54.971 Thread 0x000001df9d802e50 Exception <a 'java/lang/IncompatibleClassChangeError'{0x0000000712759ec0}: Found class java.lang.Object, but interface was expected> (0x0000000712759ec0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 83.553 Thread 0x000001df9d510690 Exception <a 'java/lang/NoSuchMethodError'{0x000000070fb53e20}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000070fb53e20) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 83.753 Thread 0x000001df9d802e50 Exception <a 'java/lang/NoSuchMethodError'{0x000000070f4b4e30}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, long)'> (0x000000070f4b4e30) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 141.161 Thread 0x000001df9d802e50 Exception <a 'java/lang/NoSuchMethodError'{0x000000070ea721c0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, long, java.lang.Object)'> (0x000000070ea721c0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 141.201 Thread 0x000001df9d802e50 Exception <a 'java/lang/NoSuchMethodError'{0x000000070eb33980}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000070eb33980) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 141.236 Thread 0x000001df9d6bbda0 Exception <a 'java/lang/NoSuchMethodError'{0x000000070e653028}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000070e653028) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 141.237 Thread 0x000001df9d6bbda0 Exception <a 'java/lang/NoSuchMethodError'{0x000000070e65c998}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000070e65c998) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 141.238 Thread 0x000001df9d6bbda0 Exception <a 'java/lang/NoSuchMethodError'{0x000000070e663a78}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000070e663a78) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 141.238 Thread 0x000001df9d6bbda0 Exception <a 'java/lang/NoSuchMethodError'{0x000000070e667810}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000070e667810) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 141.238 Thread 0x000001df9d6bbda0 Exception <a 'java/lang/NoSuchMethodError'{0x000000070e66aec0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000070e66aec0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 144.316 Thread 0x000001df9d6bbda0 Exception <a 'java/lang/NoSuchMethodError'{0x000000070e6d2d88}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int)'> (0x000000070e6d2d88) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 144.317 Thread 0x000001df9d6bbda0 Exception <a 'java/lang/NoSuchMethodError'{0x000000070e6d6210}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int)'> (0x000000070e6d6210) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 144.317 Thread 0x000001df9d6bbda0 Exception <a 'java/lang/NoSuchMethodError'{0x000000070e6db520}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int)'> (0x000000070e6db520) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 144.357 Thread 0x000001df9d6bbda0 Exception <a 'java/lang/NoSuchMethodError'{0x000000070e260ae8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000070e260ae8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 144.657 Thread 0x000001df9d802e50 Exception <a 'java/lang/NoSuchMethodError'{0x0000000713f88520}: 'long java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000713f88520) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 144.675 Thread 0x000001df9d802e50 Exception <a 'java/lang/NoSuchMethodError'{0x0000000713ad3490}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, int)'> (0x0000000713ad3490) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 269.900 Thread 0x000001df9d6bbda0 Exception <a 'sun/net/ConnectionResetException'{0x00000007136ff4d0}: Connection reset> (0x00000007136ff4d0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 539]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 300.497 Executing VM operation: Cleanup
Event: 300.497 Executing VM operation: Cleanup done
Event: 301.500 Executing VM operation: Cleanup
Event: 301.500 Executing VM operation: Cleanup done
Event: 308.545 Executing VM operation: Cleanup
Event: 308.545 Executing VM operation: Cleanup done
Event: 308.777 Executing VM operation: ChangeSingleStep
Event: 308.777 Executing VM operation: ChangeSingleStep done
Event: 308.784 Executing VM operation: ChangeSingleStep
Event: 308.784 Executing VM operation: ChangeSingleStep done
Event: 308.817 Executing VM operation: RedefineClasses
Event: 308.853 Executing VM operation: RedefineClasses done
Event: 308.883 Executing VM operation: ChangeBreakpoints
Event: 308.886 Executing VM operation: ChangeBreakpoints done
Event: 308.908 Executing VM operation: ChangeBreakpoints
Event: 308.911 Executing VM operation: ChangeBreakpoints done
Event: 308.928 Executing VM operation: ChangeBreakpoints
Event: 308.931 Executing VM operation: ChangeBreakpoints done
Event: 308.934 Executing VM operation: ChangeSingleStep
Event: 308.934 Executing VM operation: ChangeSingleStep done

Events (20 events):
Event: 149.291 Thread 0x000001df97d4c800 Thread exited: 0x000001df97d4c800
Event: 154.305 Thread 0x000001df97d4b390 Thread exited: 0x000001df97d4b390
Event: 159.479 Thread 0x000001df97d4acc0 Thread added: 0x000001df97d4acc0
Event: 166.483 Thread 0x000001df97d4acc0 Thread exited: 0x000001df97d4acc0
Event: 168.840 Thread 0x000001df97d4c800 Thread added: 0x000001df97d4c800
Event: 176.513 Thread 0x000001df97d4acc0 Thread added: 0x000001df97d4acc0
Event: 176.513 Thread 0x000001df97d4b390 Thread added: 0x000001df97d4b390
Event: 177.514 Thread 0x000001df97d4b390 Thread exited: 0x000001df97d4b390
Event: 184.526 Thread 0x000001df97d4acc0 Thread exited: 0x000001df97d4acc0
Event: 187.169 Thread 0x000001df97d4c800 Thread exited: 0x000001df97d4c800
Event: 229.816 Thread 0x000001df97d4ba60 Thread added: 0x000001df97d4ba60
Event: 229.816 Thread 0x000001df97d49f20 Thread added: 0x000001df97d49f20
Event: 230.101 Thread 0x000001df97d4c800 Thread added: 0x000001df97d4c800
Event: 235.183 Thread 0x000001df97d4c800 Thread exited: 0x000001df97d4c800
Event: 235.183 Thread 0x000001df97d49f20 Thread exited: 0x000001df97d49f20
Event: 240.191 Thread 0x000001df97d4ba60 Thread exited: 0x000001df97d4ba60
Event: 289.411 Thread 0x000001df97d4c800 Thread added: 0x000001df97d4c800
Event: 294.496 Thread 0x000001df97d4c800 Thread exited: 0x000001df97d4c800
Event: 301.427 Thread 0x000001df97d4d5a0 Thread added: 0x000001df97d4d5a0
Event: 303.753 Thread 0x000001df97d4d5a0 Thread exited: 0x000001df97d4d5a0


Dynamic libraries:
0x00007ff6607a0000 - 0x00007ff6607b0000 	C:\Program Files\Java\jdk-21\bin\javaw.exe
0x00007ffd9d590000 - 0x00007ffd9d788000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffd9c650000 - 0x00007ffd9c712000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffd9ac30000 - 0x00007ffd9af2f000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffd9b480000 - 0x00007ffd9b580000 	C:\Windows\System32\ucrtbase.dll
0x00007ffd8ed20000 - 0x00007ffd8ed3b000 	C:\Program Files\Java\jdk-21\bin\VCRUNTIME140.dll
0x00007ffd8ed00000 - 0x00007ffd8ed19000 	C:\Program Files\Java\jdk-21\bin\jli.dll
0x00007ffd9d4a0000 - 0x00007ffd9d54f000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffd9c9d0000 - 0x00007ffd9ca6e000 	C:\Windows\System32\msvcrt.dll
0x00007ffd9c5a0000 - 0x00007ffd9c63f000 	C:\Windows\System32\sechost.dll
0x00007ffd9d160000 - 0x00007ffd9d283000 	C:\Windows\System32\RPCRT4.dll
0x00007ffd9b580000 - 0x00007ffd9b5a7000 	C:\Windows\System32\bcrypt.dll
0x00007ffd9b5c0000 - 0x00007ffd9b75d000 	C:\Windows\System32\USER32.dll
0x00007ffd9b390000 - 0x00007ffd9b3b2000 	C:\Windows\System32\win32u.dll
0x00007ffd9d470000 - 0x00007ffd9d49b000 	C:\Windows\System32\GDI32.dll
0x00007ffd88480000 - 0x00007ffd8871a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_60b8b9eb71f62e16\COMCTL32.dll
0x00007ffd9b270000 - 0x00007ffd9b38a000 	C:\Windows\System32\gdi32full.dll
0x00007ffd9afc0000 - 0x00007ffd9b05d000 	C:\Windows\System32\msvcp_win.dll
0x00007ffd8f170000 - 0x00007ffd8f17a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffd9d440000 - 0x00007ffd9d46f000 	C:\Windows\System32\IMM32.DLL
0x00007ffd8ecf0000 - 0x00007ffd8ecfc000 	C:\Program Files\Java\jdk-21\bin\vcruntime140_1.dll
0x00007ffd6b7f0000 - 0x00007ffd6b87e000 	C:\Program Files\Java\jdk-21\bin\msvcp140.dll
0x00007ffd51d50000 - 0x00007ffd52a5d000 	C:\Program Files\Java\jdk-21\bin\server\jvm.dll
0x00007ffd9bdc0000 - 0x00007ffd9be2b000 	C:\Windows\System32\WS2_32.dll
0x00007ffd985d0000 - 0x00007ffd985f7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffd9aa90000 - 0x00007ffd9aadb000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffd9aa70000 - 0x00007ffd9aa82000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffd99140000 - 0x00007ffd99152000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffd8ece0000 - 0x00007ffd8ecea000 	C:\Program Files\Java\jdk-21\bin\jimage.dll
0x00007ffd983e0000 - 0x00007ffd985c4000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffd983a0000 - 0x00007ffd983d4000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffd9af30000 - 0x00007ffd9afb2000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffd8e730000 - 0x00007ffd8e76c000 	C:\Program Files\Java\jdk-21\bin\jdwp.dll
0x00007ffd8ec20000 - 0x00007ffd8ec2f000 	C:\Program Files\Java\jdk-21\bin\instrument.dll
0x00007ffd8e940000 - 0x00007ffd8e95f000 	C:\Program Files\Java\jdk-21\bin\java.dll
0x00007ffd9be30000 - 0x00007ffd9c59d000 	C:\Windows\System32\SHELL32.dll
0x00007ffd98950000 - 0x00007ffd990f4000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffd9ce00000 - 0x00007ffd9d155000 	C:\Windows\System32\combase.dll
0x00007ffd9a4b0000 - 0x00007ffd9a4df000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ffd9ccd0000 - 0x00007ffd9cd9d000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffd9c720000 - 0x00007ffd9c7cd000 	C:\Windows\System32\SHCORE.dll
0x00007ffd9c7d0000 - 0x00007ffd9c825000 	C:\Windows\System32\shlwapi.dll
0x00007ffd9ab60000 - 0x00007ffd9ab84000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffd8ea20000 - 0x00007ffd8ea2c000 	C:\Program Files\Java\jdk-21\bin\dt_socket.dll
0x00007ffd99f90000 - 0x00007ffd99fcb000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffd9a290000 - 0x00007ffd9a2fc000 	C:\Windows\system32\mswsock.dll
0x00007ffd99fd0000 - 0x00007ffd9a09a000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffd9b5b0000 - 0x00007ffd9b5b8000 	C:\Windows\System32\NSI.dll
0x00007ffd92cf0000 - 0x00007ffd92cfa000 	C:\Windows\System32\rasadhlp.dll
0x00007ffd92300000 - 0x00007ffd92380000 	C:\Windows\System32\fwpuclnt.dll
0x00007ffd8e780000 - 0x00007ffd8e798000 	C:\Program Files\Java\jdk-21\bin\zip.dll
0x00007ffd8e720000 - 0x00007ffd8e730000 	C:\Program Files\Java\jdk-21\bin\net.dll
0x00007ffd99b50000 - 0x00007ffd99c5a000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffd869a0000 - 0x00007ffd869b6000 	C:\Program Files\Java\jdk-21\bin\nio.dll
0x00007ffd84fd0000 - 0x00007ffd84fda000 	C:\Program Files\Java\jdk-21\bin\management.dll
0x00007ffd84fc0000 - 0x00007ffd84fcb000 	C:\Program Files\Java\jdk-21\bin\management_ext.dll
0x00007ffd9c640000 - 0x00007ffd9c648000 	C:\Windows\System32\PSAPI.DLL
0x00007ffd84240000 - 0x00007ffd84257000 	C:\Windows\system32\napinsp.dll
0x00007ffd84220000 - 0x00007ffd8423b000 	C:\Windows\system32\pnrpnsp.dll
0x00007ffd94640000 - 0x00007ffd94655000 	C:\Windows\system32\wshbth.dll
0x00007ffd96650000 - 0x00007ffd9666d000 	C:\Windows\system32\NLAapi.dll
0x00007ffd84050000 - 0x00007ffd84062000 	C:\Windows\System32\winrnr.dll
0x00007ffd9a4f0000 - 0x00007ffd9a508000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffd99ac0000 - 0x00007ffd99af4000 	C:\Windows\system32\rsaenh.dll
0x00007ffd9ab20000 - 0x00007ffd9ab4e000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffd9a420000 - 0x00007ffd9a42c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffd84fb0000 - 0x00007ffd84fb9000 	C:\Program Files\Java\jdk-21\bin\extnet.dll
0x00007ffd84f70000 - 0x00007ffd84f80000 	C:\Program Files\Java\jdk-21\bin\verify.dll
0x00007ffd84f40000 - 0x00007ffd84f4e000 	C:\Program Files\Java\jdk-21\bin\sunmscapi.dll
0x00007ffd9b110000 - 0x00007ffd9b26c000 	C:\Windows\System32\CRYPT32.dll
0x00007ffd9a600000 - 0x00007ffd9a627000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007ffd9a5c0000 - 0x00007ffd9a5fb000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007ffd93400000 - 0x00007ffd93417000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ffd933e0000 - 0x00007ffd933fd000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007ffd66970000 - 0x00007ffd66977000 	C:\Windows\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-21\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_60b8b9eb71f62e16;C:\Program Files\Java\jdk-21\bin\server

VM Arguments:
jvm_args: -XX:+ShowCodeDetailsInExceptionMessages -agentlib:jdwp=transport=dt_socket,suspend=y,address=localhost:49933 -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=49882 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Djava.rmi.server.hostname=localhost -Dspring.jmx.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -Dspring.application.admin.enabled=true -XX:TieredStopAtLevel=1 -Dspring.boot.project.name=AIG-ONE -javaagent:D:\sts-4.22.1.RELEASE\configuration\org.eclipse.osgi\266\0\.cp\lib\javaagent-shaded.jar -Dfile.encoding=UTF-8 -Dstdout.encoding=UTF-8 -Dstderr.encoding=UTF-8 
java_command: com.aig.aigone.AigOneApplication --spring.output.ansi.enabled=always
java_class_path (initial): D:\projects\backend\AIG-ONE-Backend\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-jpa\3.2.5\spring-boot-starter-data-jpa-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\3.2.5\spring-boot-starter-aop-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.22\aspectjweaver-1.9.22.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\3.2.5\spring-boot-starter-jdbc-3.2.5.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\5.0.1\HikariCP-5.0.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\6.1.6\spring-jdbc-6.1.6.jar;C:\Users\<USER>\.m2\repository\org\hibernate\orm\hibernate-core\6.4.4.Final\hibernate-core-6.4.4.Final.jar;C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\3.1.0\jakarta.persistence-api-3.1.0.jar;C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\2.0.1\jakarta.transaction-api-2.0.1.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.5.3.Final\jboss-logging-3.5.3.Final.jar;C:\Users\<USER>\.m2\repository\org\hibernate\common\hibernate-commons-annotations\6.0.6.Final\hibernate-commons-annotations-6.0.6.Final.jar;C:\Users\<USER>\.m2\repository\io\smallrye\jandex\3.1.2\jandex-3.1.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.6.0\classmate-1.6.0.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\4.0.5\jaxb-runtime-4.0.5.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-core\4.0.5\jaxb-core-4.0.5.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\4.0.5\txw2-4.0.5.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\4.1.2\istack-commons-runtime-4.1.2.jar;C:\Users\<USER>\.m2\repository\jakarta\inject\jakarta.inject-api\2.0.1\jakarta.inject-api-2.0.1.jar;C:\Users\<USER>\.m2\repository\org\antlr\antlr4-runtime\4.13.0\antlr4-runtime-4.13.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\3.2.5\spring-data-jpa-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\3.2.5\spring-data-commons-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\6.1.6\spring-orm-6.1.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\6.1.6\spring-context-6.1.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\6.1.6\spring-tx-6.1.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\6.1.6\spring-beans-6.1.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\6.1.6\spring-aspects-6.1.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\3.2.5\spring-boot-starter-web-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\3.2.5\spring-boot-starter-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\3.2.5\spring-boot-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\3.2.5\spring-boot-autoconfigure-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\3.2.5\spring-boot-starter-logging-3.2.5.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.4.14\logback-classic-1.4.14.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.4.14\logback-core-1.4.14.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.21.1\log4j-to-slf4j-2.21.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\2.0.13\jul-to-slf4j-2.0.13.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\3.2.5\spring-boot-starter-json-3.2.5.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.15.4\jackson-datatype-jdk8-2.15.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.4\jackson-datatype-jsr310-2.15.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.15.4\jackson-module-parameter-names-2.15.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\6.1.6\spring-web-6.1.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\6.1.6\spring-webmvc-6.1.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\6.1.6\spring-expression-6.1.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\3.2.5\spring-boot-starter-security-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\6.1.6\spring-aop-6.1.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\6.2.4\spring-security-config-6.2.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\6.2.4\spring-security-core-6.2.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\6.2.4\spring-security-crypto-6.2.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\6.2.4\spring-security-web-6.2.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\3.2.5\spring-boot-starter-validation-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.20\tomcat-embed-el-10.1.20.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\8.0.1.Final\hibernate-validator-8.0.1.Final.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-webflux\3.2.5\spring-boot-starter-webflux-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-reactor-netty\3.2.5\spring-boot-starter-reactor-netty-3.2.5.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-http\1.1.18\reactor-netty-http-1.1.18.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.109.Final\netty-resolver-dns-4.1.109.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.109.Final\netty-codec-dns-4.1.109.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.109.Final\netty-resolver-dns-native-macos-4.1.109.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-classes-macos\4.1.109.Final\netty-resolver-dns-classes-macos-4.1.109.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-epoll\4.1.109.Final\netty-transport-native-epoll-4.1.109.Final-linux-x86_64.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-core\1.1.18\reactor-netty-core-1.1.18.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-proxy\4.1.109.Final\netty-handler-proxy-4.1.109.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-socks\4.1.109.Final\netty-codec-socks-4.1.109.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webflux\6.1.6\spring-webflux-6.1.6.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.6.5\reactor-core-3.6.5.jar;C:\Users\<USER>\.m2\repository\org\postgresql\postgresql\42.6.2\postgresql-42.6.2.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.31.0\checker-qual-3.31.0.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.32\lombok-1.18.32.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\s3\2.20.11\s3-2.20.11.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\aws-xml-protocol\2.20.11\aws-xml-protocol-2.20.11.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\aws-query-protocol\2.20.11\aws-query-protocol-2.20.11.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\protocol-core\2.20.11\protocol-core-2.20.11.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\arns\2.20.11\arns-2.20.11.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\profiles\2.20.11\profiles-2.20.11.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\sdk-core\2.20.11\sdk-core-2.20.11.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\auth\2.20.11\auth-2.20.11.jar;C:\Users\<USER>\.m2\repository\software\amazon\eventstream\eventstream\1.0.1\eventstream-1.0.1.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\http-client-spi\2.20.11\http-client-spi-2.20.11.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\regions\2.20.11\regions-2.20.11.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\annotations\2.20.11\annotations-2.20.11.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\utils\2.20.11\utils-2.20.11.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\aws-core\2.20.11\aws-core-2.20.11.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\metrics-spi\2.20.11\metrics-spi-2.20.11.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\json-utils\2.20.11\json-utils-2.20.11.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\third-party-jackson-core\2.20.11\third-party-jackson-core-2.20.11.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\endpoints-spi\2.20.11\endpoints-spi-2.20.11.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\apache-client\2.20.11\apache-client-2.20.11.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\netty-nio-client\2.20.11\netty-nio-client-2.20.11.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http2\4.1.109.Final\netty-codec-http2-4.1.109.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.109.Final\netty-codec-4.1.109.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.109.Final\netty-common-4.1.109.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.109.Final\netty-buffer-4.1.109.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-epoll\4.1.109.Final\netty-transport-classes-epoll-4.1.109.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\3.2.5\spring-boot-starter-tomcat-3.2.5.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.20\tomcat-embed-core-10.1.20.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.20\tomcat-embed-websocket-10.1.20.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\6.1.6\spring-core-6.1.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\6.1.6\spring-jcl-6.1.6.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.5.2.Final\mapstruct-1.5.2.Final.jar;C:\Users\<USER>\.m2\repository\io\jsonwebtoken\jjwt\0.9.1\jjwt-0.9.1.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.15.4\jackson-databind-2.15.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.15.4\jackson-annotations-2.15.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.15.4\jackson-core-2.15.4.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-starter-webmvc-api\2.6.0\springdoc-openapi-starter-webmvc-api-2.6.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-starter-common\2.6.0\springdoc-openapi-starter-common-2.6.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-core-jakarta\2.2.22\swagger-core-jakarta-2.2.22.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-annotations-jakarta\2.2.22\swagger-annotations-jakarta-2.2.22.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-models-jakarta\2.2.22\swagger-models-jakarta-2.2.22.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.15.4\jackson-dataformat-yaml-2.15.4.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-starter-webmvc-ui\2.6.0\springdoc-openapi-starter-webmvc-ui-2.6.0.jar;C:\Users\<USER>\.m2\repository\org\webjars\swagger-ui\5.17.14\swagger-ui-5.17.14.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.2\jakarta.xml.bind-api-4.0.2.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\2.1.3\jakarta.activation-api-2.1.3.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-configuration-processor\3.2.5\spring-boot-configuration-processor-3.2.5.jar;C:\Users\<USER>\.m2\repository\com\microsoft\sqlserver\mssql-jdbc\12.4.2.jre11\mssql-jdbc-12.4.2.jre11.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-actuator\3.2.5\spring-boot-starter-actuator-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\3.2.5\spring-boot-actuator-autoconfigure-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\3.2.5\spring-boot-actuator-3.2.5.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-observation\1.12.5\micrometer-observation-1.12.5.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-commons\1.12.5\micrometer-commons-1.12.5.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-jakarta9\1.12.5\micrometer-jakarta9-1.12.5.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi\5.2.5\poi-5.2.5.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.15.0\commons-io-2.15.0.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\SparseBitSet\1.3\SparseBitSet-1.3.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.21.1\log4j-api-2.21.1.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml\5.2.5\poi-ooxml-5.2.5.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml-lite\5.2.5\poi-ooxml-lite-5.2.5.jar;C:\Users\<USER>\.m2\repository\org\apache\xmlbeans\xmlbeans\5.2.0\xmlbeans-5.2.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.25.0\commons-compress-1.25.0.jar;C:\Users\<USER>\.m2\repository\com\github\virtuald\curvesapi\1.08\curvesapi-1.08.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-mail\3.2.5\spring-boot-starter-mail-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\6.1.6\spring-context-support-6.1.6.jar;C:\Users\<USER>\.m2\repository\org\eclipse\angus\jakarta.mail\2.0.3\jakarta.mail-2.0.3.jar;C:\Users\<USER>\.m2\repository\org\eclipse\angus\angus-activation\2.0.2\angus-activation-2.0.2.jar;C:\Users\<USER>\.m2\repository\jakarta\mail\jakarta.mail-api\2.0.0\jakarta.mail-api-2.0.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-cache\3.2.5\spring-boot-starter-cache-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis\3.2.5\spring-boot-starter-data-redis-3.2.5.jar;C:\Users\<USER>\.m2\repository\io\lettuce\lettuce-core\6.3.2.RELEASE\lettuce-core-6.3.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-redis\3.2.5\spring-data-redis-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-keyvalue\3.2.5\spring-data-keyvalue-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\6.1.6\spring-oxm-6.1.6.jar;C:\Users\<USER>\.m2\repository\com\ccavenue\ccavenue\1.0.0\ccavenue-1.0.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-registry-prometheus\1.12.5\micrometer-registry-prometheus-1.12.5.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-core\1.12.5\micrometer-core-1.12.5.jar;C:\Users\<USER>\.m2\repository\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar;C:\Users\<USER>\.m2\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_common\0.16.0\simpleclient_common-0.16.0.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient\0.16.0\simpleclient-0.16.0.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_tracer_otel\0.16.0\simpleclient_tracer_otel-0.16.0.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_tracer_common\0.16.0\simpleclient_tracer_common-0.16.0.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_tracer_otel_agent\0.16.0\simpleclient_tracer_otel_agent-0.16.0.jar;C:\Users\<USER>\.m2\repository\com\google\firebase\firebase-admin\9.3.0\firebase-admin-9.3.0.jar;C:\Users\<USER>\.m2\repository\com\google\api-client\google-api-client\2.4.0\google-api-client-2.4.0.jar;C:\Users\<USER>\.m2\repository\com\google\oauth-client\google-oauth-client\1.35.0\google-oauth-client-1.35.0.jar;C:\Users\<USER>\.m2\repository\com\google\auth\google-auth-library-credentials\1.23.0\google-auth-library-credentials-1.23.0.jar;C:\Users\<USER>\.m2\repository\com\google\http-client\google-http-client-gson\1.44.1\google-http-client-gson-1.44.1.jar;C:\Users\<USER>\.m2\repository\com\google\http-client\google-http-client-apache-v2\1.44.1\google-http-client-apache-v2-1.44.1.jar;C:\Users\<USER>\.m2\repository\com\google\api-client\google-api-client-gson\2.4.0\google-api-client-gson-2.4.0.jar;C:\Users\<USER>\.m2\repository\com\google\http-client\google-http-client\1.44.1\google-http-client-1.44.1.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.23.0\error_prone_annotations-2.23.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\2.8\j2objc-annotations-2.8.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-context\1.60.1\grpc-context-1.60.1.jar;C:\Users\<USER>\.m2\repository\io\opencensus\opencensus-api\0.31.1\opencensus-api-0.31.1.jar;C:\Users\<USER>\.m2\repository\io\opencensus\opencensus-contrib-http-util\0.31.1\opencensus-contrib-http-util-0.31.1.jar;C:\Users\<USER>\.m2\repository\com\google\api\api-common\2.31.0\api-common-2.31.0.jar;C:\Users\<USER>\.m2\repository\com\google\auto\value\auto-value-annotations\1.10.4\auto-value-annotations-1.10.4.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\com\google\auth\google-auth-library-oauth2-http\1.23.0\google-auth-library-oauth2-http-1.23.0.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\google-cloud-storage\2.38.0\google-cloud-storage-2.38.0.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.2\failureaccess-1.0.2.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\http-client\google-http-client-jackson2\1.44.1\google-http-client-jackson2-1.44.1.jar;C:\Users\<USER>\.m2\repository\com\google\apis\google-api-services-storage\v1-rev20240319-2.0.0\google-api-services-storage-v1-rev20240319-2.0.0.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.10.1\gson-2.10.1.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\google-cloud-core\2.38.0\google-cloud-core-2.38.0.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\google-cloud-core-http\2.38.0\google-cloud-core-http-2.38.0.jar;C:\Users\<USER>\.m2\repository\com\google\http-client\google-http-client-appengine\1.44.1\google-http-client-appengine-1.44.1.jar;C:\Users\<USER>\.m2\repository\com\google\api\gax-httpjson\2.48.0\gax-httpjson-2.48.0.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\google-cloud-core-grpc\2.38.0\google-cloud-core-grpc-2.38.0.jar;C:\Users\<USER>\.m2\repository\com\google\api\gax\2.48.0\gax-2.48.0.jar;C:\Users\<USER>\.m2\repository\com\google\api\gax-grpc\2.48.0\gax-grpc-2.48.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-inprocess\1.62.2\grpc-inprocess-1.62.2.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-alts\1.62.2\grpc-alts-1.62.2.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-grpclb\1.62.2\grpc-grpclb-1.62.2.jar;C:\Users\<USER>\.m2\repository\org\conscrypt\conscrypt-openjdk-uber\2.5.2\conscrypt-openjdk-uber-2.5.2.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-auth\1.62.2\grpc-auth-1.62.2.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\proto-google-iam-v1\1.34.0\proto-google-iam-v1-1.34.0.jar;C:\Users\<USER>\.m2\repository\com\google\protobuf\protobuf-java\3.25.3\protobuf-java-3.25.3.jar;C:\Users\<USER>\.m2\repository\com\google\protobuf\protobuf-java-util\3.25.3\protobuf-java-util-3.25.3.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-core\1.62.2\grpc-core-1.62.2.jar;C:\Users\<USER>\.m2\repository\com\google\android\annotations\4.1.1.4\annotations-4.1.1.4.jar;C:\Users\<USER>\.m2\repository\org\codehaus\mojo\animal-sniffer-annotations\1.23\animal-sniffer-annotations-1.23.jar;C:\Users\<USER>\.m2\repository\io\perfmark\perfmark-api\0.27.0\perfmark-api-0.27.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-protobuf\1.62.2\grpc-protobuf-1.62.2.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-protobuf-lite\1.62.2\grpc-protobuf-lite-1.62.2.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\proto-google-common-protos\2.39.0\proto-google-common-protos-2.39.0.jar;C:\Users\<USER>\.m2\repository\org\threeten\threetenbp\1.6.9\threetenbp-1.6.9.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\proto-google-cloud-storage-v2\2.38.0-alpha\proto-google-cloud-storage-v2-2.38.0-alpha.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\grpc-google-cloud-storage-v2\2.38.0-alpha\grpc-google-cloud-storage-v2-2.38.0-alpha.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\gapic-google-cloud-storage-v2\2.38.0-alpha\gapic-google-cloud-storage-v2-2.38.0-alpha.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-api\1.62.2\grpc-api-1.62.2.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-netty-shaded\1.62.2\grpc-netty-shaded-1.62.2.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-util\1.62.2\grpc-util-1.62.2.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-stub\1.62.2\grpc-stub-1.62.2.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-googleapis\1.62.2\grpc-googleapis-1.62.2.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-xds\1.62.2\grpc-xds-1.62.2.jar;C:\Users\<USER>\.m2\repository\io\opencensus\opencensus-proto\0.2.0\opencensus-proto-0.2.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-services\1.62.2\grpc-services-1.62.2.jar;C:\Users\<USER>\.m2\repository\com\google\re2j\re2j\1.7\re2j-1.7.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-rls\1.62.2\grpc-rls-1.62.2.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\google-cloud-firestore\3.21.1\google-cloud-firestore-3.21.1.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\proto-google-cloud-firestore-v1\3.21.1\proto-google-cloud-firestore-v1-3.21.1.jar;C:\Users\<USER>\.m2\repository\com\google\cloud\proto-google-cloud-firestore-bundle-v1\3.21.1\proto-google-cloud-firestore-bundle-v1-3.21.1.jar;C:\Users\<USER>\.m2\repository\io\opencensus\opencensus-contrib-grpc-util\0.31.1\opencensus-contrib-grpc-util-0.31.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\33.1.0-jre\guava-33.1.0-jre.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.13\slf4j-api-2.0.13.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.109.Final\netty-codec-http-4.1.109.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.109.Final\netty-handler-4.1.109.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.109.Final\netty-resolver-4.1.109.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.109.Final\netty-transport-native-unix-common-4.1.109.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.109.Final\netty-transport-4.1.109.Final.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.14.13\byte-buddy-1.14.13.jar;C:\Users\<USER>\.m2\repository\com\google\zxing\core\3.5.2\core-3.5.2.jar;C:\Users\<USER>\.m2\repository\com\google\zxing\javase\3.5.2\javase-3.5.2.jar;C:\Users\<USER>\.m2\repository\com\beust\jcommander\1.82\jcommander-1.82.jar;C:\Users\<USER>\.m2\repository\com\github\jai-imageio\jai-imageio-core\1.4.0\jai-imageio-core-1.4.0.jar;C:\Users\<USER>\.m2\repository\org\modelmapper\modelmapper\3.2.0\modelmapper-3.2.0.jar;C:\Users\<USER>\.m2\repository\com\razorpay\razorpay-java\1.4.8\razorpay-java-1.4.8.jar;C:\Users\<USER>\.m2\repository\org\json\json\20231013\json-20231013.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\okhttp\4.12.0\okhttp-4.12.0.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio\3.6.0\okio-3.6.0.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio-jvm\3.6.0\okio-jvm-3.6.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.9.23\kotlin-stdlib-common-1.9.23.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.9.23\kotlin-stdlib-jdk8-1.9.23.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib\1.9.23\kotlin-stdlib-1.9.23.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\13.0\annotations-13.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.9.23\kotlin-stdlib-jdk7-1.9.23.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\logging-interceptor\4.12.0\logging-interceptor-4.12.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-text\1.3\commons-text-1.3.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.13.0\commons-lang3-3.13.0.jar;C:\Users\<USER>\.m2\repository\javax\json\javax.json-api\1.1.4\javax.json-api-1.1.4.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 264241152                                 {product} {ergonomic}
     bool ManagementServer                         = true                                      {product} {command line}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4211081216                                {product} {ergonomic}
   size_t MaxNewSize                               = 2524971008                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 4096                                   {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 0                                      {pd product} {ergonomic}
     bool ProfileInterpreter                       = false                                  {pd product} {command line}
    uintx ProfiledCodeHeapSize                     = 0                                      {pd product} {ergonomic}
     bool ShowCodeDetailsInExceptionMessages       = true                                   {manageable} {command line}
   size_t SoftMaxHeapSize                          = 4211081216                             {manageable} {ergonomic}
     intx TieredStopAtLevel                        = 1                                         {product} {command line}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=D:/sts-4.22.1.RELEASE//plugins/org.eclipse.justj.openjdk.hotspot.jre.full.win32.x86_64_17.0.11.v20240426-1830/jre/bin/server;D:/sts-4.22.1.RELEASE//plugins/org.eclipse.justj.openjdk.hotspot.jre.full.win32.x86_64_17.0.11.v20240426-1830/jre/bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\Common Files\Oracle\Java\javapath;C:\ProgramData\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\PuTTY\;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\Git\cmd;C:\Users\<USER>\anaconda3;C:\Users\<USER>\anaconda3\Scripts;C:\Users\<USER>\anaconda3\Library\bin;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.12;D:\flutter\bin;C:\Program Files\GTK3-Runtime Win64\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\mongosh\;D:\sts-4.22.1.RELEASE;
USERNAME=PraneethReddyThippir
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 154 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5438)
OS uptime: 4 days 4:50 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 154 stepping 3 microcode 0x429, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, hv, rdtscp, rdpid, fsrm, f16c, cet_ibt, cet_ss
Processor Information for processor 0
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 1491
Processor Information for processor 1
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 1491
Processor Information for processor 2
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 3
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 4
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 5
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 6
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 7
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 8
  Max Mhz: 2100, Current Mhz: 1500, Mhz Limit: 1491
Processor Information for processor 9
  Max Mhz: 2100, Current Mhz: 1500, Mhz Limit: 1491
Processor Information for processor 10
  Max Mhz: 2100, Current Mhz: 1500, Mhz Limit: 1491
Processor Information for processor 11
  Max Mhz: 2100, Current Mhz: 1500, Mhz Limit: 1491
Processor Information for processor 12
  Max Mhz: 2100, Current Mhz: 1500, Mhz Limit: 1491
Processor Information for processor 13
  Max Mhz: 2100, Current Mhz: 1500, Mhz Limit: 1491
Processor Information for processor 14
  Max Mhz: 2100, Current Mhz: 1500, Mhz Limit: 1491
Processor Information for processor 15
  Max Mhz: 2100, Current Mhz: 1500, Mhz Limit: 1491

Memory: 4k page, system-wide physical 16059M (1207M free)
TotalPageFile size 33239M (AvailPageFile size 7448M)
current process WorkingSet (physical memory assigned to process): 527M, peak: 527M
current process commit charge ("private bytes"): 582M, peak: 582M

vm_info: Java HotSpot(TM) 64-Bit Server VM (21.0.3+7-LTS-152) for windows-amd64 JRE (21.0.3+7-LTS-152), built on 2024-03-11T17:42:26Z by "mach5one" with MS VC++ 17.6 (VS2022)

END.
