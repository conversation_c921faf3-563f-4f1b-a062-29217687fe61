package com.aig.aigone.daycare.dto;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
public class BookingDTO {
	
	
	private Integer frequency;

	private boolean infected;

	private Integer stationId;
	private Integer bedTypeId;
	private Integer category;
	private String uhid;
	private List<BookingTimeDTO> slotList; 
//	private String createdBy;
//    private String modifiedBy;
//
//    private LocalDateTime createdAt;
//    private LocalDateTime modifiedAt;
	private String createdBy;
	private String alternativeMobileNumber;
}
