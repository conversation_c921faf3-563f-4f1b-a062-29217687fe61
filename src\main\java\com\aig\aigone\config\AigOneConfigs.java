package com.aig.aigone.config;

import java.io.IOException;
import java.io.InputStream;

import javax.net.ssl.SSLException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;

import com.aig.aigone.common.AigOneUtil;
import com.aig.aigone.common.ErrorCodes;
import com.aig.aigone.exception.AigOneException;
import com.aig.aigone.service.CacheService;
import com.aig.aigone.service.HisService;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import com.google.firebase.messaging.FirebaseMessaging;

import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.ssl.util.InsecureTrustManagerFactory;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;

@Slf4j
@Configuration
public class AigOneConfigs {

	@Value("${aig.sms.baseurl}")
	private String smsBaseUrl;

	@Value("${aig.sms.X-Authorization}")
	private String xAuthorization;

	@Value("${aig.sms.Authorization}")
	private String authorization;

	@Value("${aig.his.baseurl}")
	private String hisBaseUrl;

	@Value("${aig.icare.baseurl}")
	private String iCareBaseUrl;

	@Value("${aig.ris.baseurl}")
	private String risBaseUrl;

	@Value("${aig.firebase.config}")
	private String firebaseConfigPath;

	@Value("${aig.emr.baseurl}")
	private String emrBaseUrl;

	@Value("${aig.dialysis.baseurl}")
	private String dialysisBaseUrl;

	@Autowired
	private CacheService cacheService;

	@Autowired
	private HisService hisService;

	@Value("${aig.python.pdfBaseUrl}")
	private String pythonPdfUrl;

	@Value("${aig.airteliq.url}")
	private String airteliqUrl;

	@Value("${aig.airteliq.apiusername}")
	private String apiUsername;

	@Value("${aig.airteliq.apipassword}")
	private String apiPassword;

	@Value("${aig.simply5.create}")
	private String baseUrlSimply5Create;
	
	@Value("${aig.simply5.revoke}")
	private String baseUrlSimply5Revoke;

	@Value("${aig.simply5.apikey}")
	private String baseUrlSimply5Apikey;
	
	
	@Value("${aig.oneradius.createapi}")
	private String baseUrlOneRadiusCreate;
	
	@Value("${aig.oneradius.revokeapi}")
	private String baseUrlOneRadiusRevoke;

	@Value("${aig.oneradius.apikey}")
	private String baseUrlOneRadiusApikey;
	
	
    @Value("${aig.oneradius.smsApiUrl}")
    private String smsApiUrl;

    @Value("${aig.oneradius.authHeader}")
    private String authHeader;

    
    @Value("${aig.oneradius.airteliq.apiusername}")
	private String apiUsernameForOneRadius;

	@Value("${aig.oneradius.airteliq.apipassword}")
	private String apiPasswordForOneRadius;
    
	@Value("${aig.oneradius.airteliq.url}")
	private String airteliqUrlOneradius;
    
	@Bean
	WebClient airtelWhatsAppClientForOneradius(WebClient.Builder webClientBuilder) {
		return webClientBuilder.baseUrl(airteliqUrlOneradius)
				.defaultHeaders(headers -> headers.setBasicAuth(apiUsernameForOneRadius, apiPasswordForOneRadius)).build();
	}
    
	
    @Bean
   WebClient airtelSmsWebClientForOneRadius(WebClient.Builder builder) {
        return builder
                .baseUrl(smsApiUrl)
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader(HttpHeaders.AUTHORIZATION, authHeader)
                .build();
    }
	@Bean
	WebClient oneRadiusClientCreate(WebClient.Builder webClientBuilder) {
		return webClientBuilder.baseUrl(baseUrlOneRadiusCreate)
				.defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + baseUrlOneRadiusApikey).build();
	}
	
	@Bean
	WebClient oneRadiusClientRevoke(WebClient.Builder webClientBuilder) {
		return webClientBuilder.baseUrl(baseUrlOneRadiusRevoke)
				.defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + baseUrlOneRadiusApikey).build();
	}
	


	@Bean
	WebClient simply5ClientCreate(WebClient.Builder webClientBuilder) {
		return webClientBuilder.baseUrl(baseUrlSimply5Create)
				.defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + baseUrlSimply5Apikey).build();
	}
	
	@Bean
	WebClient simply5ClientRevoke(WebClient.Builder webClientBuilder) {
		return webClientBuilder.baseUrl(baseUrlSimply5Revoke)
				.defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + baseUrlSimply5Apikey).build();
	}

//	@Bean
//	WebClient simply5Client(WebClient.Builder webClientBuilder) {
//		return webClientBuilder
//				.baseUrl(baseUrlSimply5)
//				.defaultHeader("x-api-key", baseUrlSimply5Apikey)
//				.build();
//	}
//	
	@Bean
	WebClient airtelWhatsAppClient(WebClient.Builder webClientBuilder) {
		return webClientBuilder.baseUrl(airteliqUrl)
				.defaultHeaders(headers -> headers.setBasicAuth(apiUsername, apiPassword)).build();
	}

	@Bean
	WebClient smsClient(WebClient.Builder webClientBuilder) {
		return webClientBuilder.baseUrl(smsBaseUrl)
				// .filter((request, next) -> {
				// return next.exchange(request);
				// })
				.filter(errorHandler(AigOneUtil.CLIENT_SMS)).defaultHeaders(httpHeaders -> {
					httpHeaders.add("X-Authorization", xAuthorization);
					httpHeaders.add("Authorization", authorization);
				}).build();
	}

	@Bean
	WebClient hisClient(WebClient.Builder webClientBuilder) {
		return webClientBuilder.baseUrl(hisBaseUrl).filter(errorHandler(AigOneUtil.CLIENT_HIS))
		// .filter(buildRetryExchangeFilterFunction())
//				.filter((request, next) -> {
//					return next.exchange(request);
//				})
				.build();
	}

	@Bean
	WebClient iCareClient(WebClient.Builder webClientBuilder) {

		SslContext sslContext;
		try {
			sslContext = SslContextBuilder.forClient().trustManager(InsecureTrustManagerFactory.INSTANCE).build();
			HttpClient httpClient = HttpClient.create().secure(t -> t.sslContext(sslContext));
			// HttpClient httpClient = HttpClient.create()
			// .secure(ssl ->
			// ssl.sslContext(SslProvider.defaultClientProvider().getSslContext()));
			return webClientBuilder
//					.clientConnector(new ReactorClientHttpConnector(httpClient))
					.baseUrl(iCareBaseUrl).filter(errorHandler(AigOneUtil.CLIENT_ICARE)).filter((request, next) -> {
						String string = request.body().toString();
						return next.exchange(request);
					}).build();
		} catch (SSLException e) {
			e.printStackTrace();
		}
		return null;
	}

	@Bean
	WebClient risClient(WebClient.Builder webClientBuilder) {
		return webClientBuilder.baseUrl(risBaseUrl).filter(errorHandler(AigOneUtil.CLIENT_RIS)).build();
	}

	@Bean
	WebClient emrClient(WebClient.Builder webClientBuilder) {
		return webClientBuilder.baseUrl(emrBaseUrl).filter(errorHandler(AigOneUtil.CLIENT_EMR)).build();
	}

	@Bean
	WebClient dialysisClient(WebClient.Builder webClientBuilder) {
		return webClientBuilder.baseUrl(dialysisBaseUrl).filter(errorHandler(AigOneUtil.CLIENT_DIALYSIS)).build();
	}

	@Bean
	WebClient pythonFlaskClient(WebClient.Builder webClientBuilder) {
		return webClientBuilder.baseUrl(pythonPdfUrl).filter(errorHandler(AigOneUtil.CLIENT_PYTHON_FLASK)).build();
	}

	@Bean
	GoogleCredentials googleCredentials() {
		try {
			return GoogleCredentials.fromStream(new ClassPathResource(firebaseConfigPath).getInputStream());

//			return GoogleCredentials.fromStream(GoogleCredentials.fromStream(new ClassPathResource(firebaseConfigPath).getInputStream()));

//			if (firebaseProperties.getServiceAccount() != null) {
//				try( InputStream is = firebaseProperties.getServiceAccount().getInputStream()) {
//					return GoogleCredentials.fromStream(is);
//				}                
//			} 
//			else {
//				// Use standard credentials chain. Useful when running inside GKE
//				return GoogleCredentials.getApplicationDefault();
//			}
		} catch (IOException ioe) {
			throw new RuntimeException(ioe);
		}
	}

	@Bean
	FirebaseApp firebaseApp(GoogleCredentials credentials) {
		FirebaseOptions options = FirebaseOptions.builder().setCredentials(credentials).build();

		return FirebaseApp.initializeApp(options);
	}

	@Bean
	FirebaseMessaging firebaseMessaging(FirebaseApp firebaseApp) {
		return FirebaseMessaging.getInstance(firebaseApp);
	}

	public ExchangeFilterFunction errorHandler(String clientName) {
		return ExchangeFilterFunction.ofResponseProcessor(clientResponse -> {
			String methodName = clientResponse.request().getMethod().name();
			String path = clientResponse.request().getURI().getPath();
			switch (clientName) {
			case AigOneUtil.CLIENT_ICARE:
				break;
			case AigOneUtil.CLIENT_HIS:
				if (clientResponse.statusCode().value() == 401) {
					cacheService.updateHisAuthToken(AigOneUtil.CACHE_HIS, AigOneUtil.CACHE_KEY_HIS_LOGIN);
					return clientResponse.bodyToMono(String.class).flatMap(responseBody -> {
						log.error(String.format(
								"Request failed with methodName: %s, request: %s  status code: %d, response: %s",
								methodName, path, clientResponse.statusCode().value(), responseBody));
						return Mono.error(new AigOneException(ErrorCodes.INTERNAL_SERVER_ERROR.getMessage()));
					});
				}
				break;
			}

			if (clientResponse.statusCode().is5xxServerError()) {
				return clientResponse.bodyToMono(String.class).flatMap(responseBody -> {
					log.error(String.format(
							"Request failed with methodName: %s, request: %s  status code: %d, response: %s",
							methodName, path, clientResponse.statusCode().value(), responseBody));
					return Mono.error(new AigOneException(ErrorCodes.INTERNAL_SERVER_ERROR.getMessage()));
				});
			} else if (clientResponse.statusCode().is4xxClientError()) {
				return clientResponse.bodyToMono(String.class).flatMap(responseBody -> {
					log.error(String.format(
							"Request failed with methodName: %s, request: %s  status code: %d, response: %s",
							methodName, path, clientResponse.statusCode().value(), responseBody));
					return Mono.error(new AigOneException(ErrorCodes.INTERNAL_SERVER_ERROR.getMessage()));
				});
			} else {
//				return Mono.just(clientResponse);
				log.info(String.format("Response with methodName: %s, request: %s  status code: %d", methodName, path,
						clientResponse.statusCode().value()));
//				return clientResponse.bodyToMono(String.class).flatMap(responseBody->{
//					log.info(String.format("Response with methodName: %s, request: %s  status code: %d, response: %s", 
//							methodName,path, clientResponse.statusCode().value(),responseBody));
//					return Mono.just(clientResponse);
//				});
				return Mono.just(clientResponse);
			}
		});
	}

	private ExchangeFilterFunction logRequest() {
		return ExchangeFilterFunction.ofRequestProcessor(clientRequest -> {
			return Mono.just(clientRequest);
		});
	}

	// @Bean
	// protected WebClient.Builder webClientBuilder(LoggerService loggerService) {
	// org.eclipse.jetty.client.HttpClient httpClient = new
	// org.eclipse.jetty.client.HttpClient() {
	// @Override
	// public Request newRequest(URI uri) {
	// Request request = super.newRequest(uri);
	// return loggerService.logRequestResponse(request);
	// }
	// };
	// return WebClient.builder().clientConnector(new
	// JettyClientHttpConnector(httpClient));
	// }

	// private ExchangeFilterFunction logRequest() {
	// return ExchangeFilterFunction.ofRequestProcessor(clientRequest -> {
	// BodyInserter<?, ? super ClientHttpRequest> body = clientRequest.body();
	// return Mono.defer(() -> {
	//// HttpRequestLog requestLog = new HttpRequestLog();
	//// requestLog.setMethod(clientRequest.method().name());
	//// requestLog.setUrl(clientRequest.url().toString());
	//// requestLog.setHeaders(clientRequest.headers().toString());
	//// requestLog.setTimestamp(LocalDateTime.now());
	//
	// return clientRequest.body().map(dataBuffer -> {
	// String body = dataBuffer.toString();
	// requestLog.setBody(body);
	// httpRequestLogRepository.save(requestLog);
	// return dataBuffer;
	// }).then(Mono.just(clientRequest));
	// });
	// });
	// }
	//
	// private void logRequestDetails(ClientRequest clientRequest) {
	// clientRequest.body().
	//
	// clientRequest.body().subscribe(buffer -> {
	//// HttpRequestLog requestLog = new HttpRequestLog();
	//// requestLog.setMethod(clientRequest.method().name());
	//// requestLog.setUrl(clientRequest.url().toString());
	//// requestLog.setHeaders(clientRequest.headers().toString());
	//// requestLog.setBody(buffer.toString()); // Convert buffer to string as per
	// your logic
	//// requestLog.setTimestamp(LocalDateTime.now());
	////
	//// httpRequestLogRepository.save(requestLog);
	// });
	// }
	//
	//
//	    private ExchangeFilterFunction logResponse() {
//	        return ExchangeFilterFunction.ofResponseProcessor(clientResponse -> {
//	            clientResponse.body((clientHttpResponse, clientHttpResponseBody) -> clientHttpResponseBody
//	                    .map(dataBuffer -> dataBuffer.asInputStream())
//	                    .flatMap(inputStream -> {
//	                        String body = new String(inputStream.readAllBytes());
//	                        HttpResponseLog responseLog = new HttpResponseLog();
//	                        responseLog.setStatusCode(clientResponse.statusCode().value());
//	                        responseLog.setHeaders(clientResponse.headers().asHttpHeaders().toString());
//	                        responseLog.setBody(body);
//	                        responseLog.setTimestamp(LocalDateTime.now());
//	                        httpResponseLogRepository.save(responseLog);
//	                        return Mono.just(dataBuffer);
//	                    }));
//	            return Mono.just(clientResponse);
//	        });
//	    }

//	private ExchangeFilterFunction buildRetryExchangeFilterFunction() { 
//		return (request, next) -> next.exchange(request) 
//				.flatMap(clientResponse -> {
//					if(clientResponse.statusCode().value() == 401) {
//						cacheService.evictSingleCacheValue(AigOneUtil.CACHE_HIS, AigOneUtil.CACHE_KEY_HIS_LOGIN);
//						return Mono.error(new TokenException("Retry requested"));
//					}
//					return Mono.just(clientResponse);
//				})
//				.retryWhen(retryWhenAuthTokenInvalid(request));
//	}

	// {
	// if (foo.getStatus() == FooStatus.RETRY) {
	// return Mono.error(new SomeException("Retry requested"));
	// } else {
	// return Mono.just(foo);
	// }
	// }

//	private RetryBackoffSpec retryWhenAuthTokenInvalid(ClientRequest request) { 
//		return Retry.backoff(4, Duration.ofSeconds(2)) 
//				.filter(this::isAuthTokenInvalidException) 
//				.doBeforeRetry(rs->{
//					log.info("do Before Retry");
//					HisLoginResponseDto login = hisService.login();
//					ClientRequest.from(request)
//					.header("Authorization", "Bearer " + login.getAccess_token())
//					.build();
//				})
//				.onRetryExhaustedThrow((retryBackoffSpec, retrySignal) -> retrySignal.failure()); 
//	}
//
//	private RetryBackoffSpec retryWhenTooManyRequests() { 
//		return Retry.backoff(3, Duration.ofSeconds(2)) 
//				.filter(this::isAuthTokenInvalidException) 
//				.doBeforeRetry(rs->{
//					log.info("do Before Retry");
//					hisService.login();
//				}) 
//				.onRetryExhaustedThrow((retryBackoffSpec, retrySignal) -> retrySignal.failure()); 
//	}
//	private boolean isAuthTokenInvalidException(final Throwable throwable) { 
//		return throwable instanceof TokenException; 
//	} 
//
//	private void logRetryAttemptsWithErrorMessage() {
//
//	}

	@Bean
	public RestTemplate restTemplate() {
		return new RestTemplate();
	}
}
