!SESSION 2025-06-19 11:15:53.628 -----------------------------------------------
eclipse.buildId=4.35.0.202504180233
java.version=21.0.6
java.vendor=Eclipse Adoptium
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=en_IN
Framework arguments:  -product org.springframework.boot.ide.branding.sts4
Command-line arguments:  -os win32 -ws win32 -arch x86_64 -product org.springframework.boot.ide.branding.sts4 -data C:\Users\<USER>\Desktop\AIG-ONE-Backend

!ENTRY ch.qos.logback.classic 1 0 2025-06-19 11:15:56.043
!MESSAGE Activated before the state location was initialized. Retry after the state location is initialized.

!ENTRY org.eclipse.lemminx.uber-jar 4 0 2025-06-19 11:15:56.284
!MESSAGE bundle org.eclipse.lemminx.uber-jar:0.29.0 (328) Component descriptor entry 'OSGI-INF/*.xml' not found

!ENTRY ch.qos.logback.classic 1 0 2025-06-19 11:15:57.431
!MESSAGE Logback config file: C:\Users\<USER>\Desktop\AIG-ONE-Backend\.metadata\.plugins\org.eclipse.m2e.logback\logback.2.7.0.20241001-1350.xml

!ENTRY org.eclipse.ui 2 0 2025-06-19 11:15:57.937
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-06-19 11:15:57.937
!MESSAGE Commands should really have a category: plug-in='org.springframework.tooling.boot.ls', id='spring.initializr.addStarters', categoryId='org.eclipse.lsp4e.commandCategory'

!ENTRY org.eclipse.ui 2 0 2025-06-19 11:15:58.330
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-06-19 11:15:58.330
!MESSAGE Commands should really have a category: plug-in='org.springframework.tooling.boot.ls', id='spring.initializr.addStarters', categoryId='org.eclipse.lsp4e.commandCategory'

!ENTRY org.eclipse.mylyn.tasks.ui 2 0 2025-06-19 11:16:02.205
!MESSAGE No search provider was registered. Tasks search is not available.

!ENTRY org.eclipse.egit.ui 2 0 2025-06-19 11:16:02.924
!MESSAGE Warning: The environment variable HOME is not set. The following directory will be used to store the Git
user global configuration and to define the default location to store repositories: 'C:\Users\<USER>