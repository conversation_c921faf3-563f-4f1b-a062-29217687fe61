package com.aig.aigone.scheduler;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.stream.Collectors;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aig.aigone.fcm.dto.FcmEmployeeCouponsDTO;
import com.aig.aigone.fcm.entity.FcmEmployeeCoupons;
import com.aig.aigone.fcm.entity.FcmOrderDetail;
import com.aig.aigone.fcm.entity.FcmOrders;
import com.aig.aigone.fcm.repo.FcmEmployeeCouponsRepository;
import com.aig.aigone.fcm.repo.FcmOrderDetailsRepository;
import com.aig.aigone.fcm.repo.FcmOrdersRepository;
import com.aig.aigone.fcm.service.FcmEmployeeCouponsService;
import com.aig.aigone.model.entity.aigone.SystemSettingsEntity;
import com.aig.aigone.model.entity.aigone.UserEntity;
import com.aig.aigone.model.entity.dfcms.DFCOrderStatus;
import com.aig.aigone.repository.aigone.CouponCountRepository;
import com.aig.aigone.repository.aigone.SystemSettingsRepository;
import com.aig.aigone.repository.aigone.UserRepository;
import com.aig.aigone.repository.dfcms.CouponRepository;
import com.aig.aigone.repository.dfcms.DfcmsOrderStatusRepository;
import com.aig.aigone.service.dfcms.CouponUploadConfigService;
import com.aig.aigone.service.dfcms.UserRoleAssignService;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import jakarta.mail.util.ByteArrayDataSource;
import jakarta.persistence.EntityManager;

@Service
public class FcmCouponSchedular {

	private static final Logger logger = LoggerFactory.getLogger(FcmCouponSchedular.class);

	@Autowired
	@Qualifier("aigOneEntityManager")
	private EntityManager entityManager;

	@Autowired
	private UserRepository userRepository;

	@Autowired
	private FcmEmployeeCouponsRepository employeeCouponsRepository;

	@Autowired
	private FcmOrdersRepository dfcmsOrdersRepository;

	@Autowired
	private DfcmsOrderStatusRepository orderStatusRepository;

	@Autowired
	private FcmOrderDetailsRepository orderDetailsRepository;

	@Autowired
	private CouponUploadConfigService couponUploadConfigService;

	@Autowired
	private UserRoleAssignService userRoleAssignService;

	@Autowired
	private CouponRepository coupanRepository;

	@Autowired
	private CouponCountRepository couponCountRepository;

	@Autowired
	private JavaMailSender mailSender;

	@Autowired
	private SystemSettingsRepository systemRepo;

	@Autowired
	private FcmEmployeeCouponsService employeeCouponsService;

	private final TaskScheduler taskScheduler;
	@Value("${aig.sender.email}")
	private String senderEmail;

	private final Map<String, String> lastKnownCronExpressions = new ConcurrentHashMap<>();
	private final Map<String, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();

	@Scheduled(fixedRate = 60000) // Run every 60 seconds (1 minute)
	public void checkForCronUpdates() {
		SystemSettingsEntity setting = systemRepo.findByKey("COUPONS_SCHEDULE");

		if (setting != null) {
			String newCronExpression = setting.getValue();
			String lastCron = lastKnownCronExpressions.get("COUPONS_SCHEDULE");

			// If cron expression has changed, update the schedule
			if (newCronExpression != null && !newCronExpression.equals(lastCron)) {
				logger.info("Cron expression changed for COUPONS_SCHEDULE. Updating scheduler...");
				lastKnownCronExpressions.put("COUPONS_SCHEDULE", newCronExpression);
				scheduleTask("COUPONS_SCHEDULE", this::assignEmployeeCoupons);
			}
		}
	}

	public FcmCouponSchedular(SystemSettingsRepository systemRepo, TaskScheduler taskScheduler) {
		this.systemRepo = systemRepo;
		this.taskScheduler = taskScheduler;
		initializeSchedules();
	}

	private void initializeSchedules() {
		scheduleTask("COUPONS_SCHEDULE", this::assignEmployeeCoupons);
	}

	private void scheduleTask(String configKey, Runnable task) {
		SystemSettingsEntity setting = systemRepo.findByKey(configKey);
		String cronExpression = (setting != null && setting.getValue() != null) ? setting.getValue()
				: getDefaultCron(configKey);

		// Cancel existing task if present
		if (scheduledTasks.containsKey(configKey)) {
			scheduledTasks.get(configKey).cancel(true);
		}

		// Schedule new task
		ScheduledFuture<?> scheduledTask = taskScheduler.schedule(wrapWithErrorHandling(task, configKey),
				new CronTrigger(cronExpression));

		scheduledTasks.put(configKey, scheduledTask);
		logger.info("Scheduled {} with cron: {}", configKey, cronExpression);
	}

	private String getDefaultCron(String configKey) {
		// Define default crons for different keys
		return switch (configKey) {
		case "COUPONS_SCHEDULE" -> "0 0 3 * * *"; // 3 AM daily
		default -> "0 0 12 * * 1"; // Noon daily as fallback
		};
	}

	private Runnable wrapWithErrorHandling(Runnable task, String configKey) {
		return () -> {
			try {
				task.run();
			} catch (Exception e) {
				logger.error("Error executing task {}: {}", configKey, e.getMessage());
				// Add any additional error handling here
			}
		};
	}

	public void refreshSchedule(String configKey) {
		if (scheduledTasks.containsKey(configKey)) {
			scheduleTask(configKey, getTaskForConfigKey(configKey));
		}
	}

	public void refreshAllSchedules() {
		initializeSchedules();
	}

	private Runnable getTaskForConfigKey(String configKey) {
		return switch (configKey) {
		case "COUPONS_SCHEDULE" -> this::assignEmployeeCoupons;
		default -> () -> logger.warn("No task defined for key: {}", configKey);
		};
	}

	@Scheduled(fixedDelay = 60000)
	@Transactional
	public void processOrderRaisedCoupons() {
		logger.info("Starting process to check for expired fcmCoupons.");

		try {
			// Fetch 'OrderRaised' and 'Expired' statuses
			DFCOrderStatus orderRaisedStatus = orderStatusRepository.findByOrderStatus("OrderRaised")
					.orElseThrow(() -> new IllegalArgumentException("Order status 'OrderRaised' not found."));
			DFCOrderStatus expiredStatus = orderStatusRepository.findByOrderStatus("Expired")
					.orElseThrow(() -> new IllegalArgumentException("Order status 'Expired' not found."));

			// Retrieve all 'OrderRaised' orders
			List<FcmOrders> raisedOrders = dfcmsOrdersRepository.findByOrderStatus(orderRaisedStatus);
			LocalDateTime now = LocalDateTime.now();

			logger.info("Found {} orders with status 'OrderRaised'.", raisedOrders.size());

			for (FcmOrders order : raisedOrders) {
				// Check if order is older than 30 minutes
				if (order.getCreatedDate().isBefore(now.minusMinutes(30))) {
					logger.info("Processing order No: {}", order.getOrderNo());

					// Fetch order details
					List<FcmOrderDetail> orderDetails = orderDetailsRepository.findByOrder(order);

					if (orderDetails.isEmpty()) {
						logger.warn("No order details found for order No: {}. Skipping.", order.getOrderNo());
						continue;
					}

					// Determine employee ID
					String employeeId = determineEmployeeId(order, orderDetails);
					logger.info("Employee ID associated with order ID {}: {}", order.getId(), employeeId);

					// Calculate total coupons used
					int totalRequiredCoupons = calculateTotalCoupons(orderDetails);
					logger.info("Total coupons to be credited back for order ID {}: {}", order.getId(),
							totalRequiredCoupons);

					// Credit back coupons
					creditBackCoupons(employeeId, totalRequiredCoupons);

					// Update order details and order status to 'Expired'
					updateOrderDetailsToExpired(orderDetails, expiredStatus, now);
					updateOrderToExpired(order, expiredStatus, now);
				}
			}

			logger.info("Completed processing expired coupons.");
		} catch (Exception e) {
			logger.error("An error occurred while processing expired coupons: {}", e.getMessage(), e);
		}
	}

	private String determineEmployeeId(FcmOrders order, List<FcmOrderDetail> orderDetails) {
		return (order.getOrderBy() != null && !order.getOrderBy().isEmpty()) ? order.getOrderBy()
				: orderDetails.stream().map(FcmOrderDetail::getCreatedBy).findFirst()
						.orElseThrow(() -> new IllegalArgumentException(
								"Employee ID not found for order No: " + order.getOrderNo()));
	}

	private int calculateTotalCoupons(List<FcmOrderDetail> orderDetails) {
		return orderDetails.stream().mapToInt(detail -> detail.getCouponQty() * detail.getItemQty()).sum();
	}

	private void creditBackCoupons(String employeeId, int totalRequiredCoupons) {
		// Fetch user to determine paygroup
		UserEntity userEntity = userRepository.findByEmployeeIdAndActiveTrue(employeeId).orElseGet(() -> {
			logger.warn("User not found with employeeId: {}. Skipping coupon credit.", employeeId);
			return null;
		});

		if (userEntity == null) {
			return;
		}

		boolean isConsultant = "Consultant".equals(userEntity.getPayGroupName());
		logger.info("Processing coupon credit for {} (PayGroup: {})", employeeId, userEntity.getPayGroupName());

		// Fetch valid coupons for the current month
		LocalDateTime now = LocalDateTime.now();
		LocalDateTime startOfMonth = now.withDayOfMonth(1).toLocalDate().atStartOfDay();
		LocalDateTime endOfMonth = now.withDayOfMonth(now.getMonth().length(now.toLocalDate().isLeapYear()))
				.toLocalDate().atTime(23, 59, 59);

		List<FcmEmployeeCoupons> employeeCoupons = employeeCouponsRepository.findByEmpId(employeeId).stream().filter(
				coupon -> !coupon.getValidFrom().isAfter(endOfMonth) && !coupon.getValidTo().isBefore(startOfMonth))
				.collect(Collectors.toList());

		if (employeeCoupons.isEmpty()) {
			logger.warn("No valid coupons found for employee ID: {}.", employeeId);
			return;
		}

		int remainingCouponsToReverse = totalRequiredCoupons;

		// Handle exceeded coupons first for consultants
		if (isConsultant) {
			for (FcmEmployeeCoupons coupon : employeeCoupons) {
				if (remainingCouponsToReverse <= 0)
					break;
				int exceededAvailable = coupon.getExceededQty();
				int reverseExceeded = Math.min(remainingCouponsToReverse, exceededAvailable);
				if (reverseExceeded > 0) {
					coupon.setExceededQty(exceededAvailable - reverseExceeded);
					remainingCouponsToReverse -= reverseExceeded;
					// Reset "isDesclaimed" if exceededQty becomes zero
					if (coupon.getExceededQty() == 0) {
						coupon.setIsDesclaimed(false); // Verify field name (e.g., setIsDeclaimed)
					}

					employeeCouponsRepository.save(coupon);
					logger.info("Credited back {} exceeded coupons from coupon ID: {}", reverseExceeded,
							coupon.getId());
				}
			}
		}

		// Handle remaining coupons via availedQty (for both consultants and staff)
		for (FcmEmployeeCoupons coupon : employeeCoupons) {
			if (remainingCouponsToReverse <= 0)
				break;
			int availed = coupon.getAvailedQty();
			int reverseAvailed = Math.min(remainingCouponsToReverse, availed);
			if (reverseAvailed > 0) {
				coupon.setAvailedQty(availed - reverseAvailed);
				coupon.setBalanceQty(coupon.getBalanceQty() + reverseAvailed);
				remainingCouponsToReverse -= reverseAvailed;
				employeeCouponsRepository.save(coupon);
				logger.info("Credited back {} availed coupons to coupon ID: {}", reverseAvailed, coupon.getId());
			}
		}

		if (remainingCouponsToReverse > 0) {
			logger.error("Failed to fully credit back coupons for employee ID: {}. Remaining: {}", employeeId,
					remainingCouponsToReverse);
		}
	}

	private void updateOrderDetailsToExpired(List<FcmOrderDetail> orderDetails, DFCOrderStatus expiredStatus,
			LocalDateTime now) {
		for (FcmOrderDetail detail : orderDetails) {
			detail.setOrderStatus(expiredStatus);
			orderDetailsRepository.save(detail);
			logger.info("Updated order detail ID {} to status 'Expired'.", detail.getId());
		}
	}

	private void updateOrderToExpired(FcmOrders order, DFCOrderStatus expiredStatus, LocalDateTime now) {
		order.setOrderStatus(expiredStatus);
		order.setModifiedDate(now);
		dfcmsOrdersRepository.save(order);
		logger.info("Updated order ID {} to status 'Expired'.", order.getId());
	}

	@Scheduled(cron = "0 0 3 * * *") 
//	@Scheduled(cron = "0 0/1 * * * ?")
	@Transactional
	public void assignEmployeeCoupons() {
		logger.info("Starting monthly coupon assignment process with Consultants...");

		if (couponUploadConfigService.isExcelUploadEnabled()) {
			logger.info("Excel upload is enabled. Skipping coupon assignment.");
			return;
		}

		try {
			List<Object[]> couponData = entityManager.createNativeQuery(
					"""
																WITH consultant_cycle AS (
							    SELECT
							        CASE
							            WHEN EXTRACT(DAY FROM CURRENT_DATE) >= 26
							            THEN DATE_TRUNC('MONTH', CURRENT_DATE) + INTERVAL '25 days'
							            ELSE DATE_TRUNC('MONTH', CURRENT_DATE - INTERVAL '1 month') + INTERVAL '25 days'
							        END AS cycle_start,
							        (CASE
							            WHEN EXTRACT(DAY FROM CURRENT_DATE) >= 26
							            THEN DATE_TRUNC('MONTH', CURRENT_DATE) + INTERVAL '25 days'
							            ELSE DATE_TRUNC('MONTH', CURRENT_DATE - INTERVAL '1 month') + INTERVAL '25 days'
							        END) + INTERVAL '1 month' - INTERVAL '1 day' AS cycle_end
							)
							SELECT
							    u.employee_id,
							    u.doj,
							    u.separation_date,
							    CASE
							        WHEN u.restricted_coupon THEN 0  -- Priority check: override to 0 if restricted
							        ELSE
							            CASE
							                WHEN u.paygroup_name = 'Consultant' THEN
							                    CASE
							                        -- Handle separation date in current cycle
							                        WHEN u.separation_date IS NOT NULL
							                             AND u.separation_date >= cc.cycle_start
							                             AND u.separation_date <= cc.cycle_end
							                        THEN ROUND(
							                            (1500.0 / (DATE_PART('day', cc.cycle_end - cc.cycle_start) + 1))
							                            * (DATE_PART('day', u.separation_date - cc.cycle_start))
							                        )

							                        -- Handle DOJ in current cycle
							                        WHEN u.doj IS NOT NULL
							                             AND u.doj >= cc.cycle_start
							                             AND u.doj <= cc.cycle_end
							                        THEN ROUND(
							                            (1500.0 / (DATE_PART('day', cc.cycle_end - cc.cycle_start) + 1))
							                            * (DATE_PART('day', cc.cycle_end - u.doj))
							                        )

							                        -- Default for consultants
							                        ELSE 1500
							                    END
							                ELSE
							                    CASE
							                        -- Handle separation date for non-consultants
							                        WHEN u.separation_date IS NOT NULL
							                             AND u.separation_date >= DATE_TRUNC('MONTH', CURRENT_DATE)
							                             AND u.separation_date < DATE_TRUNC('MONTH', CURRENT_DATE) + INTERVAL '1 MONTH'
							                        THEN ROUND(
							                            (26.0 / DATE_PART('day', DATE_TRUNC('MONTH', CURRENT_DATE) + INTERVAL '1 MONTH' - INTERVAL '1 day' - DATE_TRUNC('MONTH', CURRENT_DATE) + INTERVAL '1 day'))
							                            * DATE_PART('day', u.separation_date - DATE_TRUNC('MONTH', CURRENT_DATE) + INTERVAL '1 day')
							                        )

							                        -- Handle DOJ for non-consultants
							                        WHEN u.doj IS NOT NULL
							                             AND u.doj >= DATE_TRUNC('MONTH', CURRENT_DATE)
							                             AND u.doj < DATE_TRUNC('MONTH', CURRENT_DATE) + INTERVAL '1 MONTH'
							                        THEN ROUND(
							                            (26.0 / DATE_PART('day', DATE_TRUNC('MONTH', CURRENT_DATE) + INTERVAL '1 MONTH' - INTERVAL '1 day' - DATE_TRUNC('MONTH', CURRENT_DATE) + INTERVAL '1 day'))
							                            * DATE_PART('day', DATE_TRUNC('MONTH', CURRENT_DATE) + INTERVAL '1 MONTH' - INTERVAL '1 day' - u.doj)
							                        )

							                        -- Default for non-consultants
							                        ELSE 26
							                    END
							            END
							    END AS coupon_count
							FROM public.t_user u
							CROSS JOIN consultant_cycle cc
							WHERE
							    (
							        u.separation_date IS NULL
							        OR
							        (
							            (u.paygroup_name = 'Consultant' AND u.separation_date >= cc.cycle_start)
							            OR
							            (u.paygroup_name != 'Consultant' AND u.separation_date >= DATE_TRUNC('MONTH', CURRENT_DATE))
							        )
							    )
							    AND u.active = TRUE
							    AND u.paygroup_name IN ('AHF', 'CKS', 'Intern', 'Management', 'Staff', 'EXT_CP', 'Trainee', 'Consultant');
																																		""")
					.getResultList();

			List<FcmEmployeeCouponsDTO> employeeCouponsDTOs = new ArrayList<>();

			Instant now = Instant.now();
			ZoneId zoneId = ZoneId.systemDefault();
			LocalDate today = LocalDate.ofInstant(now, zoneId);

			logger.info("Assigning coupons for employees...");

			for (Object[] row : couponData) {
				String empId = (String) row[0];
				Integer couponCount = ((Number) row[3]).intValue();

				Optional<UserEntity> userEntityOpt = userRepository.findByEmployeeIdAndActiveTrue(empId);
				if (userEntityOpt.isPresent()) {
					UserEntity user = userEntityOpt.get();
					String paygroup = user.getPayGroupName();

					LocalDateTime validFrom;
					LocalDateTime validTo;

					if ("Consultant".equalsIgnoreCase(paygroup)) {
						// Determine if we're before or after the 26th of the current month
						if (today.getDayOfMonth() >= 26) {
							// Current cycle is 26th of current month to 25th of next month
							validFrom = today.withDayOfMonth(26).atTime(1, 0, 0);
							validTo = today.plusMonths(1).withDayOfMonth(25).atTime(22, 59, 59, 999_000_000);
						} else {
							// Current cycle is 26th of previous month to 25th of current month
							validFrom = today.minusMonths(1).withDayOfMonth(26).atTime(1, 0, 0);
							validTo = today.withDayOfMonth(25).atTime(22, 59, 59, 999_000_000);
						}
					} else {
						// Non-consultants: 1st to last day of current month
						validFrom = today.withDayOfMonth(1).atTime(1, 0, 0);
						validTo = today.with(TemporalAdjusters.lastDayOfMonth()).atTime(22, 59, 59, 999_000_000);
					}


					// Check for overlapping coupon for the current period
					boolean overlappingCouponExists = employeeCouponsRepository
							.existsByEmpIdAndValidFromLessThanEqualAndValidToGreaterThanEqual(empId, validTo,
									validFrom);

					if (!overlappingCouponExists) {
						FcmEmployeeCouponsDTO dto = new FcmEmployeeCouponsDTO();
						dto.setEmpId(empId);
						dto.setCouponMasterId(1);
						dto.setValidFrom(validFrom);
						dto.setValidTo(validTo);
						dto.setCouponsQty(couponCount);
						dto.setOpeningQty(couponCount);
						dto.setAvailedQty(0);
						dto.setBalanceQty(couponCount);
						dto.setAdditionalQty(0);
						dto.setExceededQty(0);
						dto.setIsDesclaimed(false);
						dto.setStatus(true);
						dto.setRemarks("Monthly coupon assignment");

						// Set allowed quantity based on paygroup
						dto.setAllowedQty("Consultant".equalsIgnoreCase(paygroup) ? 1500 : 26);

						employeeCouponsDTOs.add(dto);
					}
				} else {
					logger.warn("User not found for employee ID: {}", empId);
				}

			}

			if (!employeeCouponsDTOs.isEmpty()) {
				employeeCouponsService.fcmAddEmployeeCoupons(employeeCouponsDTOs);
				logger.info("Completed monthly coupon assignment process.");
			} else {
				logger.info("No new coupons to assign.");
			}
		} catch (Exception e) {
			logger.error("An error occurred during the monthly coupon assignment: {}", e.getMessage(), e);
		}
	}

//	 @Scheduled(cron = "0 0/1 * * * *")
	@Scheduled(cron = "0 30 23 25 * ?")
	public void sendExceededQtyReport() {
		try {
			// Calculate validity period (26th of previous month to 25th of current month)
			LocalDateTime reportValidFrom = LocalDate.now().minusMonths(1).withDayOfMonth(26).atStartOfDay();
			LocalDateTime reportValidTo = LocalDate.now().withDayOfMonth(25).atTime(23, 59, 59);

			// Fetch exceeded coupons within the validity period
			List<FcmEmployeeCoupons> exceededCoupons = employeeCouponsRepository
					.findByExceededQtyAndValidityPeriod("Consultant", reportValidFrom, reportValidTo);

			if (!exceededCoupons.isEmpty()) {
				byte[] reportData = generateExceedanceReport(exceededCoupons, reportValidFrom.toLocalDate(),
						reportValidTo.toLocalDate());
				sendExceedanceEmail(reportData, reportValidFrom.toLocalDate(), reportValidTo.toLocalDate());
					logger.info("Exceeded quantity report sent successfully for consultants from {} to {}", 
			            reportValidFrom, reportValidTo);
			} else {
				logger.info("No exceeded quantities found for consultants in period {} to {}", reportValidFrom,
						reportValidTo);
			}
		} catch (Exception e) {
			logger.error("Failed to generate exceeded quantity report: {}", e.getMessage(), e);
		}
	}

	private byte[] generateExceedanceReport(List<FcmEmployeeCoupons> exceededCoupons, LocalDate validFrom,
			LocalDate validTo) throws IOException {
		Workbook workbook = new XSSFWorkbook();
		Sheet sheet = workbook.createSheet("Exceeded Coupons");

		// Create header row
		Row headerRow = sheet.createRow(0);
		String[] columns = { "Employee ID", "Valid From", "Valid To", "Exceeded Qty" };
		for (int i = 0; i < columns.length; i++) {
			Cell cell = headerRow.createCell(i);
			cell.setCellValue(columns[i]);
			cell.setCellStyle(getHeaderCellStyle(workbook));
		}

		// Add period information
		Row periodRow = sheet.createRow(1);
		periodRow.createCell(0).setCellValue("Reporting Period:");
		periodRow.createCell(1).setCellValue(validFrom + " to " + validTo);

		// Populate data rows
		int rowIdx = 1;
		for (FcmEmployeeCoupons coupon : exceededCoupons) {
			Row row = sheet.createRow(rowIdx++);
			row.createCell(0).setCellValue(coupon.getEmpId());
			row.createCell(1).setCellValue(coupon.getValidFrom().toString());
			row.createCell(2).setCellValue(coupon.getValidTo().toString());
			row.createCell(3).setCellValue(coupon.getExceededQty());
		}

		// Auto-size columns
		for (int i = 0; i < columns.length; i++) {
			sheet.autoSizeColumn(i);
		}

		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		workbook.write(outputStream);
		workbook.close();
		return outputStream.toByteArray();
	}

	private void sendExceedanceEmail(byte[] attachmentData, LocalDate validFrom, LocalDate validTo)
			throws MessagingException {
		MimeMessage message = mailSender.createMimeMessage();
		MimeMessageHelper helper = new MimeMessageHelper(message, true);

		SystemSettingsEntity receiverEmails = systemRepo.findByKey("COUPONS_EXCEEDANCE_RECEIVER_EMAIL");
		String toEmails = receiverEmails.getValue();
		helper.setFrom(senderEmail);
		helper.setTo(toEmails.split(","));

		SystemSettingsEntity ccEmails = systemRepo.findByKey("COUPONS_EXCEEDANCE_CC_EMAIL");
		String ccMails = ccEmails.getValue();
		if (ccMails != null && !ccMails.isEmpty()) {
			helper.setCc(ccMails.split(","));
		}

		String periodString = validFrom.format(DateTimeFormatter.ofPattern("MMM dd")) + " - "
				+ validTo.format(DateTimeFormatter.ofPattern("MMM dd, yyyy"));

		helper.setSubject(String.format("Coupon Exceedance Report - %s", periodString));
		helper.setText(
				String.format("Dear Team,\n\n" + "Please find attached the coupon exceedance report for consultants.\n"
						+ "Reporting Period: %s\n\n" + "Regards,\nOne AIG Team", periodString));

		helper.addAttachment("Consultant_Coupon_Exceedance.xlsx", new ByteArrayDataSource(attachmentData,
				"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"));

		mailSender.send(message);
		logger.info("Exceedance report email sent successfully");
	}

	private CellStyle getHeaderCellStyle(Workbook workbook) {
		CellStyle style = workbook.createCellStyle();
		Font font = workbook.createFont();
		font.setBold(true);
		style.setFont(font);
		return style;
	}

}