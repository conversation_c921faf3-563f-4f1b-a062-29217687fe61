package com.aig.aigone.service;

import java.security.SecureRandom;
import java.util.Random;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

@Service
public class OtpGenerator {

	private static final String CHARACTERS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

	public String generateOTP(int length) {
		Random random = new Random();
		StringBuilder otp = new StringBuilder(length);
		for (int i = 0; i < length; i++) {
			otp.append(random.nextInt(10));
		}
		return otp.toString();
	}

	public String generateTransactioId(int length) {
		Random random = new Random();
		StringBuilder otp = new StringBuilder(length);
		for (int i = 0; i < length; i++) {
			otp.append(random.nextInt(10));
		}
		return otp.toString();
	}

	public String generateTransactionId(int length) {
		return new SecureRandom().ints(length, 0, CHARACTERS.length())
				.mapToObj(CHARACTERS::charAt)
				.map(Object::toString)
				.collect(Collectors.joining());
	}

}