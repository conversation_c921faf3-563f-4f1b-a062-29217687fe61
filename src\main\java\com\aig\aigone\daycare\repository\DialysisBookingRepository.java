package com.aig.aigone.daycare.repository;

import java.time.LocalDate;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.aig.aigone.daycare.model.Booking;
import com.aig.aigone.daycare.model.DialysisBooking;
@Repository
public interface  DialysisBookingRepository extends JpaRepository<DialysisBooking, Long> {
	
	@Query("SELECT db FROM DialysisBooking db WHERE db.booking.id = :bookingId")
	List<DialysisBooking> findByBookingId(@Param("bookingId") Integer bookingId);
	
	@Query("SELECT d.booking  FROM DialysisBooking d WHERE d.booking.uhid = :uhid AND d.endDate >= :date")
	List<Booking> findByBookingIdAndEndDateBeforeOrEqual(@Param("uhid") String uhid, @Param("date") LocalDate date);
	
	@Query("SELECT db FROM DialysisBooking db WHERE db.booking.id = :bookingId")
	DialysisBooking findByBookingIdUnique(@Param("bookingId") Integer bookingId);

	@Query("SELECT db FROM DialysisBooking db WHERE db.booking.id IN :ids and db.isActive=true")
     List<DialysisBooking> getDialysisBookingDetailsByUHID(@Param("ids") List<Integer> ids);

	@Query(value="SELECT * FROM dc_dialysisbooking WHERE is_waiting = true;",nativeQuery = true)
	List<DialysisBooking> findByWaitingList();
 
	@Query("SELECT db FROM DialysisBooking db WHERE db.booking.id = :bookingId AND db.status='CANCELLED'")
	DialysisBooking getByBookingId(@Param("bookingId") Integer bookingId);
	
	@Query("SELECT db FROM DialysisBooking db WHERE db.booking.id = :bookingId ORDER BY db.id DESC LIMIT 1")
	DialysisBooking getRecentDialysisBooking(@Param("bookingId") Integer bookingId);
}
