package com.aig.aigone.daycare.service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;

import com.aig.aigone.daycare.dto.AvailabilityDTO;
import com.aig.aigone.daycare.dto.BookingDTO;
import com.aig.aigone.daycare.dto.CancelledAppointmentsListDTO;
import com.aig.aigone.daycare.dto.DialysisPatientDTO;
import com.aig.aigone.daycare.dto.DialysisSlotDTO;
import com.aig.aigone.daycare.dto.ExistingBookingDetailsDTO;
import com.aig.aigone.daycare.dto.RemainderDTO;
import com.aig.aigone.daycare.dto.RescheduleBookingDTO;
import com.aig.aigone.daycare.dto.WaitingListAppointmentsDTO;
import com.aig.aigone.daycare.dto.WaitingListDTO;
import com.aig.aigone.daycare.model.DailysisFrequency;
import com.aig.aigone.daycare.model.DailysisSlotTiming;
import com.aig.aigone.daycare.model.DialysisBooking;
import com.aig.aigone.daycare.model.PaymentMethod;
import com.aig.aigone.daycare.model.Slot;

public interface DialysisService {
	
	public  DialysisBooking getDialysisBooking(Integer bookingId);
	
	public Slot upcomingSlot(String uhid);
	
	public List<DialysisPatientDTO> getSlotByDate(LocalDate date);
	
	public List<Slot> getPatientBookedSlot(Integer bookingId);
	
	public String markArrival(Long slotid);

	public List<PaymentMethod> getPaymentCategory();

	public List<DailysisFrequency> getFrequency();

	public List<DailysisSlotTiming> getSlot();

	public boolean bookBed(BookingDTO bookingDetails);
	
	public List<DialysisPatientDTO> getAllBooking();
		
	public List<AvailabilityDTO> getMonthAvailability(LocalDate startDate, LocalDate endDate,Integer category);
	
	
	
	private List<List<Integer>> getDays(List<List<Integer>> badaList, List<Integer> psf, int count, int num) {
		if (psf.size() == count) {
			badaList.add(psf);
			return badaList;
		}
		if (num > 7) {
			return badaList;
		}
		
		List<Integer> temp = new ArrayList<Integer>(psf);
		temp.add(num);
		getDays(badaList, temp, count, num + 2);
		getDays(badaList, new ArrayList<Integer>(psf), count, num + 1);
		return badaList;
	}

	default List<List<Integer>> getWeekDayList(Integer frequency) {
		List<List<Integer>> badaList = new ArrayList<List<Integer>>();
		List<Integer> psf = new ArrayList<Integer>();
		return getDays(badaList,psf,frequency,2);
	}
	public List<ExistingBookingDetailsDTO> getExistingBookingDetails(String uhid);

//	public boolean rescheduleBooking(BookingDTO bookingDetails);
	public boolean rescheduleBooking(RescheduleBookingDTO rescheduleBookingDTO);
		 
	public boolean waitingListAppointment(@RequestBody WaitingListDTO bookingDetails);
	
	public List<WaitingListAppointmentsDTO> getWaitingListData();
	
	public List<CancelledAppointmentsListDTO> getCancelledAppointments();

	public void sendRemainderNotification(RemainderDTO remainderDTO);

	public List<DialysisBooking> getDialysisBookingDetailsByUHID(String uHID);

	public List<DialysisPatientDTO> getAppointmentsByDate(LocalDate startDate, LocalDate endDate);

	public String fetchAlternativeMobileNumber(Integer bookingId);

}
