package com.aig.aigone.common;

public enum ErrorCodes {

    UNAUTHORIZED_ACCESS("UNAUTH-001", "Access denied. Please log in."),
   
    INTERNAL_SERVER_ERROR("ISE-001", "An unexpected error occurred. Please try again later.");

    private final String code;
    private final String message;

    ErrorCodes(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
