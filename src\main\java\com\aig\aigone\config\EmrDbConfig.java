package com.aig.aigone.config;

import java.util.HashMap;
import java.util.Map;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;

import com.zaxxer.hikari.HikariDataSource;

@Configuration
@EnableJpaRepositories(basePackages = {"com.aig.aigone.repository.emr","com.aig.aigone.daycare.emr.repository","com.aig.aigone.daycare.emr.model"},entityManagerFactoryRef = "emrEntityManager", transactionManagerRef = "emrTransactionManager")
public class EmrDbConfig {


    @Bean(name = "emrDataSourceProperties")
    @ConfigurationProperties(prefix = "emr.datasource")
    DataSourceProperties emrDataSourceProperties() {
        return new DataSourceProperties();
    }

    //    @ConfigurationProperties("his.datasource.configuration")
    @Bean(name = "emrDataSource")
    DataSource emrDataSource(@Qualifier("emrDataSourceProperties") DataSourceProperties emrDataSourceProperties) {
        return emrDataSourceProperties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
    }
    
    @Bean
    LocalContainerEntityManagerFactoryBean emrEntityManager(EntityManagerFactoryBuilder builder,@Qualifier("emrDataSource") DataSource emrDataSource) {
//        LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
//        em.setDataSource(hisDataSource);
//        em.setPackagesToScan("com.aig.aigone.model.his.entity","com.aig.aigone.repository.his");
//        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
//        em.setJpaVendorAdapter(vendorAdapter);
////        HashMap<String, Object> properties = new HashMap<>();
////        properties.put("hibernate.hbm2ddl.auto", env.getProperty("hibernate.hbm2ddl.auto"));
////        properties.put("hibernate.dialect", env.getProperty("hibernate.dialect"));
////        em.setJpaPropertyMap(properties);
//        return em;
    	
    	Map<String, Object> properties = new HashMap<>();
        properties.put("hibernate.hbm2ddl.auto", "none");
//        properties.put("hibernate.dialect", "org.hibernate.dialect.PostgreSQLDialect");
        properties.put("hibernate.properties.hibernate.query.plan_cache_max_size", "1");
        properties.put("hibernate.properties.hibernate.plan_parameter_metadata_max_size", "1");
//        properties.put("hibernate.naming.physical-strategy", "org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy");
//        properties.put("hibernate.naming.implicit-strategy", "org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy");
        
        return builder.dataSource(emrDataSource)
                .packages("com.aig.aigone.repository.emr","com.aig.aigone.daycare.emr.repository","com.aig.aigone.daycare.emr.model")
                .persistenceUnit("emrDb")
                .properties(properties)
                .build();
    }
 
    @Bean
    PlatformTransactionManager emrTransactionManager(LocalContainerEntityManagerFactoryBean emrEntityManager) {
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(emrEntityManager.getObject());
        return transactionManager;
    }

}