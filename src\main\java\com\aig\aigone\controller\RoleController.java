package com.aig.aigone.controller;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.model.dto.RoleDto;
import com.aig.aigone.model.dto.RoleRequest;
import com.aig.aigone.service.RoleService;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequestMapping("/api/roles")
@RequiredArgsConstructor
@RestController
public class RoleController {
	private final RoleService roleService;

	@GetMapping
	public ResponseEntity<List<RoleDto>> getAllRoles() {
		return ResponseEntity.ok(roleService.getAllRoles());
	}

	@GetMapping("/{id}")
	public ResponseEntity<RoleDto> getRoleById(@PathVariable Long id) {
		return ResponseEntity.ok(roleService.getRoleById(id));
	}

	@PostMapping
	public ResponseEntity<RoleDto> createRole(@Valid @RequestBody RoleRequest request) {
		return new ResponseEntity<>(roleService.createRole(request), HttpStatus.CREATED);
	}

	@PutMapping("/{id}")
	public ResponseEntity<RoleDto> updateRole(@PathVariable Long id, @Valid @RequestBody RoleRequest request) {
		return ResponseEntity.ok(roleService.updateRole(id, request));
	}

	@DeleteMapping("/{id}")
	public ResponseEntity<Void> deleteRole(@PathVariable Long id) {
		roleService.deleteRole(id);
		return ResponseEntity.noContent().build();
	}

	@GetMapping("/search")
	public ResponseEntity<List<RoleDto>> searchRolesByName(@RequestParam("name") String name) {
		return ResponseEntity.ok(roleService.searchRolesByName(name));
	}
}