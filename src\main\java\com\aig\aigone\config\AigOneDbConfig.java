package com.aig.aigone.config;

import java.util.HashMap;
import java.util.Map;

import javax.sql.DataSource;

import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;

import com.zaxxer.hikari.HikariDataSource;

@Configuration
@EnableJpaRepositories(basePackages = {"com.aig.aigone.repository.aigone","com.aig.aigone.vp.entity","com.aig.aigone.vp.repo","com.aig.aigone.model.entity.aigone","com.aig.aigone.model.entity.dfcms","com.aig.aigone.repository.dfcms",
		"com.aig.aigone.roster.model.entity","com.aig.aigone.roster.repository","com.aig.aigone.daycare.model","com.aig.aigone.daycare.repository",
		"com.aig.aigone.labreport.model.entity","com.aig.aigone.labreport.repository","com.aig.aigone.ref.repo","com.aig.aigone.ref.entity","com.aig.aigone.dr.entity","com.aig.aigone.dr.repo","com.aig.aigone.fcm.repo","com.aig.aigone.fcm.entity",
		"com.aig.aigone.scheduler.entity","com.aig.aigone.scheduler.repository",
		"com.aig.aigone.prism.entity","com.aig.aigone.prism.repositories","com.aig.aigone.bloodbank.model.entity","com.aig.aigone.bloodbank.repository","com.aig.aigone.prism.forms.entity","com.aig.aigone.prism.forms.repositories"},entityManagerFactoryRef = "aigOneEntityManager", transactionManagerRef = "aigOneTransactionManager")

public class AigOneDbConfig {
	
	
	@Bean
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource")
    public DataSourceProperties firstDataSourceProperties() {
        return new DataSourceProperties();
    }

//    @Bean(name = "dataSource")
//    @Primary
//    @ConfigurationProperties("spring.datasource.configuration")
//    private HikariDataSource dataSource(DataSourceProperties firstDataSourceProperties) {
//        return firstDataSourceProperties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
//    }
    
    @Bean
    @Primary
    @ConfigurationProperties("spring.datasource.configuration")
    public DataSource aigOneDataSource(DataSourceProperties firstDataSourceProperties) {
        return firstDataSourceProperties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
    }
    
    @Bean
    @Primary
    public LocalContainerEntityManagerFactoryBean aigOneEntityManager(EntityManagerFactoryBuilder builder,DataSource aigOneDataSource) {
//        LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
//        em.setDataSource(aigOneDataSource);
//        em.setPackagesToScan("com.aig.aigone.model.entity","com.aig.aigone.repository");
////        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
////        em.setJpaVendorAdapter(vendorAdapter);
//        HashMap<String, Object> properties = new HashMap<>();
//        properties.put("hibernate.hbm2ddl.auto", "none");
////        properties.put("hibernate.dialect", " org.hibernate.dialect.PostgreSQLDialect");
////        properties.put("hibernate.physical_naming_strategy", "org.springframework.boot.orm.jpa.hibernate.PhysicalNamingStrategyStandardImpl");
////        properties.put("hibernate.implicit_naming_strategy", "org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl");
////        
//        em.setJpaPropertyMap(properties);
    	
    	Map<String, Object> properties = new HashMap<>();
        properties.put("hibernate.hbm2ddl.auto", "update");
        properties.put("hibernate.properties.hibernate.query.plan_cache_max_size", "1");
        properties.put("hibernate.properties.hibernate.plan_parameter_metadata_max_size", "1");
        properties.put("hibernate.dialect", "org.hibernate.dialect.PostgreSQLDialect");
        properties.put("hibernate.naming.physical-strategy", "org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy");
        properties.put("hibernate.naming.implicit-strategy", "org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy");
        
        return builder.dataSource(aigOneDataSource)
        .packages("com.aig.aigone.model.entity.aigone","com.aig.aigone.vp.entity","com.aig.aigone.repository.aigone","com.aig.aigone.model.entity.dfcms","com.aig.aigone.roster.model.entity","com.aig.aigone.roster.repository","com.aig.aigone.daycare.model","com.aig.aigone.daycare.repository","com.aig.aigone.labreport.model.entity","com.aig.aigone.labreport.repository","com.aig.aigone.ref.repo","com.aig.aigone.ref.entity","com.aig.aigone.dr.entity","com.aig.aigone.dr.repo"
        		,"com.aig.aigone.fcm.repo","com.aig.aigone.fcm.entity","com.aig.aigone.scheduler.entity","com.aig.aigone.scheduler.repository","com.aig.aigone.prism.entity","com.aig.aigone.prism.repositories","com.aig.aigone.bloodbank.model.entity","com.aig.aigone.bloodbank.repository","com.aig.aigone.prism.forms.entity","com.aig.aigone.prism.forms.repositories")
        .persistenceUnit("aigOne")
        .properties(properties).build();
        
//        return em;
    }
 
    @Bean
    @Primary
    public PlatformTransactionManager aigOneTransactionManager(LocalContainerEntityManagerFactoryBean aigOneEntityManager) {
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(aigOneEntityManager.getObject());
        return transactionManager;
    }

}