package com.aig.aigone.daycare.serviceimpl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import com.aig.aigone.daycare.dto.AvailabilityDTO;
import com.aig.aigone.daycare.dto.BookingDTO;
import com.aig.aigone.daycare.dto.BookingTimeDTO;
import com.aig.aigone.daycare.dto.PatientDTO;
import com.aig.aigone.daycare.dto.RescheduleBookingDTO;
import com.aig.aigone.daycare.dto.WaitingListDTO;
import com.aig.aigone.daycare.exception.NotBookedException;
import com.aig.aigone.daycare.exception.UhidNotFound;
import com.aig.aigone.daycare.model.Bed;
import com.aig.aigone.daycare.model.Booking;
import com.aig.aigone.daycare.model.DialysisBooking;
import com.aig.aigone.daycare.model.Slot;
import com.aig.aigone.daycare.repository.BedRepository;
import com.aig.aigone.daycare.repository.BookingRepository;
import com.aig.aigone.daycare.repository.DialysisBookingRepository;
import com.aig.aigone.daycare.repository.SlotRepository;
import com.aig.aigone.daycare.repository.SlotTimingRepository;
import com.aig.aigone.daycare.service.BookingService;
import com.aig.aigone.daycare.service.HisServiceDaycare;
import jakarta.transaction.Transactional;

@Component
public class BookingServiceImpl implements BookingService {
	private static final Logger logger = LoggerFactory.getLogger(BookingServiceImpl.class);

	@Autowired
	SlotRepository slotRepo;
	@Autowired
	BookingRepository bookingRepo;
	@Autowired
	BedRepository bedRepo;

	@Autowired
	SlotTimingRepository slotTimeRepo;

	@Autowired
	DialysisBookingRepository dialysisBookingRepo;

	@Autowired
	HisServiceDaycare hisService;

	@Override
	public List<AvailabilityDTO> getAvailibility(List<AvailabilityDTO> dateAndSlot, Integer stationId,
			Integer bedTypeId, Integer cat) {
		logger.info("Getting availability for stationId: {}, bedTypeId: {}, cat: {}", stationId, bedTypeId, cat);

		Integer totalBed = bedRepo.getBedCount(stationId, bedTypeId, cat);
		List<AvailabilityDTO> result = new ArrayList<AvailabilityDTO>(dateAndSlot);
		for (AvailabilityDTO datetime : result) {
			datetime.getSlot().forEach(slot -> {
				LocalDateTime startTime = datetime.getData().atTime(slot.getStartTime());
				LocalDateTime endTime = datetime.getData().atTime(slot.getEndTime());
				Integer occupiedBed = slotRepo.getTotalOccupiedBed(stationId, bedTypeId, cat, startTime, endTime);
				slot.setAvailability(occupiedBed < totalBed);
			});
		}
		logger.info("Availability check completed");
		return result;
	}

	@Transactional
	@Override
	public boolean bookBed(BookingDTO bookingDetails) {
		logger.info("Booking bed for UHID: {}", bookingDetails.getUhid());

		PatientDTO p = hisService.getPatientByUHID(bookingDetails.getUhid().split("\\.")[0],
				Integer.parseInt(bookingDetails.getUhid().split("\\.")[1]));
		if (p == null) {
			logger.error("Invalid UHID: {}", bookingDetails.getUhid());
			throw new UhidNotFound("Invalid UHID");
		}
		Booking book = new Booking();

		book.setUhid(bookingDetails.getUhid());
		book.setBookedOn(LocalDateTime.now());
		book.setCreatedAt(LocalDateTime.now());
		book.setModifiedAt(LocalDateTime.now());
		book.setCreatedBy(bookingDetails.getCreatedBy());
		book.setModifiedBy(bookingDetails.getCreatedBy());
		book.setAlternativeMobileNumber(bookingDetails.getAlternativeMobileNumber());
		Booking bookId = bookingRepo.save(book);

		DialysisBooking dailysisBooking = new DialysisBooking();
		List<BookingTimeDTO> timeList = bookingDetails.getSlotList();
		Collections.sort(timeList);
		//Viral Marker is captured 
		dailysisBooking.setInfected(bookingDetails.isInfected());
		dailysisBooking.setBooking(bookId);
		dailysisBooking.setStartDate(timeList.get(0).getStartTime().toLocalDate());
		dailysisBooking.setEndDate(timeList.get(timeList.size() - 1).getStartTime().toLocalDate());
		dailysisBooking.setFrequency(bookingDetails.getFrequency());
		
		dailysisBooking.setCreatedAt(LocalDateTime.now());
		dailysisBooking.setModifiedAt(LocalDateTime.now());
		dailysisBooking.setCreatedBy(bookingDetails.getCreatedBy());
		dailysisBooking.setModifiedBy(bookingDetails.getCreatedBy());
		dailysisBooking.setStatus("NEW");
		

		dialysisBookingRepo.save(dailysisBooking);

		for (BookingTimeDTO time : timeList) {
			Optional<Bed> availableBed = getAvailableBed(bookingDetails.getStationId(), bookingDetails.getBedTypeId(),
					bookingDetails.getCategory(), time.getStartTime(), time.getEndTime());
			availableBed.ifPresentOrElse(bed -> {
				Slot slot = new Slot();
				slot.setBed(bed);
				slot.setBookingId(bookId);
				slot.setStartingTime(time.getStartTime());
				slot.setEndTime(time.getEndTime());
				slot.setCreatedAt(LocalDateTime.now());
				slot.setModifiedAt(LocalDateTime.now());
				slot.setCreatedBy(bookingDetails.getCreatedBy());
				slot.setModifiedBy(bookingDetails.getCreatedBy());
				slot.setStatus("NEW");
				slotRepo.save(slot);
			}, () -> {
				logger.error("Booking failed for UHID: {} - Not all slots available", bookingDetails.getUhid());
				throw new NotBookedException("Not all slot available");
			});
		}
		logger.info("Booking successful for UHID: {}", bookingDetails.getUhid());
		return true;
	}

	public Optional<Bed> getAvailableBed(Integer stationId, Integer bedTypeId, Integer cat, LocalDateTime startTime,
			LocalDateTime endTime) {
		logger.debug(
				"Checking available bed for stationId: {}, bedTypeId: {}, category: {}, startTime: {}, endTime: {}",
				stationId, bedTypeId, cat, startTime, endTime);

		List<Bed> bedList = bedRepo.getBed(stationId, bedTypeId, cat);
		if (bedList.size() == 0) {
			logger.warn("No beds found for stationId: {}, bedTypeId: {}, category: {}", stationId, bedTypeId, cat);
			return Optional.empty();
		}

		for (Bed bed : bedList) {
			logger.debug("Available bed found: {}", bed.getBedId());
			List<Slot> s = slotRepo.findAvailableSlotsWithinTimeRange(startTime, endTime, bed.getBedId());
			if (s.size() == 0) {
				return Optional.of(bed);
			}
		}
		logger.warn("No available beds found for the given time range");
		return Optional.empty();
	}

	@Override
	public boolean existsByUhid(String uhid) {
	        return bookingRepo.existsByUhid(uhid);
	    }

	@Override
	public List<Integer> getBookingIdsByUhid(String uhid) {
		
		return bookingRepo.findBookingIdsByUhid(uhid);
	}
	
	@Transactional
		@Override
		public boolean rescheduleBooking(RescheduleBookingDTO bookingDetails) {
		logger.info("Rescheduling bed for UHID: {}", bookingDetails.getUhid());

		PatientDTO p = hisService.getPatientByUHID(bookingDetails.getUhid().split("\\.")[0],
				Integer.parseInt(bookingDetails.getUhid().split("\\.")[1]));
		if (p == null) {
			logger.error("Invalid UHID: {}", bookingDetails.getUhid());
			throw new UhidNotFound("Invalid UHID");
		}
			try {
				Booking book = bookingRepo.findByBookingId(bookingDetails.getBookingId());
				//book.setBookedOn(LocalDateTime.now());
				book.setModifiedAt(LocalDateTime.now());
				book.setModifiedBy(bookingDetails.getModifiedBy());
			Booking	bookId= bookingRepo.save(book);
				
				DialysisBooking dailysisBooking = new DialysisBooking();
				 //dailysisBooking = dialysisBookingRepo.findByBookingIdUnique(bookingDetails.getBookingId());
				
				List<BookingTimeDTO> timeList = bookingDetails.getSlotList();
				Collections.sort(timeList);
				dailysisBooking.setBooking(bookId);
				dailysisBooking.setFrequency(bookingDetails.getFrequency());
				dailysisBooking.setInfected(bookingDetails.isInfected());
				dailysisBooking.setStartDate(timeList.get(0).getStartTime().toLocalDate());
				dailysisBooking.setEndDate(timeList.get(timeList.size() - 1).getStartTime().toLocalDate());
				dailysisBooking.setCreatedAt(LocalDateTime.now());
				dailysisBooking.setModifiedAt(LocalDateTime.now());
				dailysisBooking.setCreatedBy(bookingDetails.getModifiedBy());
				dailysisBooking.setModifiedBy(bookingDetails.getModifiedBy());
				dailysisBooking.setStatus("RESCHEDULED");
				dailysisBooking.setComments(bookingDetails.getComments());
				dialysisBookingRepo.save(dailysisBooking);
				List<Slot> slotList = slotRepo.findByBooking(bookId);
				for(Slot slot : slotList) {
					//slotRepo.delete(slot);
					slot.setAvailable(true);
					slot.setComments(bookingDetails.getComments());
					slot.setModifiedBy(bookingDetails.getModifiedBy());
					slot.setStatus("RESCHEDULED");
					slot.setModifiedAt(LocalDateTime.now());
				}
				slotRepo.saveAll(slotList);
				for(BookingTimeDTO time : timeList) {
					Optional<Bed> availableBed = getAvailableBed(bookingDetails.getStationId(), bookingDetails.getBedTypeId(),
							bookingDetails.getCategory(), time.getStartTime(), time.getEndTime());
					availableBed.ifPresentOrElse(bed -> {
						Slot slot = new Slot();
						slot.setBed(bed);
						slot.setBookingId(bookId);
						slot.setStartingTime(time.getStartTime());
						slot.setEndTime(time.getEndTime());
						slot.setComments(bookingDetails.getComments());
						slot.setCreatedAt(LocalDateTime.now());
						slot.setModifiedAt(LocalDateTime.now());
						slot.setCreatedBy(bookingDetails.getModifiedBy());
						slot.setModifiedBy(bookingDetails.getModifiedBy());
						slot.setStatus("RESCHEDULED");
						slotRepo.save(slot);
					}, () -> {
						logger.error("reschedule failed for UHID: {} - Not all slots available", bookingDetails.getUhid());
						throw new NotBookedException("Not all slot available");
					});
				}
			} catch (Exception e) {
				e.printStackTrace();
				throw new NotBookedException("Not all slot available");
			}
			return true;
		}
	
	public boolean waitingListAppointment(@RequestBody WaitingListDTO bookingDetails){
		
		logger.info("Adding appointment to waiting list: {}", bookingDetails.getUhid());

		PatientDTO p = hisService.getPatientByUHID(bookingDetails.getUhid().split("\\.")[0],
				Integer.parseInt(bookingDetails.getUhid().split("\\.")[1]));
		if (p == null) {
			logger.error("Invalid UHID: {}", bookingDetails.getUhid());
			throw new UhidNotFound("Invalid UHID");
		}
		Booking book = new Booking();

		book.setUhid(bookingDetails.getUhid());
		book.setBookedOn(LocalDateTime.now());
		book.setCreatedAt(LocalDateTime.now());
		book.setModifiedAt(LocalDateTime.now());
		book.setCreatedBy(bookingDetails.getCreatedBy());
		book.setModifiedBy(bookingDetails.getCreatedBy());
		Booking bookId = bookingRepo.save(book);

		DialysisBooking dailysisBooking = new DialysisBooking();
//		List<BookingTimeDTO> timeList = bookingDetails.getSlotList();
//		Collections.sort(timeList);
		//Viral Marker is captured 
		dailysisBooking.setInfected(bookingDetails.isInfected());
		dailysisBooking.setBooking(bookId);
//		dailysisBooking.setStartDate(timeList.get(0).getStartTime().toLocalDate());
//		dailysisBooking.setEndDate(timeList.get(timeList.size() - 1).getStartTime().toLocalDate());
		dailysisBooking.setStartDate(bookingDetails.getStartDate());
		dailysisBooking.setEndDate(bookingDetails.getEndDate());
		dailysisBooking.setFrequency(bookingDetails.getFrequency());
		dailysisBooking.setWaiting(true);
		
		dailysisBooking.setCreatedAt(LocalDateTime.now());
		dailysisBooking.setModifiedAt(LocalDateTime.now());
		dailysisBooking.setCreatedBy(bookingDetails.getCreatedBy());
		dailysisBooking.setModifiedBy(bookingDetails.getCreatedBy());
		dailysisBooking.setStatus("WAITING");
		
		dialysisBookingRepo.save(dailysisBooking);
		
		
		return true;
	}

}
