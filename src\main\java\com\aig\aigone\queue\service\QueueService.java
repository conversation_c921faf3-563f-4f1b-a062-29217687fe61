package com.aig.aigone.queue.service;

import java.util.List;

import org.springframework.http.HttpHeaders;

import com.aig.aigone.queue.dto.AddUserToQueueResponseDto;
import com.aig.aigone.queue.dto.AllotedQueuesResponseDto;
import com.aig.aigone.queue.dto.CommonResponseDto;
import com.aig.aigone.queue.dto.CounterDetailsResponseDto;
import com.aig.aigone.queue.dto.GenericResponse;
import com.aig.aigone.queue.dto.GenericResponse1;
import com.aig.aigone.queue.dto.PatientQueueDto;
import com.aig.aigone.queue.dto.QRCodeDetailsRequestDto;
import com.aig.aigone.queue.dto.QueueWeightAgeDto;
import com.aig.aigone.queue.dto.TokenDetailsResponseDto;
import com.aig.aigone.queue.dto.TokenResponseDto;
import com.aig.aigone.queue.dto.UserQueueAssesmentRequestDto;
import com.aig.aigone.queue.dto.UserQueueDto;
import com.aig.aigone.queue.dto.UserServicesDto;
import com.aig.aigone.queue.dto.GenerateBarCodeResponseDto;

public interface QueueService {

	GenericResponse<AllotedQueuesResponseDto> fetchStaffUserAllocatedQueues(String empId);

	GenericResponse<QueueWeightAgeDto> fetchQueueWeightage(String empId);

	HttpHeaders getQueueAuthToken(String empId);

	GenericResponse<UserServicesDto> fetchUserServices(String empId,Long userQueueId);


	GenericResponse<AddUserToQueueResponseDto> addUserToQueue(String empId,Long queueId, QRCodeDetailsRequestDto qrDetails);

	GenericResponse<AddUserToQueueResponseDto> getUserQueue(String empId,String preCheckStatus, Long queueId, List<Long> queueIds, String assignmentOption, String queueCode);

	GenericResponse<AddUserToQueueResponseDto> updateUserQueueWeightage(String empId,String queueWeightageAction, Long queueWeightageActionId,
			Long userQueueId,String remarks);

	GenericResponse<CommonResponseDto> callNextSemiAuto(String employeeId, Long queueId, QRCodeDetailsRequestDto qrDetails);

	GenericResponse1<TokenDetailsResponseDto> fetchTokenDetails(String uhId);

	GenericResponse<CommonResponseDto> updateUserQueueAssesment(String employeeId,
			UserQueueAssesmentRequestDto details);

	GenericResponse<String> getCommonRemarks(String employeeId, String type);
	
	GenericResponse1<AddUserToQueueResponseDto> callNextPreCheck(String employeeId, Long queueId);

	GenericResponse<CounterDetailsResponseDto> getUserQueueCounters(String employeeId, Long queueId);

	GenericResponse1<CounterDetailsResponseDto> getCounterDetails(String empId, Long queueCounterId);

	GenericResponse1<String> updateCounter(String employeeId, Long queueCounterId, String status);

	GenericResponse<UserQueueDto> getUserQueueAll(String employeeId, String queueCode);

	GenericResponse1<String> updatePreCheckStatus(String employeeId, Long userQueueId, List<Long> userServiceIds);

	GenericResponse1<TokenResponseDto> getTokenDetails(String employeeId, Long tokenId);

	GenericResponse<String> getStaffUserAllocatedRescouce(String employeeId, List<String> resources);

	GenericResponse1<AllotedQueuesResponseDto> getQueueDetails(String employeeId, String queueCode);

	GenericResponse1<PatientQueueDto> getPatientQueue(String employeeId, String uhId);

	GenericResponse<List<GenerateBarCodeResponseDto>> generateBarCode(String employeeId, List<Long> userServiceIds);

}
