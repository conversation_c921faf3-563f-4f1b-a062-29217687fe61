package com.aig.aigone.controller.dfcms;

import java.util.List;

import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.exceptions.dfcms.DfcmsException;
import com.aig.aigone.model.dto.UserDto;
import com.aig.aigone.model.dto.dfcms.AssignRolesRequestDto;
import com.aig.aigone.model.entity.aigone.EmployeeTypeEntity;
import com.aig.aigone.model.entity.aigone.UserEntity;
import com.aig.aigone.service.dfcms.ExternalUserService;
import com.aig.aigone.service.dfcms.UserRoleAssignService;

import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/api/dfcms/auth")
@Slf4j
public class DFCMSUserController {

	@Autowired
	private UserRoleAssignService userRoleService;

	@Autowired
	private ExternalUserService externalUserService;

	@Operation(summary = "Register External User")
	@PostMapping("/register")
	public ResponseEntity<String> registerExternalUser(@RequestBody UserDto userDto) {
		externalUserService.registerExternalUser(userDto);
		return new ResponseEntity<>("External User Registered Successfully", HttpStatus.CREATED);
	}

	@Operation(summary = "Get all external users")
	@PreAuthorize("hasAnyRole('DC_ADMIN', 'HR','COCKPIT_ADMIN')")
	@GetMapping("/external")
	public ResponseEntity<List<UserDto>> getAllExternalUsers() {
		// Fetch all external users as DTOs
		List<UserDto> externalUsers = externalUserService.getAllExternalUsers();
		return new ResponseEntity<>(externalUsers, HttpStatus.OK);
	}

	@Operation(summary = "Update External User")
	@PutMapping("/update/external/{phoneNo}")
	@PreAuthorize("hasAnyRole('DC_ADMIN', 'HR','COCKPIT_ADMIN')")
	public ResponseEntity<String> updateExternalUser(@PathVariable Long phoneNo, @RequestBody UserDto userDto) {
		externalUserService.updateExternalUser(phoneNo, userDto);
		return new ResponseEntity<>("External User Updated Successfully", HttpStatus.OK);
	}

	@Operation(summary = "Delete an external user by ID")
	@PreAuthorize("hasAnyRole('DC_ADMIN', 'HR','COCKPIT_ADMIN')")
	@DeleteMapping("/external/{id}")
	public ResponseEntity<Void> deleteExternalUserById(@PathVariable Long id) {
		externalUserService.deleteExternalUserById(id);
		return new ResponseEntity<>(HttpStatus.NO_CONTENT);
	}

	@GetMapping("/search/{employeeId}")
	@Operation(summary = "Get User by Employee ID")
	public ResponseEntity<com.aig.aigone.model.dto.UserDto> getUserByEmployeeId(@PathVariable String employeeId) {
		com.aig.aigone.model.dto.UserDto user = userRoleService.findUserByEmployeeId(employeeId)
				.orElseThrow(() -> new DfcmsException(HttpStatus.NOT_FOUND, "User does not exist"));
		return ResponseEntity.ok(user);
	}

	@PreAuthorize("hasRole('COCKPIT_ADMIN')")
	@Operation(summary = "Assign Roles to User")
	@PostMapping("/assign-roles")
	public ResponseEntity<String> assignRolesToUser(@RequestBody AssignRolesRequestDto request) {
		// Delegate to the service layer
		userRoleService.assignRolesToUser(request);
		return ResponseEntity.ok("Roles assigned successfully to the user!");
	}

	@PreAuthorize("hasRole('COCKPIT_ADMIN')")
	@Operation(summary = "Delete User Role")
	@DeleteMapping("/userRole")
	public ResponseEntity<Void> deleteUserRole(@RequestParam Long userId, @RequestParam Long roleId) {
		userRoleService.deleteUserRole(userId, roleId);
		return ResponseEntity.noContent().build(); // HTTP 204 (No Content)
	}

	@GetMapping("/roleNames")
	@Operation(summary = "Get All Role Names")
	public List<String> getAllRoleNames() {
		return externalUserService.getAllRoleNames();
	}
	
    @GetMapping("/externalPayGroupNames")
    @PreAuthorize("hasAnyRole('DC_ADMIN', 'HR','COCKPIT_ADMIN')")
    @Operation(summary = "Get All External Pay Group Names")
    public ResponseEntity<List<String>> getAllExternalPayGroupNames() {
        log.info("Received request to fetch all external pay group names.");
        List<String> payGroupNames = externalUserService.getAllExternalPayGroupNames();
        return ResponseEntity.ok(payGroupNames);
    }
	
//	@Operation(summary = "Get all Employee Types")
//	@GetMapping("/employee-types")
//	public ResponseEntity<List<EmployeeTypeEntity>> getAllEmployeeTypes() {
//	    List<EmployeeTypeEntity> employeeTypes = externalUserService.getAllEmployeeTypes();
//	    return ResponseEntity.ok(employeeTypes);
//	}


}
