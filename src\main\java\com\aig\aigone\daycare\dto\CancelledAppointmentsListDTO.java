package com.aig.aigone.daycare.dto;

import java.util.List;

import com.aig.aigone.daycare.model.Booking;
import com.aig.aigone.daycare.model.DialysisBooking;
import com.aig.aigone.daycare.model.Slot;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CancelledAppointmentsListDTO {

	private Booking booking;
	private DialysisBooking dialysisBooking;
	private List<Slot> slotsList;
	private PatientDTO patientDetails;
}
