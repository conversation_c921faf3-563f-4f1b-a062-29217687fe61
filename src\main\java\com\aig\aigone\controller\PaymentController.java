package com.aig.aigone.controller;

import java.io.IOException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.exception.AigOneException;
import com.aig.aigone.model.dto.PaymentGatewayEnum;
import com.aig.aigone.model.dto.PaymentInitRequestDto;
import com.aig.aigone.model.dto.PaymentInitResponseDto;
import com.aig.aigone.model.entity.aigone.SystemSettingsKey;
import com.aig.aigone.pg.dto.RazorPaySuccessDto;
import com.aig.aigone.service.PaymentService;
import com.aig.aigone.service.SystemSettingService;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/payment")
public class PaymentController {
	
	@Autowired
	private PaymentService paymentService;
	
	@Autowired
	private SystemSettingService settingService;

	@PostMapping("/init")
	public PaymentInitResponseDto initiate(@RequestBody PaymentInitRequestDto request,HttpServletRequest servletRequest) {
		
		System.out.println(request.toString()+"requet for payment checking ");
		String enableMitrPayment = settingService.getSettingValue(SystemSettingsKey.ENABLE_MITHR_PAYMENT.getName());
		if(enableMitrPayment.equals("false")) {
			throw new AigOneException("Temporarily disabling this feature.");
		}
		String deviceType = servletRequest.getHeader("device");
		request.setDevice(deviceType);
		
		String defaultPg = settingService.getSettingValue(SystemSettingsKey.DEFAULT_PAYMENT_GATEWAY.getName());
		if(request.getPaymentGateway() == null) {
			request.setPaymentGateway(PaymentGatewayEnum.valueOf(defaultPg));
		}
		
		return paymentService.initializePayment(request);
	}
	
	@PostMapping("/status")
	public void status(@RequestParam("encResp") String encResp,@RequestParam("orderNo") String orderNo, HttpServletResponse response) {
		log.info("payment status update:: "+encResp +" Order No: "+orderNo);
		try {
			response.sendRedirect(paymentService.updatePaymentOrder(encResp, orderNo));
		} catch (IOException e) {
			log.error("Error while redirection "+e);
		}
	}
	
	@PostMapping("/razorpay/status")
	public String razorPaystatus(@RequestBody RazorPaySuccessDto successDto, HttpServletResponse response) {
		log.info("payment status update from razorpay:: " + successDto.toString());
		return paymentService.razorPayCallback(successDto);
	}
	
//	@PostMapping("/payu/status")
//	public String razorPaystatus(@RequestParam("mihpayid") String mihpayid,
//			@RequestParam("mode") String mode,
//			@RequestParam("status") String status,
//			@RequestParam("unmappedstatus") String unmappedstatus,
//			@RequestParam("key") String key,
//			@RequestParam("txnid") String txnid,
//			@RequestParam("hash") String hash,
//			@RequestParam("bank_ref_num") String bank_ref_num) {
//		log.info("payment status update from payu:: " );
//		return null;
//	}
	
	@PostMapping("/payu/status")
	public void razorPaystatus(@RequestBody MultiValueMap<String, String> formData,HttpServletResponse response) {
//		return paymentService.payUCallBack(formData);
		
		log.info("payment status update PAYU:: "+formData);
		try {
			response.sendRedirect(paymentService.payUCallBack(formData));
		} catch (IOException e) {
			log.error("Error while redirection "+e);
		}
	}
	
	
	@GetMapping("/details")
	public void status(@RequestParam("orderId") String orderId) {
		paymentService.statusTracker(orderId);

	}
}
