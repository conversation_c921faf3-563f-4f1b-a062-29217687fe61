package com.aig.aigone.daycare.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aig.aigone.controller.DialysisController;

import com.aig.aigone.daycare.dto.BookingTimeDTO;
import com.aig.aigone.daycare.dto.CancelSlotDTO;
import com.aig.aigone.daycare.dto.DeleteAppointmentDTO;
import com.aig.aigone.daycare.dto.PatientDTO;
import com.aig.aigone.daycare.dto.SlotDTO;
import com.aig.aigone.daycare.exception.BookingNotFoundException;
import com.aig.aigone.daycare.exception.DialysisBookingNotFoundException;
import com.aig.aigone.daycare.exception.SlotConflictException;
import com.aig.aigone.daycare.exception.SlotNotFoundException;
import com.aig.aigone.daycare.model.Bed;
import com.aig.aigone.daycare.model.Booking;
import com.aig.aigone.daycare.model.DialysisBooking;
import com.aig.aigone.daycare.model.Slot;
import com.aig.aigone.daycare.service.WhatsAppService;
import com.aig.aigone.daycare.repository.BookingRepository;
import com.aig.aigone.daycare.repository.DialysisBookingRepository;
import com.aig.aigone.daycare.repository.SlotRepository;
import com.aig.aigone.daycare.serviceimpl.BookingServiceImpl;
import com.aig.aigone.daycare.util.DaycareConstants;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;



@Service
public class SlotService {
	private static final Logger logger = LoggerFactory.getLogger(SlotService.class);
	 

	@Autowired
	HisServiceDaycare hisService;
	
    @Autowired
    private SlotRepository slotRepository;
    
    @Autowired
	private WhatsAppService whatsAppService;

    @Autowired
    private BookingRepository bookingRepository; 
    
    
    @Autowired
    private DialysisBookingRepository dialysisBookingRepository;
    
    @Autowired
    private BookingServiceImpl bookingServiceImpl;
    

    /**
     * Deletes a specific slot by its ID.
     */
    @Transactional
    public void cancelSlot(CancelSlotDTO cancelSlotDTO) {
//        if (!slotRepository.existsById(Long.parseLong(""+cancelSlotDTO.getSlotId()))) {
//            throw new SlotNotFoundException("Slot Not Found : "+cancelSlotDTO.getSlotId());
//        }
        //slotRepository.deleteById(slotId);
    	Slot slot = null;
    	Optional<Slot> slotOp = slotRepository.findById(Long.parseLong(""+cancelSlotDTO.getSlotId()));
    	if(slotOp.isPresent()) {
    		slot = slotOp.get();
    		List<Slot> slotList = slotRepository.findAllActiveSlotsForBookingId(slot.getBookingId());
    		if(slotList.size() == 1) {
    			DeleteAppointmentDTO deleteAppointmentDTO = new DeleteAppointmentDTO();
    			deleteAppointmentDTO.setBookingId(slot.getBookingId().getId());
    			deleteAppointmentDTO.setUhid(slot.getBookingId().getUhid());
    			deleteAppointmentDTO.setComments(cancelSlotDTO.getComments());
    			deleteAppointmentDTO.setModifiedBy(cancelSlotDTO.getModifiedBy());
    			deleteAllByUhidAndBookingId(deleteAppointmentDTO);
    		}else {
    		slot.setAvailable(true);
    		slot.setComments(cancelSlotDTO.getComments());
    		slot.setModifiedAt(LocalDateTime.now());
    		slot.setModifiedBy(cancelSlotDTO.getModifiedBy());
    		slot.setStatus("CANCELLED");
    		slotRepository.save(slot);
    		}
    	}
    	else {
    		throw new SlotNotFoundException("Slot Not Found : "+cancelSlotDTO.getSlotId());
    	}
    	
    	String uhid = cancelSlotDTO.getUhid();
		String iacode = uhid.split("\\.")[0];
		Integer regNo = Integer.parseInt(uhid.split("\\.")[1]);

		PatientDTO patient = hisService.getPatientByUHID(iacode, regNo);
		String patientPhoneNumber = patient.getPhoneNo();
		String patientName = patient.getPatientName();

		String bookingDate = slot.getStartingTime().toLocalDate()
				.format(DateTimeFormatter.ofPattern("dd-MM-yyyy"));

		String bookingTime = slot.getStartingTime()
				.format(DateTimeFormatter.ofPattern("hh:mm a"));

		logger.info("Calling Whatsapp API - Send appointment notification to patient....");

//		// Live - whatsapp notification for appointment confirmation
//		// whatsAppService.sendDialysisNotification(DayCareConstants.APPOINTMENT_CONFIRMATION,patientPhoneNumber,patientName,bookingDate,
//		// bookingTime);
//		
//		// Test- whatsapp notification
//		// Cheryshma - **********
//		whatsAppService.sendDialysisNotification(DaycareConstants.DIALYSIS_APPOINTMENT_CONFIRMATION, "**********",
//				"John", DaycareConstants.APPOINTMENT_TYPE, bookingDate, bookingTime);
		 try {
		        // Live - whatsapp notification for appointment confirmation
		        // whatsAppService.sendDialysisNotification(DayCareConstants.APPOINTMENT_CONFIRMATION, patientPhoneNumber, patientName, bookingDate, bookingTime);

		        // Test- whatsapp notification
		        // Cheryshma - **********
		        whatsAppService.sendDialysisNotification(DaycareConstants.DIALYSIS_APPOINTMENT_CANCELLATION, patientPhoneNumber,
		                patientName,bookingDate, bookingTime);

		        logger.info("**** Whatsapp notification sent successfully to patient. ****");
		    } catch (Exception e) {
		        logger.error("**** Failed to send Whatsapp notification: {}", e.getMessage());
		    }
    }

   

//    @Transactional
//    public void deleteSlotsByUhid(String uhid) {
//        // Step 1: Get list of booking IDs by uhid
//        List<Integer> bookingIdList = bookingRepository.findBookingIdsByUhid(uhid);
//        
//        // Step 2: Iterate over each bookingId
//        for (Integer bookingId : bookingIdList) {
//            // Step 3: For each bookingId, retrieve the list of slot IDs
//            List<Long> slotIdList = slotRepository.findSlotIdsByBookingId(bookingId);
//            
//            // Step 4: Iterate over the slot IDs and delete each slot
//            for (Long slotId : slotIdList) {
//                deleteSlot(slotId);  // Reusing the deleteSlot method
//            }
//        }
//    }
    
    @Transactional
    public void deleteAllByUhidAndBookingId(DeleteAppointmentDTO deleteAppointment) {
        // Step 1: Verify that Booking exists for the provided UHID and booking ID
    	String uhid = deleteAppointment.getUhid(); 
    	Integer bookingId = deleteAppointment.getBookingId();
        Booking booking = bookingRepository.findByBookingId(bookingId);
        if (booking == null || !booking.getUhid().equals(uhid)) {
            throw new BookingNotFoundException("Booking not found for UHID: " + uhid + " and Booking ID: " + bookingId);
        }

        // Step 2: Delete Slots associated with the booking ID
        List<Long> slotIds = slotRepository.findSlotIdsByBookingId(bookingId);
        Slot forWhatsappNotification = null;
        Optional<Slot> forWhatsappNotificationslotOp = slotRepository.findById(slotIds.get(0));
        if(forWhatsappNotificationslotOp.isPresent()) {
        	forWhatsappNotification = forWhatsappNotificationslotOp.get();
        	}
        if (slotIds.isEmpty()) {
            throw new SlotNotFoundException("No slots found for Booking ID: " + bookingId);
        }
        slotIds.forEach(slotId -> {
//            if (!slotRepository.existsById(slotId)) {
//                throw new SlotNotFoundException("Slot not found for Slot ID: " + slotId);
//            }
            //slotRepository.deleteById(slotId);
            Slot slot = null;
            Optional<Slot> slotOp = slotRepository.findById(slotId);
            if(slotOp.isPresent()) {
            	slot = slotOp.get();
            	slot.setComments(deleteAppointment.getComments());
            	slot.setAvailable(true);
            	slot.setStatus("CANCELLED");
            	slot.setModifiedAt(LocalDateTime.now());
            	slot.setModifiedBy(deleteAppointment.getModifiedBy());
            	slotRepository.save(slot);
            }else {
            	throw new SlotNotFoundException("Slot not found for Slot ID: " + slotId);
            }
        });

        // Step 3: Delete DialysisBooking entries associated with the booking ID
     // Step 3: Delete DialysisBooking entries associated with the booking ID
        List<DialysisBooking> dialysisBookings = dialysisBookingRepository.findByBookingId(bookingId);
        if (dialysisBookings.isEmpty()) {
            throw new DialysisBookingNotFoundException("No dialysis bookings found for Booking ID: " + bookingId);
        }


        // Proceed to delete the found dialysis bookings
        //dialysisBookings.forEach(dialysisBookingRepository::delete);
        dialysisBookings.forEach(dialysisBooking ->{
        	dialysisBooking.setActive(false);
        	//dialysisBooking.setStatus("CANCELLED");
        	dialysisBooking.setModifiedAt(LocalDateTime.now());
        	dialysisBooking.setModifiedBy(deleteAppointment.getModifiedBy());
        	//dialysisBooking.setComments(deleteAppointment.getComments());
        	dialysisBookingRepository.save(dialysisBooking);
        });
        DialysisBooking recentBooking = dialysisBookings.stream()
                .max(Comparator.comparing(DialysisBooking::getId))
                .orElse(null);
        DialysisBooking dialysisBooking2 = new DialysisBooking();
        dialysisBooking2.setBooking(recentBooking.getBooking());
        dialysisBooking2.setFrequency(recentBooking.getFrequency());
        dialysisBooking2.setInfected(recentBooking.isInfected());
        dialysisBooking2.setStartDate(recentBooking.getStartDate());
        dialysisBooking2.setEndDate(recentBooking.getEndDate());
        dialysisBooking2.setCreatedAt(LocalDateTime.now());
        dialysisBooking2.setModifiedAt(LocalDateTime.now());
        dialysisBooking2.setCreatedBy(deleteAppointment.getModifiedBy());
        dialysisBooking2.setModifiedBy(deleteAppointment.getModifiedBy());
        dialysisBooking2.setStatus("CANCELLED");
        dialysisBooking2.setActive(false);
        dialysisBooking2.setComments(deleteAppointment.getComments());
        dialysisBookingRepository.save(dialysisBooking2);


        // Step 4: Delete the Booking entry itself
        //bookingRepository.deleteById(bookingId);
        booking.setActive(false);
        booking.setModifiedAt(LocalDateTime.now());
    	booking.setModifiedBy(deleteAppointment.getModifiedBy());
        bookingRepository.save(booking);
        
        //Whatsapp Notification
		//String uhid = deleteAppointment.getUhid();
		String iacode = uhid.split("\\.")[0];
		Integer regNo = Integer.parseInt(uhid.split("\\.")[1]);

		PatientDTO patient = hisService.getPatientByUHID(iacode, regNo);
		String patientPhoneNumber = patient.getPhoneNo();
		String patientName = patient.getPatientName();

		String bookingStartDate = recentBooking.getStartDate()
				.format(DateTimeFormatter.ofPattern("dd-MM-yyyy"));
		
		String bookingEndDate = recentBooking.getEndDate()
				.format(DateTimeFormatter.ofPattern("dd-MM-yyyy"));

		String bookingTime = forWhatsappNotification.getStartingTime()
				.format(DateTimeFormatter.ofPattern("hh:mm a"));

		logger.info("Calling Whatsapp API - Send remainder notification to patient....");

		 try {
		        // Cheryshma - **********
		        whatsAppService.sendDialysisNotification(DaycareConstants.WHOLE_CYCLE_CANCELLATION, patientPhoneNumber,
		                patientName, bookingStartDate, bookingEndDate,bookingTime);

		        logger.info("**** Whatsapp notification sent successfully to patient. ****");
		    } catch (Exception e) {
		        logger.error("**** Failed to send Whatsapp notification: {}", e.getMessage());
		    }
    }


    public Slot rescheduleSlot(SlotDTO slotDTO) {
    	
        Slot slot = slotRepository.findById(slotDTO.getId())
                .orElseThrow(() -> new SlotNotFoundException("No slots found for Slot ID: " + slotDTO.getId()));
        
        Boolean checkSlotExistOnSameDay = slotRepository.checkIfSlotExistOnSameDay(slotDTO.getStartingTime().toLocalDate(), slotDTO.getEndTime().toLocalDate(), slotDTO.getUhid(),slotDTO.getId());
        if(checkSlotExistOnSameDay) {
        	throw new SlotNotFoundException(slotDTO.getUhid()+" has already slot on this day");
        }
         
        Optional<Slot> existingSlot = slotRepository.findSlotWithSameTime(slotDTO.getStartingTime(), slotDTO.getEndTime(), slot.getBed().getBedId());
        Slot newSlot = new Slot();
        if (existingSlot.isPresent()) {
            //throw new SlotConflictException("A slot with the same start and end time already exists.");
        	Optional<Bed> availableBed = bookingServiceImpl.getAvailableBed(slot.getBed().getStationId(),slot.getBed().getBedtypeId(),slot.getBed().getCategory(),slotDTO.getStartingTime(),slotDTO.getEndTime());
        	if(availableBed.isPresent()) {
        		newSlot.setBed(availableBed.get());
                newSlot.setBookingId(slot.getBookingId());
                newSlot.setStartingTime(slotDTO.getStartingTime());
                newSlot.setEndTime(slotDTO.getEndTime());
                newSlot.setComments(slotDTO.getComments());
                newSlot.setCreatedAt(LocalDateTime.now());
                newSlot.setModifiedAt(LocalDateTime.now());
                newSlot.setCreatedBy(slotDTO.getModifiedBy());
                newSlot.setModifiedBy(slotDTO.getModifiedBy());
                newSlot.setStatus("RESCHEDULED");
        	}else {
        		throw new SlotConflictException("No available beds found for the given time range");
        	}
        }else { 
            newSlot.setBed(slot.getBed());
            newSlot.setBookingId(slot.getBookingId());
            newSlot.setStartingTime(slotDTO.getStartingTime());
            newSlot.setEndTime(slotDTO.getEndTime());
            newSlot.setComments(slotDTO.getComments());
            newSlot.setCreatedAt(LocalDateTime.now());
            newSlot.setModifiedAt(LocalDateTime.now());
            newSlot.setCreatedBy(slotDTO.getModifiedBy());
            newSlot.setModifiedBy(slotDTO.getModifiedBy());
            newSlot.setStatus("RESCHEDULED");
			
        }
 
//        slot.setStartingTime(slotDTO.getStartingTime());
//        slot.setEndTime(slotDTO.getEndTime());
        slot.setAvailable(true);
        slot.setStatus("RESCHEDULED");
        slot.setModifiedAt(LocalDateTime.now());
        slot.setModifiedBy(slotDTO.getModifiedBy());
        slot.setComments(slotDTO.getComments());
        slotRepository.save(slot);
        
        slotRepository.save(newSlot);
        
        String uhid = slotDTO.getUhid();
		String iacode = uhid.split("\\.")[0];
		Integer regNo = Integer.parseInt(uhid.split("\\.")[1]);

		PatientDTO patient = hisService.getPatientByUHID(iacode, regNo);
		String patientPhoneNumber = patient.getPhoneNo();
		String patientName = patient.getPatientName();

		String bookingDate = slot.getStartingTime().toLocalDate()
				.format(DateTimeFormatter.ofPattern("dd-MM-yyyy"));

		String bookingTime = slot.getStartingTime()
				.format(DateTimeFormatter.ofPattern("hh:mm a"));
		
		String rescheduledDate = slotDTO.getStartingTime().toLocalDate()
				.format(DateTimeFormatter.ofPattern("dd-MM-yyyy"));
		
		String rescheduleTime = slotDTO.getStartingTime().format(DateTimeFormatter.ofPattern("hh:mm a"));

		logger.info("Calling Whatsapp API - Send appointment notification to patient....");

//		// Live - whatsapp notification for appointment confirmation
//		// whatsAppService.sendDialysisNotification(DayCareConstants.APPOINTMENT_CONFIRMATION,patientPhoneNumber,patientName,bookingDate,
//		// bookingTime);
//		
//		// Test- whatsapp notification
//		// Cheryshma - **********
//		whatsAppService.sendDialysisNotification(DaycareConstants.DIALYSIS_APPOINTMENT_CONFIRMATION, "**********",
//				"John", DaycareConstants.APPOINTMENT_TYPE, bookingDate, bookingTime);
		 try {
		        // Live - whatsapp notification for appointment confirmation
		        // whatsAppService.sendDialysisNotification(DayCareConstants.APPOINTMENT_CONFIRMATION, patientPhoneNumber, patientName, bookingDate, bookingTime);

		        // Test- whatsapp notification
		        // Cheryshma - **********
		        whatsAppService.sendDialysisNotification(DaycareConstants.DIALYSIS_APPOINTMENT_RESCHEDULE, patientPhoneNumber,
		                patientName,bookingDate, bookingTime,rescheduledDate,rescheduleTime);

		        logger.info("**** Whatsapp notification sent successfully to patient. ****");
		    } catch (Exception e) {
		        logger.error("**** Failed to send Whatsapp notification: {}", e.getMessage());
		    }
		 return newSlot;
    }
    
    public List<Map<String, Object>> getBedAndSlotAvailabilityForThreeDays(Map<String, Object> input) {
        List<String> dates = (List<String>) input.get("dates");
        List<Map<String, Object>> result = new ArrayList<>();

        for (String date : dates) {
            List<Map<String, Object>> slotsList = new ArrayList<>();
            String[][] timeRanges = {
                {"06:00:00", "10:00:00", "6AM - 10AM"},
                {"10:00:00", "14:00:00", "10AM - 2PM"},
                {"16:00:00", "20:00:00", "4PM - 8PM"},
                {"20:00:00", "00:00:00", "8PM - 12AM"}
            };

            for (String[] timeRange : timeRanges) {
                List<Map<String, Object>> beds = slotRepository.getBedAndSlotAvailabilityPerSlotTiming(date, timeRange[0], timeRange[1]);
                Map<String, Object> slot = new LinkedHashMap<>();
                slot.put("time", timeRange[2]);
                slot.put("beds", beds);
                slotsList.add(slot);
            }

            LocalDate dt = LocalDate.parse(date);
            Map<String, Object> dayEntry = new LinkedHashMap<>();
            dayEntry.put("date", formatDate(dt));
            dayEntry.put("day", dt.getDayOfWeek().toString().substring(0, 1).toUpperCase() + dt.getDayOfWeek().toString().substring(1).toLowerCase());
            dayEntry.put("slots", slotsList);

            result.add(dayEntry);
        }
        return result;
    }
    
    private static String formatDate(LocalDate date) {
        int day = date.getDayOfMonth();
        String suffix = getDaySuffix(day);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMM, yyyy", Locale.ENGLISH);
        return day + suffix + " " + date.format(formatter);
    }

    private static String getDaySuffix(int day) {
        if (day >= 11 && day <= 13) {
            return "th";
        }
        switch (day % 10) {
            case 1: return "st";
            case 2: return "nd";
            case 3: return "rd";
            default: return "th";
        }
    }
}

