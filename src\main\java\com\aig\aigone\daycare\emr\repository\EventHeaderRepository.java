package com.aig.aigone.daycare.emr.repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.aig.aigone.daycare.emr.model.EventHeader;

@Repository
public interface EventHeaderRepository extends JpaRepository<EventHeader, Integer> {

	@Query("SELECT h FROM EventHeader h WHERE h.uhid = :uhid AND FUNCTION('DATE', h.eventDateTime) = :date")
	List<EventHeader> getHeaderByUhidAndDate(@Param("uhid") String uhid, @Param("date") LocalDate date);

	@Query(value="SELECT DISTINCT\r\n"
			+ "    h.patient_id,\r\n"
			+ "    p.patient_name,\r\n"
			+ "    MAX(CASE WHEN d.reporting_concept_code = 'DIA123456' THEN d.start_date_time END) AS \"Pre-Dialysis form submission date\",\r\n"
			+ "	MAX(CASE WHEN d.reporting_concept_code = 'DIA123456' THEN d.request_clin_note_content -> 'topics' -> 10 -> 'sections' -> 3 ->> 'selected' END) AS \"Start_date\",\r\n"
			+ "    MAX(CASE WHEN d.reporting_concept_code = 'INTRATECH' THEN d.start_date_time END) AS \"Intra dialysis form submission date\",   \r\n"
			+ "    MAX(CASE WHEN d.reporting_concept_code = 'INTRATECH' THEN d.request_clin_note_content -> 'topics' -> 5 -> 'sections' -> 11 ->> 'selected' END) AS \"End_date\",\r\n"
			+ "    Case when \r\n"
			+ "	MAX(CASE WHEN d.reporting_concept_code = 'INTRATECH' THEN d.request_clin_note_content -> 'topics' -> 5 -> 'sections' -> 11 ->> 'selected' END)  is null\r\n"
			+ "	then 'IN_PROGRESS' \r\n"
			+ "	else 'COMPLETED' end as \"Status\"\r\n"
			+ "FROM\r\n"
			+ "    healthplug.hp_care_events_hdr h\r\n"
			+ "JOIN \r\n"
			+ "    healthplug.hp_care_events_dtl d ON h.event_accn_no = d.event_accn_no\r\n"
			+ "JOIN \r\n"
			+ "    pm_patient_demographics p ON p.patient_id = h.patient_id\r\n"
			+ "JOIN \r\n"
			+ "    hp_encounter e ON e.patient_id = p.patient_id\r\n"
			+ "WHERE  h.patient_id= ? and\r\n"
			+ "    DATE(d.start_date_time ) = current_date\r\n"
			+ "    AND d.reporting_concept_code IN ('DIA123456', 'INTRATECH')\r\n"
			+ "GROUP BY \r\n"
			+ "    h.patient_id, p.patient_name;",nativeQuery = true)
	Map<String,String> getDailysisStatus(String uhid);
}
