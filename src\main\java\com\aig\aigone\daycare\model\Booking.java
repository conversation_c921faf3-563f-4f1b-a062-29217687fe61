package com.aig.aigone.daycare.model;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
@Entity
@Table(name="dc_booking")
public class Booking {
	
	@Id
	@GeneratedValue( strategy = GenerationType.AUTO)
	private Integer id;
	
	@Column(name = "is_active", nullable = false)
	private boolean isActive = true;
	
	@NotNull
	private LocalDateTime bookedOn;
	
	@NotNull
	private String uhid;
	
	private boolean status = true;
	
	private LocalDateTime createdAt;
	private LocalDateTime modifiedAt;
	private String createdBy;
	private String modifiedBy;
	@Column(name="alternative_mobile_number")
	private String alternativeMobileNumber;
}
