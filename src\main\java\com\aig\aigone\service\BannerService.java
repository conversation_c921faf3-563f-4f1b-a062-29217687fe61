package com.aig.aigone.service;

import java.util.List;
import java.util.Optional;

import com.aig.aigone.model.dto.BannerDTO;

public interface BannerService {
    
    List<BannerDTO> getAllActiveBanners();
    
//    List<BannerDTO> getAllBanners();
    
    List<BannerDTO> getAllBanners(Optional<String> bannerType, Optional<Boolean> status);
    
    BannerDTO getBannerById(Long id);
    
    BannerDTO createBanner(BannerDTO bannerDTO);
    
    BannerDTO updateBanner(Long id, BannerDTO bannerDTO);
    
    void deleteBanner(Long id);
} 