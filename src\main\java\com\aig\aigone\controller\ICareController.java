package com.aig.aigone.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.aig.aigone.model.dto.CreateTicketRequestDto;
import com.aig.aigone.model.dto.IcareReqWhatsAppMsgDto;
import com.aig.aigone.model.dto.TicketQuestionDto;
import com.aig.aigone.model.dto.icare.ICareDepartmentsRequestDto;
import com.aig.aigone.model.entity.aigone.TicketQuestion;
import com.aig.aigone.model.entity.aigone.TicketQuestionsEntity;
import com.aig.aigone.service.ICareService;
import com.aig.aigone.service.TicketQuestionService;

import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/api/icare")
@Slf4j
public class ICareController {

	@Autowired
	private ICareService iCareService;

	@Autowired
	private TicketQuestionService ticketService;
	
	
	@PostMapping("/send/whatsapp/msg")
	public void sendWhatsAppMsgForIcareTickets(@RequestBody IcareReqWhatsAppMsgDto icareReqWhatsAppMsgDto ) {
		
		
	}

	@Operation(summary = "fetch departments and sub departments ")
	@GetMapping("/departments")
	public String departments(@RequestParam("selectedid") String selectedId) {
		return iCareService.fetchDepartmentsAndSubDepartments(selectedId);
	}

	@Operation(summary = "fetch Filters ")
	@GetMapping("/filters")
	public String filters() {
//		https://testaig.performitor.com/appcode/webservices/V2/filtersdata.php?tag=getFilters&branchid=6&moduleid=6&menuname=actionlist&userurl=728d70cb0bfbe25c8607d1e281abd523
		return iCareService.getFilters();
	}

	@Operation(summary = "fetch Location ")
	@GetMapping("/locations")
	public String locations(@RequestParam("floorNo") int floorNo) {
//		https://testaig.performitor.com/appcode/webservices/V2/filtersdata.php?tag=getFilters&branchid=6&moduleid=6&menuname=actionlist&userurl=728d70cb0bfbe25c8607d1e281abd523
		return iCareService.getLocations(floorNo);
	}

	@Operation(summary = "fetch actions list data ")
	@GetMapping("/actions-list-data")
	public String actions(@RequestParam("start") int start, @RequestParam("limit") int limit,
			@RequestParam("actionStatus") String actionStatus, @RequestParam("periodIcare") String periodIcare) {
//		https://testaig.performitor.com/appcode/webservices/V2/actionlist.php?tag=actionlistData&branchid=6&moduleid=6&userurl=728d70cb0bfbe25c8607d1e281abd523
//		&start=0&limit=25&ACTIONSTATUS=Assigned&PERIODEICARE=149^
		periodIcare="148^";
		log.info(start+", "+limit+", "+actionStatus+", "+"periodIcare"+periodIcare);
		return iCareService.getActionListData(start, limit, actionStatus, periodIcare);
	}

	@Operation(summary = "fetch actions list details ")
	@GetMapping("/actions-list-details")
	public String actionsListData(@RequestParam("actionStatus") String actionStatus,
			@RequestParam("periodIcare") String periodIcare, @RequestParam("actionListUrl") String actionListUrl) {
//		https://testaig.performitor.com/appcode/webservices/V2/actionlist.php?tag=actionlistDetails&branchid=6&moduleid=6&userurl=728d70cb0bfbe25c8607d1e281abd523
//		&ACTIONSTATUS=Assigned&PERIODEICARE=149^&actionlisturl=db1b6c56179dc729eeafee2d4f7ce7fd
		periodIcare="148^";
		String actionStatus1=actionStatus.trim();
		log.info("actionStatus"+actionStatus+", "+"periodIcare"+periodIcare);
		return iCareService.getActionListDetails(actionStatus1, periodIcare, actionListUrl);
	}

	@Operation(summary = "handle save ")
	@PostMapping(value="/handle/{action}",consumes = "multipart/form-data")
	public String handleActionSave(
			@PathVariable(name="action",required =true) String action,
			@RequestParam(name="file",required = false) MultipartFile file, 
			@RequestParam("actionListUrl") String url,
			@RequestParam("actionStatus") String actionStatus,
			@RequestParam("comment") String comment,
			@RequestParam("buttonType") String buttonType

	) {
//		https://testaig.performitor.com/appcode/webservices/V2/actionlist.php?tag=handleAction&userurl=728d70cb0bfbe25c8607d1e281abd523&branchid=6
//		&moduleid=6&actionlisturl=db1b6c56179dc729eeafee2d4f7ce7fd&ACTIONSTATUS=Assigned&comment=trying to add some comment to tikcet by api&buttontype=save&appname=iCare

		ICareDepartmentsRequestDto request = new ICareDepartmentsRequestDto();
		request.setActionListUrl(url);
		request.setActionStatus(actionStatus);
		request.setComment(comment);
		request.setButtonType(buttonType);
		//System.out.println(request);
		return iCareService.handleActionSave(request,file);
	}

	@Operation(summary = "handle forward")
	@PostMapping("/handle/forward")
	public String handleActionForward(@RequestBody ICareDepartmentsRequestDto request) {
//		https://testaig.performitor.com/appcode/webservices/V2/actionlist.php?tag=handleAction&userurl=728d70cb0bfbe25c8607d1e281abd523&branchid=6&moduleid=6&actionlisturl=db1b6c56179dc729eeafee2d4f7ce7fd&ACTIONSTATUS=Assigned&comment=trying to add some comment to tikcet by api&buttontype=forward&appname=iCare&assignedto=3264

		return iCareService.handleActionForward(request);
	}
	
	

	@Operation(summary = "handle forward")
	@PostMapping(value="/handle/saveupload",consumes = "multipart/form-data")
	public String handleActionForward(@RequestParam("file") MultipartFile file,
			@RequestParam("actionListUrl") String url) {
//		https://testaig.performitor.com/appcode/webservices/V2/actionlist.php?tag=handleAction&userurl=728d70cb0bfbe25c8607d1e281abd523&branchid=6&moduleid=6&actionlisturl=db1b6c56179dc729eeafee2d4f7ce7fd&ACTIONSTATUS=Assigned&comment=trying to add some comment to tikcet by api&buttontype=forward&appname=iCare&assignedto=3264
		ICareDepartmentsRequestDto request = new ICareDepartmentsRequestDto();
		request.setActionListUrl(url);
		request.setActionStatus("Assigned");
		request.setButtonType("save");
		return iCareService.handleActionSave(request, file);
	}

	@Operation(summary = "fetch assignee list")
	@GetMapping("/assginee")
	public String assigneeList(@RequestParam("actionListUrl") String actionListUrl) {
//		https://testaig.performitor.com/appcode/webservices/V2/actionlist.php?tag=getAssigneeList&branchid=6&moduleid=6&userurl=728d70cb0bfbe25c8607d1e281abd523&actionlisturl=db1b6c56179dc729eeafee2d4f7ce7fd
		return iCareService.assigneeList(actionListUrl);
	}

	
	@Operation(summary = "fetch forward user list")
	@GetMapping("/forward/users")
	public String fetchForwardUserList(@RequestParam("actionListUrl") String actionListUrl,@RequestParam("newDriveId") String newDriveId ) {
//		https://testaig.performitor.com/appcode/webservices/V2/actionlist.php?tag=getAssigneeList&branchid=6&moduleid=6&userurl=728d70cb0bfbe25c8607d1e281abd523&actionlisturl=db1b6c56179dc729eeafee2d4f7ce7fd
		
		String response =iCareService.forwardListUsers(actionListUrl, newDriveId);
		log.info(response);
		return response;
	}
	
	
	@Operation(summary = "fetch forward department list")
	@GetMapping("/forward/dprts")
	public String fetchForwardDepartmentList(@RequestParam("actionListUrl") String actionListUrl,@RequestParam("actionStatus") String actionStatus) {
//		https://testaig.performitor.com/appcode/webservices/V2/actionlist.php?tag=getAssigneeList&branchid=6&moduleid=6&userurl=728d70cb0bfbe25c8607d1e281abd523&actionlisturl=db1b6c56179dc729eeafee2d4f7ce7fd
		return iCareService.forwardListDepartments(actionListUrl, actionStatus);
	}
	
	
	
	
	@Operation(summary = "create ticket")
	@PostMapping("/ticket")
	public String createTicket(@RequestBody CreateTicketRequestDto request) {
		return iCareService.createTicket(request);
	}

	
	
	
	@Operation(summary = "create ticket")
	@PostMapping(value = "/ticket/create", consumes = "multipart/form-data")
	public String createTicketWithImageUpload(
			@RequestParam(name="Picture",required = false) MultipartFile file,
			@RequestParam("department") String department,
			@RequestParam("comment") String comment,
			@RequestParam(name="subDepartment",required = false) String subDepartment,
			@RequestParam(name =  "ipnumber",required = false) Integer ipnumber,
			@RequestParam(name = "uhId",required = false) String uhId,
			@RequestParam("location") String location,
			@RequestParam("floorNo") int floorNo,
			@RequestParam(name ="employeeid",required = false) String employeeid,
			@RequestParam (name="issuetype",required = false)String issuetype,
			@RequestParam (name="issuetypeid",required = false)Long issuetypeid
			) {
		
		
		CreateTicketRequestDto request = new CreateTicketRequestDto();
		
		
		request.setDepartment(department);
		request.setComment(comment);
		request.setSubDepartment(subDepartment);
		request.setIpnumber(ipnumber);
		request.setUhId(uhId);
		request.setLocation(location);
		request.setFloorNo(floorNo);
		request.setEmployeeid(employeeid);
		request.setIssuetype(issuetype);
		request.setIssuetypeid(issuetypeid);
		return iCareService.createTicket(request,file);
	}

	@Operation(summary = "view tickets")
	@GetMapping("/ticket/view")
	public String viewTickets(@RequestParam("start") int start, @RequestParam("limit") int limit,
			@RequestParam("uhId") String uhId) {
		log.info(start+", "+limit);
		return iCareService.viewTickets(start, limit, uhId);
	}
	

	

	@Operation(summary = "view ticket details")
	@GetMapping("/ticket/details")
	public String viewTicketDetails(@RequestParam("ticketId") String ticketId) {
		return iCareService.viewTicketDetails(ticketId);
	}

	@Operation(summary = "raise request questions for icare")
	@GetMapping("/ticket/questions")
	public Map<String, Object> fetchRaiseaiseRequestQuestions() {
		return iCareService.fetchRaiseaiseRequestQuestionsImpl();
	}

	@GetMapping("/ticket/question/getAllTicketQuestions")
	public ResponseEntity<List<TicketQuestionsEntity>> getAllTicketQuestions() {
		return ResponseEntity.ok(ticketService.getAllQuestions());
	}

	@GetMapping("/ticket/question/getTicketQuestionById/{id}")
	public ResponseEntity<TicketQuestionsEntity> getTicketQuestionById(@PathVariable Long id) {
		return ResponseEntity.ok(ticketService.getQuestionById(id));
	}

	@PostMapping("/ticket/question/createTicketQuestion")
	public ResponseEntity<TicketQuestionsEntity> createTicketQuestion(@RequestBody TicketQuestionDto ticketQuestion) {
		return new ResponseEntity<>(ticketService.createQuestion(ticketQuestion), HttpStatus.CREATED);
	}

	@PutMapping("/ticket/question/updateTicketQuestion/{id}")
	public ResponseEntity<TicketQuestionsEntity> updateTicketQuestion(@PathVariable Long id,
			@RequestBody TicketQuestionDto ticketQuestion) {
		return ResponseEntity.ok(ticketService.updateQuestion(id, ticketQuestion));
	}


}
