package com.aig.aigone.config;

import java.util.HashMap;
import java.util.Map;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;

import com.zaxxer.hikari.HikariDataSource;

@Configuration
@EnableJpaRepositories(basePackages = {"com.aig.aigone.emrDA.repository"},entityManagerFactoryRef = "emrDataAnalyticsEntityManager", transactionManagerRef = "emrDataAnalyticsTransactionManager")
public class EmrDataAnalyticsDbConfig {


    @Bean(name = "emrDataAnalyticsDataSourceProperties")
    @ConfigurationProperties(prefix = "emrda.datasource")
    DataSourceProperties emrDataAnalyticsDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean(name = "emrDataAnalyticsDataSource")
    DataSource emrDataAnalyticsDataSource(@Qualifier("emrDataAnalyticsDataSourceProperties") DataSourceProperties emrDataAnalyticsDataSourceProperties) {
        return emrDataAnalyticsDataSourceProperties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
    }
    
    @Bean
    LocalContainerEntityManagerFactoryBean emrDataAnalyticsEntityManager(EntityManagerFactoryBuilder builder,@Qualifier("emrDataAnalyticsDataSource") DataSource emrDataAnalyticsDataSource) {
    	Map<String, Object> properties = new HashMap<>();
        properties.put("hibernate.hbm2ddl.auto", "none");
        properties.put("hibernate.properties.hibernate.query.plan_cache_max_size", "1");
        properties.put("hibernate.properties.hibernate.plan_parameter_metadata_max_size", "1");
        
        return builder.dataSource(emrDataAnalyticsDataSource)
                .packages("com.aig.aigone.emrDA.repository")
                .persistenceUnit("emrDADb")
                .properties(properties)
                .build();
    }
 
    @Bean
    PlatformTransactionManager emrDataAnalyticsTransactionManager(LocalContainerEntityManagerFactoryBean emrDataAnalyticsEntityManager) {
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(emrDataAnalyticsEntityManager.getObject());
        return transactionManager;
    }

}