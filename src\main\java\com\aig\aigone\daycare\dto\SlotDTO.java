package com.aig.aigone.daycare.dto;


import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
public class SlotDTO {
	
	@JsonProperty("slotId")
	private Long Id;
	private LocalDateTime startingTime;
    private LocalDateTime endTime;
    private boolean isArrived;
    private Integer bookingId;
    private String comments;
    private PatientDTO patient;
    private String uhid;
    private String modifiedBy;
}
