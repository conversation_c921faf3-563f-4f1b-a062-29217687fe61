package com.aig.aigone.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.model.dto.SendNotificationRequestDto;
import com.aig.aigone.model.dto.WhatsappMessageRequestDto;
import com.aig.aigone.model.dto.WhatsappMessageResponseDto;
import com.aig.aigone.service.NotificationService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/notification")
public class NotificationController {
	
	@Autowired
	private NotificationService notificationService;
	
	@PostMapping("/send")
	public void sendNotification(@RequestBody SendNotificationRequestDto request) {
		notificationService.sendNotification(request);
	}
	
	@PostMapping("/send/whatsapp/message")
	public WhatsappMessageResponseDto sendWhatsappMessage(@RequestBody WhatsappMessageRequestDto request) {
		return notificationService.sendWhatsappMessage(request);
	}
}
