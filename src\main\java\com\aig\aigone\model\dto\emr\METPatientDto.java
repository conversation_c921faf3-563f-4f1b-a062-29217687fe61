package com.aig.aigone.model.dto.emr;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Date;
import java.util.List;

import com.aig.aigone.model.dto.METPatientLogDto;
import com.aig.aigone.model.entity.aigone.METPatientACKStatusEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
public class METPatientDto implements Serializable{

	private static final long serialVersionUID = 1L;

	private Long id;
	private String uhId;
	private Long eventAccnNo;
	private Long eventDtlAccnNo;
	private Date latestStartDateTime;
	private BigDecimal temp;
	private Integer pulse;
	private Integer respiratoryRate;
	private Integer systolicBp;
	private Integer diastolicBp;
	private Integer spo2;
	private String meanArterialPressure;
	private String inspired;
	private String levelOfConsciousness;
	private Integer painScale;
	private String painLocation;
	private String bmi;
	private Integer mewsScore;
	private Integer respiratoryRateMewsScore;
	private Integer spo2MewsScore;
	private Integer inspiredOxygenMewsScore;
	private Integer systolicBpMewsScore;
	private Integer heartRateMewsScore;
	private Integer levelOfConsciousnessMewsScore;
	private Integer tempMewsScore;
	private METPatientACKStatusEnum status;
	private String ackBy;
	private String sentTo;
	private Date ackTime;
	private Date sentTime;
	private String assignBedNum;
	private String patientName;
	private String remarks;
	
	private List<METPatientLogDto> logs;
	
	public METPatientDto(String patientId, Integer eventAccnNo, Integer eventDtlAccnNo, Double temp, Double pulse,
			Double respiratoryRate, Double systolicBp, Double diastolicBp, Double spo2,Double meanArterialPressure, 
			String inspired, String levelOfConsciousness, Double painScale,String painLocation, Double bmi, 
			Double mewsScore, String assignBedNum,String patientName,Instant latestStartDateTime, Long rn
			) {
		super();
		this.uhId = patientId!=null? patientId.trim():null;
		this.eventAccnNo = eventAccnNo.longValue();
		this.eventDtlAccnNo = eventDtlAccnNo.longValue();
		this.temp = (temp != null) ? BigDecimal.valueOf(temp) : BigDecimal.ZERO;
		this.pulse = (pulse != null) ?  pulse.intValue() : 0;
		this.respiratoryRate = (respiratoryRate != null ) ? respiratoryRate.intValue() : 0;
		this.systolicBp = (systolicBp != null) ? systolicBp.intValue() : 0;
		this.diastolicBp = (diastolicBp != null) ? diastolicBp.intValue(): 0;
		this.spo2 =  ((spo2 != null) ?spo2.intValue() : 0);
		this.meanArterialPressure = meanArterialPressure != null ? meanArterialPressure.toString() : null;
		this.inspired = inspired != null? inspired.trim():null;
		this.levelOfConsciousness = levelOfConsciousness != null ? levelOfConsciousness.trim() : null;
		this.painScale = (painScale != null) ? painScale.intValue() : 0;
		this.painLocation = painLocation != null ? painLocation.trim() : null;
		this.bmi = bmi != null ? bmi.toString() : null;
		this.mewsScore = (mewsScore != null) ? mewsScore.intValue() : 0;
		this.assignBedNum = assignBedNum != null ? assignBedNum.trim() : null;
		this.patientName = patientName != null ? patientName.trim() : null;
		this.latestStartDateTime = Date.from(latestStartDateTime);
	}
	
	public METPatientDto() {
    }
	
	
}
