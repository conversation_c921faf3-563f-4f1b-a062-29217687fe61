package com.aig.aigone.daycare.service;

import java.util.List;

import com.aig.aigone.daycare.dto.PatientDTO;
import com.aig.aigone.daycare.his.model.BedType;
import com.aig.aigone.daycare.his.model.Company;
import com.aig.aigone.daycare.his.model.Procedure;
import com.aig.aigone.daycare.his.model.Station;

public interface HisServiceDaycare {
	public List<Station> getStation();

	public List<BedType> getBedtype();

	PatientDTO getPatientByUHID(String aicode, Integer regNo);

	public List<Procedure> getProcedure();

	public List<Company> getCompany();
}
