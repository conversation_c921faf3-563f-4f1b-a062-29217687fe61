package com.aig.aigone.daycare.dto;

import java.time.LocalDate;
import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BookingTimeDTO implements Comparable<BookingTimeDTO> {
	private LocalDateTime startTime;
	private LocalDateTime endTime;
	
    @Override
    public int compareTo(BookingTimeDTO other) {
        // Compare based on startTime
        return this.startTime.compareTo(other.getStartTime());
    }
}
