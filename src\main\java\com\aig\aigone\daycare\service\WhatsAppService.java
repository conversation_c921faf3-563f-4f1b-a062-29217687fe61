package com.aig.aigone.daycare.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
public class WhatsAppService {

	@Autowired
	private RestTemplate restTemplate;

	@Value("${whatsapp.api.url}")
	private String apiUrl;

	@Value("${whatsapp.api.username}")
	private String username;

	@Value("${whatsapp.api.password}")
	private String password;

	public ResponseEntity<String> sendDialysisNotification(String event, String phoneNumber, String... vars) {

		String jsonPayload = createPayload(event, phoneNumber, vars);

		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.setBasicAuth(username, password);

		HttpEntity<String> request = new HttpEntity<>(jsonPayload, headers);

		return restTemplate.exchange(apiUrl, HttpMethod.POST, request, String.class);
	}

	private String createPayload(String event, String phoneNumber, String[] vars) {

		// building json request body from string input
		StringBuilder jsonBody = new StringBuilder();
		if (!phoneNumber.startsWith("91"))
			phoneNumber = "91" + phoneNumber;
		jsonBody.append("{\n");
		jsonBody.append("\"event\": \"").append(event).append("\",\n");
		jsonBody.append("\"phoneNumber\": \"").append(phoneNumber).append("\",\n");
		for (int i = 0; i < vars.length; i++) {
			jsonBody.append("\"var").append(i + 1).append("\": \"").append(vars[i]).append("\"");
			if (i != vars.length - 1) {
				jsonBody.append(",\n");
			}
		}
		jsonBody.append("\n}");
		return jsonBody.toString();
	}
}
