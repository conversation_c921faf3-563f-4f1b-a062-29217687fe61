package com.aig.aigone.bloodbank.model.dto;

import java.io.Serializable;
import java.time.LocalDate;


import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
//@Data
@AllArgsConstructor
@Getter
@Setter
public class DonorBasicInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    
    
//    @JsonProperty(access = JsonProperty.Access.READ_ONLY) 
    private Long id;
    private String name;
    private String mobile;
    private String bloodGroup;
    private String age;
    private String gender;
    private String lastDonationDate;
    private String employeeId;
    private String donorType;
    private String coMorbities;
    private String medications;
    private LocalDate registrationDate;
    private Boolean eligibleForDonation;
    private String lastDonationText;



}
