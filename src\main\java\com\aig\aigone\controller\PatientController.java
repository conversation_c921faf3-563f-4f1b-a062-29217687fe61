package com.aig.aigone.controller;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.aig.aigone.exception.AigOneException;
import com.aig.aigone.model.dto.PatientAmnesiaDetailsDTO;
import com.aig.aigone.model.dto.PatientAttendantDto;
import com.aig.aigone.model.dto.PatientRequestAppointmentDto;
import com.aig.aigone.model.dto.PatientTestDTO;
import com.aig.aigone.model.dto.PatientVisitorPassDto;
import com.aig.aigone.model.dto.UserSessionVO;
import com.aig.aigone.model.dto.his.DoctorAvailableSlotsDto;
import com.aig.aigone.model.dto.his.HisResponseDto;
import com.aig.aigone.model.dto.his.HisResponseListDto;
import com.aig.aigone.model.dto.his.LocationDto;
import com.aig.aigone.model.dto.his.OrderDto;
import com.aig.aigone.model.dto.his.OrderResponseDto;
import com.aig.aigone.model.dto.his.PateintAppointmentsDto;
import com.aig.aigone.model.dto.his.PateintPreviousAppointmentsDto;
import com.aig.aigone.model.dto.his.PatientAbnoramlReportsResponseDto;
import com.aig.aigone.model.dto.his.PatientDetailsDto;
import com.aig.aigone.model.dto.his.PatientDocumentsDto;
import com.aig.aigone.model.dto.his.PatientIPDetailsDto;
import com.aig.aigone.model.dto.his.PatientRegistrationRequestDto;
import com.aig.aigone.model.dto.his.PatientReportDetailsDto;
import com.aig.aigone.model.dto.his.PatientReportsResponseDto;
import com.aig.aigone.model.dto.his.PaymentInfoDto;
import com.aig.aigone.model.dto.his.ProcedureGuideDto;
import com.aig.aigone.model.dto.his.UploadRequest;
import com.aig.aigone.model.dto.his.ViewReportRequestDto;
import com.aig.aigone.model.dto.his.WellnessPackageDto;
import com.aig.aigone.model.entity.aigone.AigAssistantEntity;
import com.aig.aigone.model.entity.aigone.FaqEntity;
import com.aig.aigone.model.entity.aigone.PatientAmnesiaDetails;
import com.aig.aigone.model.entity.aigone.TicketQuesEnum;
import com.aig.aigone.model.entity.aigone.TicketQuestionsEntity;
import com.aig.aigone.security.SecurityUtil;
import com.aig.aigone.service.HisService;

import io.swagger.v3.oas.annotations.Operation;

@RestController
@RequestMapping("/api/his/patient")
public class PatientController {

	@Autowired
	private HisService hisService;

//	@Autowired
//	private DialysisService dialysisService;

//	@Operation(summary= "Get Upcoming Slot")
//	@GetMapping("/dialysis/slot/{uhId}")
//	public String getUpcomingSlot(@PathVariable(name = "uhId", required = true) String uhId) {
//		return dialysisService.getUpcomingSlot(uhId);
//	}

	@Operation(summary = "Register Patient ")
	@PostMapping("/register")
	public String registration(@RequestBody PatientRegistrationRequestDto patiRegistrationRequestDto) {
		return hisService.registerPatient(patiRegistrationRequestDto);
	}

	@Operation(summary = "fetch Patient Profile ")
	@GetMapping("/profile")
	public String patientProfile() {
		return null;
	}

	@Operation(summary = "fetch Patient Details ")
	@GetMapping("/{uhId}")
	public HisResponseDto<PatientDetailsDto> patientDetails(@PathVariable(name = "uhId", required = true) String uhId) {
		return hisService.fetchPatientDetailsByUhId(uhId);
	}

	@Operation(summary = "fetch Patient Members ")
	@GetMapping("/members")
	public HisResponseListDto<PatientDetailsDto> patientMembers() {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return hisService.fetchPatientsByPhoneNo(String.valueOf(currentUser.getPhoneNo()));
	}

	
	@Operation(summary = "fetch Patient Members ")
	@GetMapping("/byPhoneno/{phoneno}")
	public HisResponseListDto<PatientDetailsDto> patientMembersByPhoneno(@PathVariable String phoneno) {
		//UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return hisService.fetchPatientsByPhoneNo(phoneno);
	}
    
	
	@Operation(summary = "add member ")
	@PostMapping("/members/add")
	public String addNewMember() {
		return null;
	}

	@Operation(summary = "fetch Patient Appointments ")
	@PostMapping("/member/appointments")
	public String patinetAppointments() {
		return null;
	}

	@Operation(summary = "fetch Lab Orders ")
	@GetMapping("/lab/orders/{uhId}")
	public List<OrderDto> labOrders(@PathVariable(name = "uhId", required = true) String uhId) {
		return hisService.fetchLabOrdersByUhId(uhId);
	}

	@Operation(summary = "fetch idcards ")
	@GetMapping("/master/idcards")
	public List<String> fetchIdCards() {
		return hisService.fetchAllIdCards();
	}

	@Operation(summary = "fetch gender ")
	@GetMapping("/master/gender")
	public List<String> genderMaster() {
		return hisService.fetchGenderMaster();
	}

	@Operation(summary = "fetch location by pincode ")
	@GetMapping("/master/location/{pincode}")
	public List<LocationDto> locationByPincode(@PathVariable(name = "pincode", required = true) String pincode) {
		return hisService.fetchLocationByPincode(pincode);
	}

	@Operation(summary = "fetch states ")
	@GetMapping("/master/states")
	public List<String> fetchStates() {
		return hisService.fetchStates();
	}

	@Operation(summary = "fetch cities ")
	@GetMapping("/master/cities/{stateName}")
	public List<String> fetchCities(@PathVariable(name = "stateName", required = true) String stateName) {
		return hisService.fetchCities(stateName);
	}

	@Operation(summary = "fetch title ")
	@GetMapping("/master/title")
	public List<String> fetchTitle() {
		return hisService.fetchTitle();
	}

	@Operation(summary = "fetch marital status ")
	@GetMapping("/master/maritalStatus")
	public List<String> fetchMaritalStatus() {
		return hisService.fetchMaritalStatus();
	}

	@Operation(summary = "fetch payment info ")
	@GetMapping("/paymentInfo")
	public String fetchPaymentInfo(@RequestBody PaymentInfoDto paymentInfoDto) {
		return hisService.fetchPaymentInfo(paymentInfoDto);
	}

	@Operation(summary = "fetch Patient Appointments")
	@GetMapping("/appointments/{uhId}")
	public List<PateintAppointmentsDto> appointments(@PathVariable(name = "uhId", required = true) String uhId) {
		return hisService.fetchAppointmentsByUhIdAndDate(uhId, new Date());
	}

	@Operation(summary = "fetch patient IP details ")
	@GetMapping("/ip/{uhId}")
	public PatientIPDetailsDto ipDetails(@PathVariable(name = "uhId", required = true) String uhId) {
		List<PatientIPDetailsDto> ipDetails = hisService.fetchPatiennIpDetailsByUhId(uhId,
				SecurityUtil.getCurrentUser().getEmployeeId());
		if (!CollectionUtils.isEmpty(ipDetails)) {
			return ipDetails.get(0);
		}

		return null;
	}

	@Operation(summary = "fetch patient IP details ")
	@GetMapping("/ip/bedno/{bedNo}")
	public PatientIPDetailsDto ipDetailsByBedNo(@PathVariable(name = "bedNo", required = true) String bedNo) {
		List<PatientIPDetailsDto> ipDetails = hisService.fetchPatiennIpDetailsByBedNo(bedNo);
		if (!CollectionUtils.isEmpty(ipDetails)) {
			return ipDetails.get(0);
		}

		return null;
	}

	@Operation(summary = "update procedure info guide ")
	@PostMapping("/upload")
	public String uploadFile(@RequestParam("file") MultipartFile file) {
		try {
			hisService.processExcelFile(file);
			return "File Updated Successfully";
		} catch (Exception e) {
			e.printStackTrace();
			throw new AigOneException("Failed to update Excel File");
		}
	}

	@Operation(summary = "fetch FAQ's ")
	@GetMapping("/faqs/{type}")
	public List<FaqEntity> fetchFaqsByType(@PathVariable(name = "type", required = true) String type) {
		List<FaqEntity> response = hisService.fetchFaqsByType(type);
		return response;
	}

	@Operation(summary = "fetch completed orders ")
	@GetMapping("/lab/orders/completed/{uhId}")
	public List<OrderDto> completedOrders(@PathVariable(name = "uhId", required = true) String uhId) {
		return hisService.fetchCompletedOrdersByUhId(uhId);
	}

	@Operation(summary = "create order")
	@PostMapping("/order")
	public HisResponseListDto<OrderResponseDto> createOrder(@RequestBody OrderDto order) {
		return hisService.createOrder(order);
	}

	@Operation(summary = "fetch Patient Previous Appointments")
	@GetMapping("/previous/appointments/{uhId}")
	public List<PateintPreviousAppointmentsDto> previousAppointments(
			@PathVariable(name = "uhId", required = true) String uhId) {
		return hisService.fetchPreviousAppointmentsByUhId(uhId);
	}

	@Operation(summary = "book an appointment")
	@PostMapping("/appointment/book")
	public String bookAppointment(@RequestBody DoctorAvailableSlotsDto reSlotsDto) {
		return hisService.savePatientAppointmentWithDoctor(reSlotsDto);
	}

	@Operation(summary = "fetch wellness packages")
	@GetMapping("/wellnessPackages")
	public List<WellnessPackageDto> fetchWellnessPackage() {
		List<WellnessPackageDto> response = hisService.fetchWellnessPackage();
		return response;
	}

	@Operation(summary = "fetch ticket questions ")
	@GetMapping("/ticketQuestions")
	public List<TicketQuestionsEntity> fetchTicketQuestions(
			@RequestParam(name = "question", required = false) String question) {
		List<TicketQuestionsEntity> response = hisService.fetchTicketQuestions(question);
		return response;
	}

	@Operation(summary = "Delete/Cancel Appointment")
	@PostMapping("/appointment/delete/{appointmentId}")
	public String deleteAppointment(@PathVariable(name = "appointmentId", required = true) Long appointmentId) {
		return hisService.deleteAppointment(appointmentId);
	}

	@Operation(summary = "Appointments")
	@GetMapping("/appointment/details/{appointmentId}")
	public PateintAppointmentsDto appointmentDetails(
			@PathVariable(name = "appointmentId", required = true) Long appointmentId) {
		List<PateintAppointmentsDto> fecthAppointmentByAppointmentId = hisService
				.fecthAppointmentByAppointmentId(appointmentId);
		return fecthAppointmentByAppointmentId.get(0);
	}

	@Operation(summary = "fetch procedure guide")
	@GetMapping("/procedureGuide")
	public List<ProcedureGuideDto> fetchProcedureGuide() {
		List<ProcedureGuideDto> response = hisService.fetchProcedureGuide();
		return response;
	}

	@Operation(summary = "fetch aig assistant details")
	@GetMapping("/aig-assistant/{type}")
	public List<AigAssistantEntity> fetchAigAssistantDetails(
			@PathVariable(name = "type", required = true) String type) {
		return hisService.fetchAigAssistantDetails(type);
	}

	@Operation(summary = "Upload Files")
	@PostMapping("/uploadDoc")
	public String upload(@RequestBody UploadRequest request) {
		return hisService.uploadDoc(request);

	}

	@Operation(summary = "fetch patient reports")
	@GetMapping("/reports/{type}/{uhId}")
	public HisResponseListDto<PatientReportsResponseDto> fetchPatientReports(
			@PathVariable(name = "type", required = true) String type,
			@PathVariable(name = "uhId", required = true) String uhId) {
		return hisService.fetchPatientReports(type, uhId.toUpperCase());
	}

	@Operation(summary = "fetch patient reports")
	@GetMapping("/reports/all/{uhId}")
	public HisResponseListDto<PatientReportsResponseDto> fetchAllPatientReports(
			@PathVariable(name = "uhId", required = true) String uhId) {
		return hisService.fetchAllPatientReports(uhId.toUpperCase());
	}
	
	@Operation(summary = "fetch patient reports")
	@GetMapping("/reports/allAbnormal/{uhId}")
	public HisResponseListDto<PatientAbnoramlReportsResponseDto> fetchAllAbnormalLabValues(
			@PathVariable(name = "uhId", required = true) String uhId) {
		return hisService.fetchAllAbnormalLabValues(uhId.toUpperCase());
	}


	@Operation(summary = "fetch base64 report")
	@PostMapping("/reports/view")
	public String fetchBase64Report(@RequestBody ViewReportRequestDto viewReportDto) {
		return hisService.fetchBase64Report(viewReportDto);
	}

	@Operation(summary = "fetch base64 document And PAcks Link")
	@PostMapping("/reports/pacs/view")
	public PatientReportDetailsDto fetchBase64AndPAcsDocument(@RequestBody ViewReportRequestDto patientDocDto) {
		return hisService.fetchBase64AndPacsReport(patientDocDto);
	}

	@Operation(summary = "fetch patient documents")
	@GetMapping("/documents/{uhId}")
	public List<PatientDocumentsDto> fetchPatientDocuments(@PathVariable(name = "uhId", required = true) String uhId) {
		return hisService.fetchPatientDocuments(uhId);
	}

	@Operation(summary = "fetch patient consultations")
	@GetMapping("/consultations/{uhId}")
	public List<PatientDocumentsDto> fetchPatientConsultations(
			@PathVariable(name = "uhId", required = true) String uhId) {
		return hisService.fetchPatientConsultations(uhId);
	}

	@Operation(summary = "fetch base64 document")
	@PostMapping("/document/view")
	public String fetchBase64Document(@RequestBody PatientDocumentsDto patientDocDto) {
		return hisService.fetchBase64Document(patientDocDto);
	}

	@Operation(summary = "fetch user token for endoscopy")
	@GetMapping("/userToken/{uhId}")
	public String fetchUserTokenEndoscopy(@PathVariable(name = "uhId", required = true) String uhId) {
		return hisService.fetchUserTokenEndoscopy(uhId);
	}

	@Operation(summary = "save patient attendant")
	@PostMapping("/attendant/save")
	public List<PatientAttendantDto> savePatientAttendant(@RequestBody PatientAttendantDto attendantDto) {
		return hisService.savePatientAttendant(attendantDto);
	}

	@Operation(summary = "fetch patient attendants")
	@GetMapping("/attendants/{uhId}")
	public List<PatientAttendantDto> fetchPatientAttendants(@PathVariable(name = "uhId", required = true) String uhId) {
		return hisService.fetchPatientAttendants(uhId);
	}

	@Operation(summary = "delete patient attendant")
	@PostMapping("/attendant/delete/{attendantId}")
	public String deletePatientAttendant(@PathVariable(name = "attendantId", required = true) Long attendantId) {
		return hisService.deletePatientAttendant(attendantId);
	}

	@Operation(summary = "fetch attendant patients")
	@GetMapping("/attendant/patients")
	public HisResponseListDto fetchAttendantPatients() {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return hisService.fetchAttendantPatients(Long.valueOf(currentUser.getPhoneNo()));
	}

	@Operation(summary = "patient request appointment")
	@PostMapping("/appointment/request")
	public String patientRequestAppointment(@RequestBody PatientRequestAppointmentDto appointmentDto) {
		return hisService.patientRequestAppointment(appointmentDto);
	}

	@Operation(summary = "create a visitor pass")
	@PostMapping("/visitorPass/create")
	public String createVisitorPass(@RequestBody PatientVisitorPassDto visitorDto) {
		return hisService.createVisitorPass(visitorDto);
	}

	@Operation(summary = "view visitor pass")
	@GetMapping("/visitorPass/view")
	public List<PatientVisitorPassDto> fetchVisitorPass(
			@RequestParam(name = "visitorPhoneNo", required = true) Long visitorPhoneNo,
			@RequestParam(name = "uhId", required = true) String uhId,
			@RequestParam(name = "validate", required = false) boolean validate) {
		return hisService.fetchVisitorPass(visitorPhoneNo, uhId, validate);
	}

	@Operation(summary = "feth all active visitor passes")
	@GetMapping("visitorPass/active/{uhId}/{ipNumber}")
	public List<PatientVisitorPassDto> fetchActiveIPVistorPass(
			@PathVariable(name = "uhId", required = true) String uhId,
			@PathVariable(name = "ipNumber", required = true) Long ipNumber) {
		return hisService.fetchActiveIPVistorPass(uhId, ipNumber);
	}

	@Operation(summary = "fetch ticket questions by type ")
	@GetMapping("/ticketQuestions/{type}")
	public List<TicketQuestionsEntity> fetchTicketQuestionsByType(
			@PathVariable(name = "type", required = false) TicketQuesEnum type) {
		List<TicketQuestionsEntity> response = hisService.fetchTicketQuestionsByType(type);
		return response;
	}

	@Operation(summary = "Retrieve patients who have taken CBP/CBC tests")
	@GetMapping("/cbp-cbc")
	public ResponseEntity<List<PatientTestDTO>> getPatientsWithCBPCBC(
	        @RequestParam(name = "date", required = false) @DateTimeFormat(pattern = "dd-MM-yyyy") String date,
	        @RequestParam(name = "uhid", required = false) String uhid,
	        @RequestParam(name = "isCaptured", required = false) Boolean isCaptured) {  // New parameter

	    List<PatientTestDTO> response = hisService.getPatientsWithCBPCBC(date, uhid, isCaptured);
	    return ResponseEntity.ok(response);
	}


	@PostMapping("/cbp-cbc/captureDetails")
	@Operation(summary = "Post the Captured Details of PatientAmnesiaDetails ")
	public ResponseEntity<Map<String, String>> capturePatientDetails(
			@RequestBody PatientAmnesiaDetails patientDetails) {
		String message = hisService.capturePatientDetails(patientDetails);

		Map<String, String> response = new HashMap<>();
		response.put("status", "success");
		response.put("message", message);

		return ResponseEntity.ok(response);
	}

	@Operation(summary = "Retrieve Captured Details by UHID")
	@GetMapping("/cbp-cbc/getCapturedDetails")
	public ResponseEntity<List<PatientAmnesiaDetailsDTO>> getCapturedDetails(@RequestParam String UHID) {
		List<PatientAmnesiaDetailsDTO> patientDetails = hisService.getCapturedDetailsByUhid(UHID);
		return ResponseEntity.ok(patientDetails);
	}
	
	@Operation(summary = "Retrieve Captured Details by ID")
	@GetMapping("/cbp-cbc/getCapturedDetailsById")
	public ResponseEntity<PatientAmnesiaDetailsDTO> getCapturedDetailsById(@RequestParam Long id) {
	    PatientAmnesiaDetailsDTO patientDetails = hisService.getCapturedDetailsById(id);
	    return ResponseEntity.ok(patientDetails);
	}


	@PutMapping("/cbp-cbc/updateDetails/{id}")
	@Operation(summary = "Update Captured Details of PatientAmnesia")
	public ResponseEntity<Map<String, Object>> updatePatientDetails(@PathVariable Long id,
	        @RequestBody PatientAmnesiaDetails updatedDetails) {

	    // Call service to update patient details and get the updated entity
	    PatientAmnesiaDetails updatedData = hisService.updatePatientDetails(id, updatedDetails);

	    // Create response with updated data
	    Map<String, Object> response = new HashMap<>();
	    response.put("status", "success");
	    response.put("message", "Patient details updated successfully.");
	    response.put("updatedData", updatedData);  // Sending updated data

	    return ResponseEntity.ok(response);
	}
	
	
	@GetMapping("/getOrdersByVisit/{visitId}")
    public ResponseEntity<List<String>> getInterfaceIdsByVisitId(@PathVariable Long visitId) {
        List<String> interfaceIds = hisService.getInterfaceIdsByVisitId(visitId);
        return ResponseEntity.ok(interfaceIds);
    }


}
