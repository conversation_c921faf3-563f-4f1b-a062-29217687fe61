package com.aig.aigone.daycare.dto;

import java.util.List;

import com.aig.aigone.daycare.model.Booking;
import com.aig.aigone.daycare.model.DialysisBooking;
import com.aig.aigone.daycare.model.Slot;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ExistingBookingDetailsDTO {

    private Booking booking;
    private List<Slot> patientBookedSlots; 
    private BookingStatusResponseDTO bookingStatusResponse; 
    private List<DialysisBooking> dialysisBookingList;
//    private List<DialysisBookingDetails> dialysisBookingDetailsList;
    
    // Constructor
    public ExistingBookingDetailsDTO(Booking booking, List<Slot> patientBookedSlots, BookingStatusResponseDTO bookingStatusResponse,List<DialysisBooking> dialysisBookingList) {
        this.booking = booking;
        this.patientBookedSlots = patientBookedSlots; 
        this.bookingStatusResponse = bookingStatusResponse;
        this.dialysisBookingList=dialysisBookingList;
    }

    
}

