package com.aig.aigone.model.entity.dfcms;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Table(name = "tbl_dbfms_transaction")
@Data
public class TransactionEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String qrcodeId;

    @Column(nullable = false)
    private Long empCode;

    @Column(nullable = false)
    private String status; // active, redeemed, cancelled, expired

    @Column(nullable = false)
    private LocalDateTime transactionDate;

    @Column(nullable = false)
    private String operation; // CREATE, VALIDATE, CANCEL, etc.
}
