package com.aig.aigone.labreport.model.dto;

import java.io.Serializable;
import java.util.Base64;

import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class LabVerifiedDoctorsListDto implements Serializable{
	
	private static final long serialVersionUID = 1L;
	
	private Integer orderId;
    private Integer testId;
    private Integer labDocId;
    private String title;
    private String doctorName;
    private String designation;
    private String qualification;
    private String signatureBase64;

    public LabVerifiedDoctorsListDto(Integer orderId, Integer testId,Integer labDocId, String title, String doctorName,
                            String designation, String qualification, byte[] signature) {
        this.orderId = orderId;
        this.testId = testId;
        this.labDocId=labDocId;
        this.title = title;
        this.doctorName = (title != null ? title.trim(): "") + (doctorName != null ? doctorName.trim() : "");;
        this.designation = designation;
        this.qualification = qualification;
        this.signatureBase64 = (signature != null) ? Base64.getEncoder().encodeToString(signature) : null;
    }

	
}
