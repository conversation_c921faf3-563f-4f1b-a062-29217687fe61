package com.aig.aigone.controller;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.model.dto.BannerDTO;
import com.aig.aigone.service.BannerService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/api/banners")
@Tag(name = "Banner Management", description = "APIs for managing banner carousel")
@Slf4j
public class BannerController {

    @Autowired
    private BannerService bannerService;

    @GetMapping("/active")
    @Operation(summary = "Get all active banners")
    public ResponseEntity<List<BannerDTO>> getAllActiveBanners() {
        log.info("Fetching all active banners");
        return ResponseEntity.ok(bannerService.getAllActiveBanners());
    }

    @GetMapping
    @Operation(summary = "Get all banners (active and inactive)")
    public ResponseEntity<List<BannerDTO>> getAllBanners(
            @RequestParam(value = "bannerType", required = false) String bannerType,
            @RequestParam(value = "status", required = false) Boolean status) {
        log.info("Fetching all banners{}{}",
            bannerType != null ? " with bannerType=" + bannerType : "",
            status != null ? ", status=" + status : "");
        return ResponseEntity.ok(bannerService.getAllBanners(Optional.ofNullable(bannerType), Optional.ofNullable(status)));
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get banner by ID")
    public ResponseEntity<BannerDTO> getBannerById(@PathVariable Long id) {
        log.info("Fetching banner with id: {}", id);
        return ResponseEntity.ok(bannerService.getBannerById(id));
    }

    @PostMapping
    @Operation(summary = "Create a new banner")
    public ResponseEntity<BannerDTO> createBanner(@RequestBody BannerDTO bannerDTO) {
        log.info("Creating new banner: {}", bannerDTO);
        return new ResponseEntity<>(bannerService.createBanner(bannerDTO), HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update an existing banner")
    public ResponseEntity<BannerDTO> updateBanner(@PathVariable Long id, @RequestBody BannerDTO bannerDTO) {
        log.info("Updating banner with id: {}", id);
        return ResponseEntity.ok(bannerService.updateBanner(id, bannerDTO));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete a banner")
    public ResponseEntity<Void> deleteBanner(@PathVariable Long id) {
        log.info("Deleting banner with id: {}", id);
        bannerService.deleteBanner(id);
        return ResponseEntity.noContent().build();
    }
} 