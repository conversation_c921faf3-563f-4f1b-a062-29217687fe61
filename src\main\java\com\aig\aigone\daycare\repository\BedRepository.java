package com.aig.aigone.daycare.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.aig.aigone.daycare.model.Bed;
@Repository
public interface BedRepository extends JpaRepository<Bed, Long>{

	@Query("SELECT b from Bed b WHERE"
			+ " b.stationId = :stationId AND b.bedtypeId = :bedType AND b.category = :category")
	List<Bed> getBed(@Param("stationId")Integer stationId,@Param("bedType")Integer Bedtype, @Param("category")Integer category);
	
	
	@Query("SELECT COUNT(b) FROM Bed b WHERE b.stationId = :stationId AND b.bedtypeId = :bedType AND b.category = :category ")
	Integer getBedCount(@Param("stationId")Integer stationId,@Param("bedType")Integer Bedtype, @Param("category")Integer category);
}
