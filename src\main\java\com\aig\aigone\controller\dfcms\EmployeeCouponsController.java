package com.aig.aigone.controller.dfcms;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.model.dto.dfcms.EmployeeCouponsDTO;
import com.aig.aigone.model.entity.dfcms.EmployeeCoupons;
import com.aig.aigone.service.dfcms.EmployeeCouponsService;

import io.swagger.v3.oas.annotations.Operation;

@RestController
@RequestMapping("/api/dfcms/employee-coupons")
public class EmployeeCouponsController {

	private static final Logger LOGGER = LoggerFactory.getLogger(EmployeeCouponsController.class);

	private final EmployeeCouponsService employeeCouponsService;

	public EmployeeCouponsController(EmployeeCouponsService employeeCouponsService) {
		this.employeeCouponsService = employeeCouponsService;
	}

	@GetMapping
	@PreAuthorize("hasAnyRole('DC_ADMIN', 'HR','COCKPIT_ADMIN')")
	@Operation(summary = "Get All Employee Coupons")
	public ResponseEntity<List<EmployeeCouponsDTO>> getAllEmployeeCoupons() {
		LOGGER.info("Received request to fetch all employee coupons.");
		List<EmployeeCouponsDTO> coupons = employeeCouponsService.getAllEmployeeCoupons();
		return ResponseEntity.ok(coupons); // Success response
	}

	@GetMapping("/getCouponsAvailable/{empId}")
	@Operation(summary = "Get Employee Coupons by Employee ID")
	public ResponseEntity<List<EmployeeCoupons>> getEmployeeCouponsByEmpId(@PathVariable String empId) {
		LOGGER.info("Received request to fetch employee coupons for Employee ID: {}", empId);
		List<EmployeeCoupons> coupons = employeeCouponsService.getEmployeeCouponsByEmpId(empId);
		return ResponseEntity.ok(coupons);
	}

	@GetMapping("/getCouponsAvailableAllData/{empId}")
	@Operation(summary = "Get Employee Coupons by Employee ID with all data")
	public ResponseEntity<List<EmployeeCoupons>> getEmployeeCouponsByEmpIdAll(@PathVariable String empId) {
		LOGGER.info("Received request to fetch employee coupons for Employee ID: {}", empId);
		List<EmployeeCoupons> coupons = employeeCouponsService.getEmployeeCouponsByEmpIdAllData(empId);
		return ResponseEntity.ok(coupons);
	}

	// Create a new employee coupon
	@PostMapping("/add")
	@PreAuthorize("hasAnyRole('DC_ADMIN', 'HR','COCKPIT_ADMIN')")
	@Operation(summary = "Add Employee Coupon")
	public ResponseEntity<?> addEmployeeCoupon(@RequestBody List<EmployeeCouponsDTO> employeeCouponsDTO) {
		LOGGER.info("Adding a new employee coupon.");
		List<EmployeeCoupons> createdCoupon = employeeCouponsService.addEmployeeCoupons(employeeCouponsDTO);
		return ResponseEntity.status(HttpStatus.CREATED).body(createdCoupon);
	}

	// Update an existing employee coupon
	@PutMapping("/edit/{id}")
	@PreAuthorize("hasAnyRole('DC_ADMIN', 'HR','COCKPIT_ADMIN')")
	@Operation(summary = "Update Employee Coupon")
	public ResponseEntity<?> updateEmployeeCoupon(@PathVariable Integer id,
			@RequestBody EmployeeCouponsDTO employeeCouponsDTO) {
		LOGGER.info("Received request to update employee coupon with ID: {}", id);
		EmployeeCoupons updatedCoupon = employeeCouponsService.updateEmployeeCoupon(id, employeeCouponsDTO);
		return ResponseEntity.ok(updatedCoupon);
	}

	// Delete an employee coupon
	@DeleteMapping("/delete/{id}")
	@PreAuthorize("hasAnyRole('DC_ADMIN','COCKPIT_ADMIN')")
	@Operation(summary = "Delete Employee Coupon")
	public ResponseEntity<?> deleteEmployeeCoupon(@PathVariable Integer id) {
		LOGGER.info("Received request to delete employee coupon with ID: {}", id);
		employeeCouponsService.deleteEmployeeCoupon(id);
		return ResponseEntity.noContent().build();
	}

	@GetMapping("/getCouponsByMonth/{year}/{month}")
	@Operation(summary = "Get Employee Coupons by Year and Month")
	public ResponseEntity<List<EmployeeCoupons>> getEmployeeCouponsByMonth(@PathVariable int year,
			@PathVariable int month) {
		LOGGER.info("Received request to fetch employee coupons for Year: {}, Month: {}", year, month);
		List<EmployeeCoupons> coupons = employeeCouponsService.getEmployeeCouponsByMonth(year, month);
		return ResponseEntity.ok(coupons);
	}

	@PreAuthorize("hasAnyRole('COCKPIT_ADMIN','HR')")
	@PostMapping("/addAdditionalCoupons/{id}/{additionalQty}")
	@Operation(summary = "Add Additional Coupons for an Employee")
	public ResponseEntity<EmployeeCoupons> addAdditionalCoupons(@PathVariable Integer id,
			@PathVariable Long additionalQty) {

		LOGGER.info("Received request to add {} additional coupons for Employee Coupon ID: {}", additionalQty, id);

		EmployeeCoupons updatedCoupon = employeeCouponsService.updateAdditionalCoupons(id, additionalQty);

		return ResponseEntity.ok(updatedCoupon);
	}

}
