package com.aig.aigone.bloodbank.controller;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;

import com.aig.aigone.bloodbank.model.dto.*;
import com.aig.aigone.bloodbank.service.DonorService;
import com.aig.aigone.bloodbank.service.impl.BloodbankWhatsAppMsg;
import com.aig.aigone.bloodbank.service.impl.DonorServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;

import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import org.slf4j.Logger;
@RestController
@RequestMapping("/api/donors")
@Slf4j
public class DonorController {

    @Autowired
    private DonorService donorService;

  

    @PostMapping("/register")
    public ResponseEntity<Map<String, Object>> registerDonor(@RequestBody DonorBasicInfoDTO donorDto) {
        return donorService.registerDonorWithResponse(donorDto);
    }

    @GetMapping
    public ResponseEntity<List<DonorBasicInfoDTO>> getAllDonors() {
        return ResponseEntity.ok(donorService.getAllDonors());
    }

    @GetMapping("/{employeeId}")
    public ResponseEntity<DonorBasicInfoDTO> getDonorByEmployeeId(@PathVariable String employeeId) {
        return ResponseEntity.ok(donorService.getDonorByEmployeeId(employeeId));
    }

    @PutMapping("/update-donation-date/{donorId}")
    public ResponseEntity<Map<String, String>> updateDonationDate(
            @PathVariable Long donorId,
            @RequestParam("date") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate donationDate,
            @RequestParam(value = "coMorbities", required = false) String coMorbities,
            @RequestParam(value = "medications", required = false) String medications) {
        return donorService.updateLastDonationDateWithResponse(donorId, donationDate, coMorbities, medications);
    }

    @PostMapping("/send-whatsapp")
    public Mono<ResponseEntity<String>> sendWhatsAppToDonors(@RequestBody WhatsAppSendRequest request)
            throws JsonProcessingException {
        return donorService.sendWhatsAppToDonors(request);
    }


}
