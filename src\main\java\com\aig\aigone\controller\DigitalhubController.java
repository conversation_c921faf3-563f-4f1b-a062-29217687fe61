package com.aig.aigone.controller;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.entity.EducationBrochureInfo;
import com.aig.aigone.images.EducationBrochurePdf;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/api/digitalhub")
@Slf4j
public class DigitalhubController {

	private final EducationBrochurePdf educationBrochurePdf;

	@Autowired
	public DigitalhubController(EducationBrochurePdf educationBrochurePdf) {
		this.educationBrochurePdf = educationBrochurePdf;
	}

	@GetMapping("/education-brochures")
	public ResponseEntity<Map<String, byte[]>> listEducationBrochures() {
		try {
			Map<String, byte[]> brochures = educationBrochurePdf.getAllBrochuresAsByteArray();
			return ResponseEntity.ok(brochures);
		} catch (Exception e) {
			log.error("Error while fetching brochures", e);
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Collections.emptyMap()); // or return a
																											// message
		}
	}

	@GetMapping("/education-brochures/list")
	public ResponseEntity<List<EducationBrochureInfo>> listBrochures() {
		try {
			List<EducationBrochureInfo> brochures = educationBrochurePdf.listAvailableBrochures();
			return ResponseEntity.ok(brochures);
		} catch (Exception e) {
			log.error("Error while listing brochures", e);
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Collections.emptyList());
		}
	}

	@GetMapping("/education-brochure/{fileName}")
	public ResponseEntity<byte[]> getBrochure(@PathVariable String fileName) {
		try {
			byte[] brochureBytes = educationBrochurePdf.getBrochure(fileName);

			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_PDF);
			headers.setContentDisposition(ContentDisposition.inline().filename(fileName).build());
			headers.setContentLength(brochureBytes.length);

			return ResponseEntity.ok().headers(headers).body(brochureBytes);
		} catch (Exception e) {
			log.error("Error while fetching brochure: {}", fileName, e);
			return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
		}
	}

}
