package com.aig.aigone.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.core.io.Resource;

@ConfigurationProperties(prefix = "aig.firebase.config")
public class FirebaseProperties {
    private Resource serviceAccount;
    

    /**
     * @return the serviceAccount
     */
    public Resource getServiceAccount() {
        return serviceAccount;
    }

    /**
     * @param serviceAccount the serviceAccount to set
     */
    public void setServiceAccount(Resource serviceAccount) {
        this.serviceAccount = serviceAccount;
    }

}