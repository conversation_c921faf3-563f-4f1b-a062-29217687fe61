package com.aig.aigone.config;

import org.springframework.boot.web.servlet.FilterRegistrationBean;

import com.aig.aigone.security.LogFilter;

//@Configuration
public class FilterConfig {

//    @Bean
    public FilterRegistrationBean<LogFilter> logFilter() {
        FilterRegistrationBean<LogFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new LogFilter());
        registrationBean.addUrlPatterns("/*"); // Apply filter to all URLs
        return registrationBean;
    }
}
