package com.aig.aigone.bloodbank.service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import org.springframework.http.ResponseEntity;

import com.aig.aigone.bloodbank.model.dto.DonorBasicInfoDTO;
import com.aig.aigone.bloodbank.model.dto.DonorHistoryRequest;
import com.aig.aigone.bloodbank.model.dto.WhatsAppSendRequest;


import reactor.core.publisher.Mono;

public interface DonorService {

    ResponseEntity<Map<String, Object>> registerDonorWithResponse(DonorBasicInfoDTO dto);

    List<DonorBasicInfoDTO> getAllDonors();

    DonorBasicInfoDTO getDonorByEmployeeId(String employeeId);

    boolean updateLastDonationDate(Long donorId, String donationDate, String coMorbities, String medications);
    
    ResponseEntity<Map<String, String>> updateLastDonationDateWithResponse(Long donorId, LocalDate donationDate,
    		
        String coMorbities, String medications);
    Mono<ResponseEntity<String>> sendWhatsAppToDonors(WhatsAppSendRequest request);
    String deleteDonorByEmployeeId(String employeeId);
}
