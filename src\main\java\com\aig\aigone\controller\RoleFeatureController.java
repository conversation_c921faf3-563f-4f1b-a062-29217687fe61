package com.aig.aigone.controller;

import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.model.dto.FeatureDTO;
import com.aig.aigone.model.dto.RoleDto;
import com.aig.aigone.model.dto.RoleFeatureRequest;
import com.aig.aigone.service.RoleFeatureService;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/api/role-features")
@RequiredArgsConstructor
public class RoleFeatureController {
    private final RoleFeatureService roleFeatureService;

    @PutMapping("/assign")
    public ResponseEntity<Void> assignFeaturesToRole(@Valid @RequestBody RoleFeatureRequest request) {
        roleFeatureService.assignFeaturesToRole(request);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/remove")
    public ResponseEntity<Void> removeFeaturesFromRole(@Valid @RequestBody RoleFeatureRequest request) {
        roleFeatureService.removeFeaturesFromRole(request);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/by-role/{roleId}")
    public ResponseEntity<List<FeatureDTO>> getFeaturesByRole(@PathVariable Long roleId) {
        return ResponseEntity.ok(roleFeatureService.getFeaturesByRole(roleId));
    }

    @GetMapping("/by-feature/{featureId}")
    public ResponseEntity<List<RoleDto>> getRolesByFeature(@PathVariable Long featureId) {
        return ResponseEntity.ok(roleFeatureService.getRolesByFeature(featureId));
    }
}