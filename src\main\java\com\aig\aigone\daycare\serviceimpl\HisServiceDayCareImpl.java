package com.aig.aigone.daycare.serviceimpl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.aig.aigone.daycare.dto.PatientDTO;
import com.aig.aigone.daycare.his.model.BedType;
import com.aig.aigone.daycare.his.model.Company;
import com.aig.aigone.daycare.his.model.InPatient;
import com.aig.aigone.daycare.his.model.Patient;
import com.aig.aigone.daycare.his.model.Procedure;
import com.aig.aigone.daycare.his.model.Station;
import com.aig.aigone.daycare.his.repository.BedTypeRepository;
import com.aig.aigone.daycare.his.repository.CompanyRepository;
import com.aig.aigone.daycare.his.repository.HISRepository;
import com.aig.aigone.daycare.his.repository.InPatientRepository;
import com.aig.aigone.daycare.his.repository.ProcedureHisRepository;
import com.aig.aigone.daycare.his.repository.StationRepository;
import com.aig.aigone.daycare.service.HisServiceDaycare;

@Component
public class HisServiceDayCareImpl implements HisServiceDaycare {
	private static final Logger logger = LoggerFactory.getLogger(HisServiceDayCareImpl.class);

	@Autowired
	HISRepository hisRepo;

	@Autowired
	InPatientRepository inPatientRepo;

	@Autowired
	StationRepository stationRepo;

	@Autowired
	BedTypeRepository bedtypeRepo;

	@Autowired
	ProcedureHisRepository procedureRepo;

	@Autowired
	CompanyRepository companyRepo;

	@Override
	public List<Company> getCompany() {
		logger.info("Fetching all companies");
		return companyRepo.findAll();
	}

	public List<Procedure> getProcedure() {
		logger.info("Fetching all procedures");
		return procedureRepo.findAll();
	}

	@Override
	public List<Station> getStation() {
		logger.info("Fetching all stations");
		return stationRepo.findAll();
	}

	@Override
	public List<BedType> getBedtype() {
		logger.info("Fetching all bet type");
		return bedtypeRepo.findAll();
	}

	@Override
	public PatientDTO getPatientByUHID(String aicode, Integer regNo) {
		logger.info("Fetching patient by UHID: aicode={}, regNo={}", aicode, regNo);

		Patient patient = hisRepo.getPatientByUHID(aicode, regNo);
		InPatient inPatient = inPatientRepo.findPatientByIaCodeAndRegistrationNo(aicode, regNo);
		PatientDTO patientDto = new PatientDTO();

		patientDto.setPatientID(patient.getIaCode() + "." + patient.getRegistrationNo());
		//Salutation Mr. or Mrs. or Master added 
		patientDto.setPatientName(patient.getTitle()+" "+( patient.getFirstName() + patient.getMiddleName() + patient.getLastName()).toUpperCase());
		patientDto.setPhoneNo(patient.getPhoneNo());
		patientDto.setGender(patient.getGender() == 1 ? "Male" : "Female");
		patientDto.setAge(patient.getAge());

		if (inPatient != null) {
			logger.info("Inpatient details found for UHID: aicode={}, regNo={}", aicode, regNo);
			patientDto.setInpatient(true);
			patientDto.setPrimary_consultantId(inPatient.getPrimaryDoctor().getId());
//			patientDto.setPrimary_cosultantName(inPatient.getPrimaryDoctor().getName());
			//Salutation Dr. added to consultant name 
			patientDto.setPrimary_cosultantName("Dr. " + inPatient.getPrimaryDoctor().getName().toUpperCase());
			patientDto.setRef_consultantId(inPatient.getReferDoctor().getId());
			//Salutation Dr. added to consultant name 
			patientDto.setRef_cosultantName("Dr. "+inPatient.getReferDoctor().getName().toUpperCase());
			patientDto.setPaymentType(inPatient.getPaymentType());
			patientDto.setInsuranceName(inPatient.getInsuranceCompanyName());
		}

		return patientDto;
	}

}
