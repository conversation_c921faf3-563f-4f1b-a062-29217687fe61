package com.aig.aigone.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.boot.configurationprocessor.json.JSONObject;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;

import com.aig.aigone.common.AigOneUtil;
import com.aig.aigone.common.DateUtil;
import com.aig.aigone.common.HisApiUtil;
import com.aig.aigone.common.JsonUtilService;
import com.aig.aigone.crm.model.dto.PatientOpBillsListRequestDto;
import com.aig.aigone.daycare.his.repository.HISRepository;
import com.aig.aigone.exception.AigOneException;
import com.aig.aigone.images.AnemiaImageStorageService;
import com.aig.aigone.labreport.model.entity.LabReportTestEntity;
import com.aig.aigone.labreport.repository.LabReportTestRepository;
import com.aig.aigone.mapper.AigOneMapper;
import com.aig.aigone.model.dto.AppointmentRequestDto;
import com.aig.aigone.model.dto.CTFieldDto;
import com.aig.aigone.model.dto.CTSubmitRequestDto;
import com.aig.aigone.model.dto.ChecklistTemplateDetailsDto;
import com.aig.aigone.model.dto.ChecklistTemplatesDto;
import com.aig.aigone.model.dto.EmployeeRosterDto;
import com.aig.aigone.model.dto.LookupOptionsDto;
import com.aig.aigone.model.dto.METPatientLogDto;
import com.aig.aigone.model.dto.METPatientRequestDto;
import com.aig.aigone.model.dto.OpConsultationDto;
import com.aig.aigone.model.dto.PatientAmnesiaDetailsDTO;
import com.aig.aigone.model.dto.PatientAttendantDto;
import com.aig.aigone.model.dto.PatientRequestAppointmentDto;
import com.aig.aigone.model.dto.PatientRequestAppointmentLogDto;
import com.aig.aigone.model.dto.PatientTestDTO;
import com.aig.aigone.model.dto.PatientVisitorPassDto;
import com.aig.aigone.model.dto.TimeSlot;
import com.aig.aigone.model.dto.UserDto;
import com.aig.aigone.model.dto.UserSessionVO;
import com.aig.aigone.model.dto.VisitorPassBedsConfigDto;
import com.aig.aigone.model.dto.VisitorPassConfigDto;
import com.aig.aigone.model.dto.VisitorPassFloorConfigDto;
import com.aig.aigone.model.dto.emr.METPatientDto;
import com.aig.aigone.model.dto.his.AppointmentRequestResponseDto;
import com.aig.aigone.model.dto.his.AppointmentsDetailsDto;
import com.aig.aigone.model.dto.his.AppointmentsRequestDto;
import com.aig.aigone.model.dto.his.AppointmentsSummaryDto;
import com.aig.aigone.model.dto.his.DeleteAppointmentRequestDto;
import com.aig.aigone.model.dto.his.DoctorAppointmentDetailsDaywiseRequestDto;
import com.aig.aigone.model.dto.his.DoctorAppointmentDetailsDaywiseResponseDto;
import com.aig.aigone.model.dto.his.DoctorAvailableSlotsDto;
import com.aig.aigone.model.dto.his.DoctorScheduleRequestDto;
import com.aig.aigone.model.dto.his.EmrUploadRequest;
import com.aig.aigone.model.dto.his.HisLoginRequestDto;
import com.aig.aigone.model.dto.his.HisLoginResponseDto;
import com.aig.aigone.model.dto.his.HisResponseDto;
import com.aig.aigone.model.dto.his.HisResponseListDto;
import com.aig.aigone.model.dto.his.LocationDto;
import com.aig.aigone.model.dto.his.OrderDetailsQueryResults;
import com.aig.aigone.model.dto.his.OrderDto;
import com.aig.aigone.model.dto.his.OrderItemDto;
import com.aig.aigone.model.dto.his.OrderResponseDto;
import com.aig.aigone.model.dto.his.PateintAppointmentsDto;
import com.aig.aigone.model.dto.his.PateintPreviousAppointmentsDto;
import com.aig.aigone.model.dto.his.PatientAbnoramlReportsResponseDto;
import com.aig.aigone.model.dto.his.PatientDetailsDto;
import com.aig.aigone.model.dto.his.PatientDocumentsDto;
import com.aig.aigone.model.dto.his.PatientFlaggedDetailsDto;
import com.aig.aigone.model.dto.his.PatientIPDetailsDto;
import com.aig.aigone.model.dto.his.PatientOPDetailsDto;
import com.aig.aigone.model.dto.his.PatientPlannedDetailsDto;
import com.aig.aigone.model.dto.his.PatientRegistrationRequestDto;
import com.aig.aigone.model.dto.his.PatientReportAbnormalDto;
import com.aig.aigone.model.dto.his.PatientReportDetailsDto;
import com.aig.aigone.model.dto.his.PatientReportSummaryDto;
import com.aig.aigone.model.dto.his.PatientReportsRequestDto;
import com.aig.aigone.model.dto.his.PatientReportsResponseDto;
import com.aig.aigone.model.dto.his.PaymentInfoDto;
import com.aig.aigone.model.dto.his.ProcedureGuideDto;
import com.aig.aigone.model.dto.his.ProcedureInfoGuideDto;
import com.aig.aigone.model.dto.his.ReportStatusDto;
import com.aig.aigone.model.dto.his.SavePatientAppointmentRequestDto;
import com.aig.aigone.model.dto.his.ServiceDto;
import com.aig.aigone.model.dto.his.UploadRequest;
import com.aig.aigone.model.dto.his.ViewReportHisResponseDto;
import com.aig.aigone.model.dto.his.ViewReportRequestDto;
import com.aig.aigone.model.dto.his.ViewReportResponseDto;
import com.aig.aigone.model.dto.his.WellnessPackageDto;
import com.aig.aigone.model.dto.hismaster.DrugDiagnosticApiResponseDto;
import com.aig.aigone.model.dto.hismaster.DrugDiagnosticOrdersPrism;
import com.aig.aigone.model.dto.hismaster.OpConsultNoteReqToEmrHis;
import com.aig.aigone.model.dto.hismaster.OpConsultNoteToEmrDto;
import com.aig.aigone.model.dto.hismaster.OpConsultNoteToEmrResponseDto;
import com.aig.aigone.model.dto.hismaster.OpConsultNoteToHisDto;
import com.aig.aigone.model.dto.hismaster.OpConsultResToPrismFromHisEmrDto;
import com.aig.aigone.model.dto.mdt.MdtDto;
import com.aig.aigone.model.entity.aigone.AigAssistantEntity;
import com.aig.aigone.model.entity.aigone.AigAssistantTypeEnum;
import com.aig.aigone.model.entity.aigone.CTFieldOptionTypeEnum;
import com.aig.aigone.model.entity.aigone.ChecklistFieldEntity;
import com.aig.aigone.model.entity.aigone.ChecklistFieldOptionsEntity;
import com.aig.aigone.model.entity.aigone.ChecklistSubmittedEntity;
import com.aig.aigone.model.entity.aigone.ChecklistSubmittedFieldOptionValuesEntity;
import com.aig.aigone.model.entity.aigone.ChecklistSubmittedStatusEnum;
import com.aig.aigone.model.entity.aigone.ChecklistTemplateEntity;
import com.aig.aigone.model.entity.aigone.DoctorFavouritePatientsEntity;
import com.aig.aigone.model.entity.aigone.EmployeeRosterDepartmentEnum;
import com.aig.aigone.model.entity.aigone.EmployeeRosterEntity;
import com.aig.aigone.model.entity.aigone.FaqEntity;
import com.aig.aigone.model.entity.aigone.FaqTypeEnum;
import com.aig.aigone.model.entity.aigone.LookupOptionsMasterEntity;
import com.aig.aigone.model.entity.aigone.LookupValuesMasterEntity;
import com.aig.aigone.model.entity.aigone.MDTEntity;
import com.aig.aigone.model.entity.aigone.METPatientACKStatusEnum;
import com.aig.aigone.model.entity.aigone.METPatientEntity;
import com.aig.aigone.model.entity.aigone.METPatientLogEntity;
import com.aig.aigone.model.entity.aigone.OpConsultationEntity;
import com.aig.aigone.model.entity.aigone.PatientAmnesiaDetails;
import com.aig.aigone.model.entity.aigone.PatientAttendantEntity;
import com.aig.aigone.model.entity.aigone.PatientEntity;
import com.aig.aigone.model.entity.aigone.PatientRequestAppointmentEntity;
import com.aig.aigone.model.entity.aigone.PatientRequestAppointmentLogEntity;
import com.aig.aigone.model.entity.aigone.PatientRequestAppointmentStatusEnum;
import com.aig.aigone.model.entity.aigone.PatientVisitorPassEntity;
import com.aig.aigone.model.entity.aigone.ProcedureEntity;
import com.aig.aigone.model.entity.aigone.ProcedureInfoGuideEntity;
import com.aig.aigone.model.entity.aigone.RequestedFromEnum;
import com.aig.aigone.model.entity.aigone.RoleEntity;
import com.aig.aigone.model.entity.aigone.StatusEnum;
import com.aig.aigone.model.entity.aigone.SystemSettingsEntity;
import com.aig.aigone.model.entity.aigone.SystemSettingsKey;
import com.aig.aigone.model.entity.aigone.TicketQuesEnum;
import com.aig.aigone.model.entity.aigone.TicketQuestionsEntity;
import com.aig.aigone.model.entity.aigone.UserAttendanceEntity;
import com.aig.aigone.model.entity.aigone.UserEntity;
import com.aig.aigone.prism.repositories.DoctorDelegateRepository;
import com.aig.aigone.prism.repositories.PrismTeamsRepository;
import com.aig.aigone.prism.services.PrismTeamsService;
import com.aig.aigone.repository.aigone.AigAssistantRepository;
import com.aig.aigone.repository.aigone.CTSubmittedFieldsRepository;
import com.aig.aigone.repository.aigone.ChecklistFieldRepository;
import com.aig.aigone.repository.aigone.ChecklistSubmittedRepository;
import com.aig.aigone.repository.aigone.ChecklistTemplateRepository;
import com.aig.aigone.repository.aigone.EmployeeRosterRepository;
import com.aig.aigone.repository.aigone.FaqRepository;
import com.aig.aigone.repository.aigone.FavouritePatientsRepository;
import com.aig.aigone.repository.aigone.LookupOptionsMasterRepository;
import com.aig.aigone.repository.aigone.LookupValuesMasterRepository;
import com.aig.aigone.repository.aigone.METPatientLogRepository;
import com.aig.aigone.repository.aigone.METPatientRepository;
import com.aig.aigone.repository.aigone.MdtRepository;
import com.aig.aigone.repository.aigone.OpConsultationRepository;
import com.aig.aigone.repository.aigone.PatientAmnesiaDetailsRepository;
import com.aig.aigone.repository.aigone.PatientAttendantRepository;
import com.aig.aigone.repository.aigone.PatientRepository;
import com.aig.aigone.repository.aigone.PatientRequestAppointmentRepository;
import com.aig.aigone.repository.aigone.PatientVisitorPassRepository;
import com.aig.aigone.repository.aigone.PayableServicesRepository;
import com.aig.aigone.repository.aigone.ProcedureInfoGuideRepository;
import com.aig.aigone.repository.aigone.ProcedureRepository;
import com.aig.aigone.repository.aigone.SystemSettingsRepository;
import com.aig.aigone.repository.aigone.TicketQuestionsRepository;
import com.aig.aigone.repository.aigone.UserAttendanceRepository;
import com.aig.aigone.repository.aigone.UserRepository;
import com.aig.aigone.repository.emr.EmrDbRepository;
import com.aig.aigone.repository.his.HisDbRepository;
import com.aig.aigone.roster.service.RosterService;
import com.aig.aigone.security.SecurityUtil;
import com.aig.aigone.service.EmrService;
import com.aig.aigone.service.FileUploadService;
import com.aig.aigone.service.HisService;
import com.aig.aigone.service.SmsService;
import com.aig.aigone.service.SystemSettingService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Slf4j
@Service
@Scope(proxyMode = ScopedProxyMode.TARGET_CLASS)
public class HisServiceImpl implements HisService {
	
	
	@Autowired
	private PrismTeamsService prismTeamsService;
	
	@Autowired
	private EmrDbRepository emrDbRepo;

	@Autowired
	private UserRepository userRepo;

	@Autowired
	private PatientRepository patientRepo;

	@Autowired
	private AigOneMapper aigOneMapper;

	@Autowired
	private WebClient hisClient;

	@Autowired
	private HisDbRepository hsDbRepo;

	@Autowired
	private ProcedureRepository procedureInfoRepo;

	@Autowired
	private UserAttendanceRepository userAttendanceRepo;

	@Autowired
	private ProcedureInfoGuideRepository procedureInfoGuideRepo;

	@Autowired
	private FaqRepository faqRepo;

	@Autowired
	private HisService self;

	@Autowired
	private TicketQuestionsRepository TicketQuesRepo;

	@Autowired
	private EmployeeRosterRepository EmployeeRosterRepo;

	@Autowired
	private PayableServicesRepository payableServiceRepo;

	@Autowired
	private AigAssistantRepository aigAssistantRepo;
	
	@Autowired
	private HISRepository hisRepository;

	@Value("${aig.his.password}")
	private String password;

	@Value("${aig.his.userName}")
	private String userName;

	@Autowired
	private JsonUtilService jsoService;

	@Autowired
	private ChecklistTemplateRepository ctRepo;

	@Autowired
	private ChecklistFieldRepository ctFieldRepo;

	@Autowired
	private ChecklistSubmittedRepository ctSubmittedRepo;

	@Autowired
	private LookupOptionsMasterRepository lookupOptionsRepo;

	@Autowired
	private LookupValuesMasterRepository lookupValuesRepo;

	@Autowired
	private WebClient risClient;

	@Autowired
	private WebClient emrClient;

	@Value("${aig.his.documentsbaseurl}")
	private String hisDocumentsBaseUrl;

	@Autowired
	private SystemSettingsRepository systemRepo;

	@Autowired
	private CTSubmittedFieldsRepository ctSubmittedFieldsRepo;

	@Autowired
	private FileUploadService fileUploadService;

	@Autowired
	private PatientAttendantRepository patientAttendantRepo;

	@Autowired
	private SmsService smsService;

	@Autowired
	private PatientRequestAppointmentRepository patientReqAppoRepo;

	@Autowired
	private METPatientRepository metPatientRepo;

	@Autowired
	private EmrService emrService;

	@Autowired
	private METPatientLogRepository metPatientLogRepo;

	@Autowired
	private PatientVisitorPassRepository vistorPassRepo;

	@Autowired
	private OpConsultationRepository opConsultationRepo;

	@Autowired
	private SystemSettingService systemSettingsService;

	@Autowired
	private FavouritePatientsRepository favouritePatientsRepo;

	@Autowired
	private MdtRepository mdtRepo;

	@Autowired
	private RosterService rosterService;
	
	 @Autowired
	 private PatientAmnesiaDetailsRepository patientDetailsRepository;
	 
	 @Autowired
	 private LabReportTestRepository labReportTestRepo;
	 
	@Autowired
	AnemiaImageStorageService anemiaImageStorageService;	
	
	@Autowired
	PrismTeamsRepository prismTeamsRepository;

	@Autowired
	private DoctorDelegateRepository delegateAccessRepo;
	
	
//	
//	@Autowired
//	private EmrServiceImpl emrServiceImpl;
	@Autowired
	private SystemSettingService settingService;

	@Override
	public UserDto fetchUserByPhoneNo(long phoneNo) {
		UserEntity user = userRepo.findByPhoneNoAndActiveTrue(phoneNo)
				.orElseThrow(() -> new EntityNotFoundException("User Not Found with Phone Number: " + phoneNo));
		Set<RoleEntity> roleEntities = user.getRoles();
		List<Map<String,String>> icareTabsList= new ArrayList<Map<String,String>>();
		Map<String,String> icareTabsMap=new HashMap<String, String>();
		UserDto dto = aigOneMapper.toUserDto(user);
		List<String> iCareTabs = user.getICareTabs();
		if (iCareTabs != null) {
			for(String list : iCareTabs) {
				icareTabsMap.put(list, list);
			}
			icareTabsList.add(icareTabsMap);
			dto.setICareTabs(icareTabsList);
		} else {
			dto.setICareTabs(new ArrayList<>());
		}
		dto.setRosterTabs(Arrays.asList("MEDICAL GASTROENTEROLOGY"));
		 String showRoster=settingService.getSettingValue(SystemSettingsKey.SHOW_ROSTER.getName());
       boolean showRosterr = Boolean.parseBoolean(showRoster);	
		dto.setShowRoster(showRosterr);
		List<String> roles = new ArrayList<>();
		if (!CollectionUtils.isEmpty(roleEntities)) {
			roles.addAll(roleEntities.stream().map(RoleEntity::getCode).toList());
			dto.setRoleNames(roles);
		}
		dto.setRosterExists(rosterService.verifyEmployeeRosterCreation(user.getId(), DateUtil.getTodayDate()));
		;
		return dto;
	}

	@Override
	public UserDto fetchUserDetailsByEmployeeId(String employeeId) {
		UserEntity user = userRepo.findByEmployeeIdAndActive(employeeId, true)
				.orElseThrow(() -> new EntityNotFoundException("User Not Found with Employee Id: " + employeeId));
		Set<RoleEntity> roleEntities = user.getRoles();
		UserDto dto = aigOneMapper.toUserDto(user);
		List<String> roles = new ArrayList<>();
		if (!CollectionUtils.isEmpty(roleEntities)) {
			roles.addAll(roleEntities.stream().map(RoleEntity::getCode).toList());
			dto.setRoleNames(roles);
		}
		if(user.getDbId()!=null) {
			dto.setMappedDoctors(hsDbRepo.fetchMappedDcotorsByAssistantId(user.getDbId()));
		}
		return dto;
	}
	
//	@Override
//	public List<UserDto> fetchMappedDoctorsByEmployeeId(String employeeId) {
//		UserEntity user = userRepo.findByEmployeeIdAndActiveTrue(employeeId).orElseThrow(
//				() -> new EntityNotFoundException("Employee Not Found with given employee id: " + employeeId));
//		return hsDbRepo.fetchMappedDcotorsByAssistantId(user.getDbId());
//	}
	
	
	@Override
	public List<UserDto> fetchMappedDoctorsByEmployeeId(String employeeId) {
	    UserEntity user = userRepo.findByEmployeeIdAndActiveTrue(employeeId)
	            .orElseThrow(() -> new EntityNotFoundException("Employee Not Found with given employee id: " + employeeId));

	    List<UserDto> mappedDoctors = new ArrayList<>();

	    // Always add current user
	    UserDto userDto = new UserDto();
	    userDto.setDbId(user.getDbId());
	    userDto.setEmployeeId(user.getEmployeeId());
	    userDto.setName(user.getName());
	    userDto.setConsultant(user.isConsultant());
	    userDto.setTitle(user.getTitle());
	    userDto.setGender(user.getGender());
	    userDto.setPhoneNo(user.getPhoneNo() != null ? user.getPhoneNo().toString() : null);
	    userDto.setDob(user.getDob());
	    userDto.setDesignation(user.getDesignation());
	    userDto.setDepartment(user.getDepartment());
	    userDto.setActive(user.isActive());
	    userDto.setPayGroupName(user.getPayGroupName());
	    userDto.setCompanyName(user.getCompanyName());
	    userDto.setConfirmationPeriod(user.getConfirmationPeriod());
	    userDto.setAssociateGrade(user.getAssociateGrade());
	    userDto.setPerMailId(user.getPerMailId());
	    userDto.setOffMailId(user.getOffMailId());
	    userDto.setNpDays(user.getNpDays());
	    userDto.setDivision(user.getDivision());
	    userDto.setManagerId(user.getManagerId());
	    userDto.setManagerName(user.getManagerName());
	    userDto.setBloodGroup(user.getBloodGroup());
	    userDto.setHosLocation(user.getHosLocation());

	    // Only fetch mapped doctors if DB ID is present
	    if (user.getDbId() != null) {
	        List<UserDto> fetchedDoctors = hsDbRepo.fetchMappedDcotorsByAssistantId(user.getDbId());
	        if (fetchedDoctors != null) {
	            mappedDoctors.addAll(fetchedDoctors);
	        }
	    }

	    mappedDoctors.add(userDto); // Add current user at the end
	    return mappedDoctors;
	}





	private List<UserDto> prepareUsersData(int length) {
		return IntStream.range(0, length).mapToObj(i -> prepareTestUser(i)).toList();
	}

	UserDto prepareTestUser(int i) {
		UserDto dto = new UserDto();
		// dto.setActive(true);
		dto.setEmployeeId("AIG000" + i);
		// dto.setFirstName("AIG"+i);
		// dto.setLastName("Hyderabad"+i);
		return dto;
	}
	// AppointmentsSummaryDto prepareTestAppointmentSummary(int i){
	// AppointmentsSummaryDto dto = new AppointmentsSummaryDto();
	// dto.setAppontmentType(i==1?"OP": (i==2?"IP":"OT"));
	// dto.setBilledCount(0);
	// dto.setCount(i);
	// return dto;
	// }

	AppointmentsDetailsDto prepareTestAppointmentsDetailsDto(int i) {
		AppointmentsDetailsDto dto = new AppointmentsDetailsDto();
		dto.setStartTime(
				DateUtil.convertStringToDate(i == 1 ? "09:00" : (i == 2 ? "12:00" : "16:00"), DateUtil.FORMAT_HH_MM));
		dto.setEndTime(
				DateUtil.convertStringToDate(i == 1 ? "11:00" : (i == 2 ? "15:00" : "19:00"), DateUtil.FORMAT_HH_MM));
		dto.setScheduleType(i == 1 ? "OP" : (i == 2 ? "IP" : "OT"));
		return dto;
	}

	@Override
	public List<AppointmentsSummaryDto> fetchAppointmentsSummary(String doctorId, Date date) {
		// UserEntity user = userRepo.findByEmployeeId(doctorId).orElseThrow(
		// () -> new EntityNotFoundException("Employee Not Found with given employee id:
		// " + doctorId));

		return List.of(
				new AppointmentsSummaryDto(AigOneUtil.IP, 0, hsDbRepo.fetchIPCountByDoctorId(Long.parseLong(doctorId))),
				new AppointmentsSummaryDto(AigOneUtil.OP, 0,
						hsDbRepo.fetchOpCountByDoctorIdAndDate(Long.parseLong(doctorId), date)));

		// AppointmentsSummaryDto ipSummary = new AppointmentsSummaryDto();
		// ipSummary.setAppontmentType(AigOneUtil.IP);
		// ipSummary.setCount(hsDbRepo.fetchIPCountByDoctorId(user.getDbId()));
		//
		// int fetchIPCountByDoctorId = hsDbRepo.fetchIPCountByDoctorId(user.getDbId());
		//
		//
		// return IntStream.range(0,
		// 3).mapToObj(i->prepareTestAppointmentSummary(i)).toList();
	}

	@Override
	public List<AppointmentsDetailsDto> fetchAppointmentDetails(String doctorId, Date date) {
		return IntStream.range(0, 3).mapToObj(i -> prepareTestAppointmentsDetailsDto(i)).toList();
	}

	@Override
	public List<AppointmentsDetailsDto> updateAppointmentDetails(AppointmentsRequestDto appointmentsRequest) {
		return null;
	}

	@Override
	public List<String> fetchAllIdCards() {
		List<String> idCards = hsDbRepo.fetchIdCards();
		if (CollectionUtils.isEmpty(idCards)) {
			throw new AigOneException("Id Card Types Master not found");
		}
		return idCards;
	}

	@Override
	public List<String> fetchGenderMaster() {
		List<String> genderMaster = hsDbRepo.fetchGenderMaster();
		if (CollectionUtils.isEmpty(genderMaster)) {
			throw new AigOneException("Gender Master not found");
		}
		return genderMaster;
	}

	@Override
	public List<LocationDto> fetchLocationByPincode(String pincode) {
		List<LocationDto> locations = hsDbRepo.locationByPinCode(pincode);
		if (CollectionUtils.isEmpty(locations)) {
			throw new AigOneException("No Locations found for given pincode: " + pincode);
		}
		return locations;
	}

	@Override
	@Cacheable(value = AigOneUtil.CACHE_HIS, key = "T(com.aig.aigone.common.AigOneUtil).CACHE_KEY_HIS_LOGIN")
	public HisLoginResponseDto login() {
		log.info("HIs Login called");
		HisLoginRequestDto loginRequestDto = new HisLoginRequestDto();
		loginRequestDto.setUserName(userName);
		loginRequestDto.setPassword(password);

		HisLoginResponseDto response = hisClient.post().uri(HisApiUtil.LOGIN).contentType(MediaType.APPLICATION_JSON)
				.body(BodyInserters.fromValue(loginRequestDto)).retrieve().bodyToMono(HisLoginResponseDto.class)
				.block();
		return response;
	}

	public OpConsultResToPrismFromHisEmrDto opConsultResToPrismFromHisEmr(
			OpConsultNoteReqToEmrHis opConsultNoteReqToEmrHis) {

		HisLoginResponseDto login = login();

		OpConsultNoteToEmrDto opConsultNoteToEmrDto = new OpConsultNoteToEmrDto();
		OpConsultNoteToHisDto opConsultNoteToHisDto = new OpConsultNoteToHisDto();

		opConsultNoteToEmrDto.setPatientId(opConsultNoteReqToEmrHis.getPatientId());

		Integer randomNum = (int) (Math.random() * 10);
		long currentTime = System.currentTimeMillis();

		opConsultNoteToEmrDto.setDocumentId("emr" + randomNum + currentTime);
		opConsultNoteToEmrDto.setDocumentName("OP_Consult_Note.pdf");
		opConsultNoteToEmrDto.setDocumentType("Lab Report");
		opConsultNoteToEmrDto.setDocumentSource("PATIENT");
		opConsultNoteToEmrDto.setDocumentBase64(opConsultNoteReqToEmrHis.getDocumentBase64());
		opConsultNoteToEmrDto.setDocumentContentType("pdf");
		opConsultNoteToEmrDto.setPractAccessYn("Y");
		opConsultNoteToEmrDto.setAddedBy(opConsultNoteReqToEmrHis.getAddedBy());
		opConsultNoteToEmrDto.setAddedDateTime(opConsultNoteReqToEmrHis.getAddedDateTime());
		opConsultNoteToEmrDto.setStatus("E");
		opConsultNoteToEmrDto.setDocumentFormat("PDF");

		

		//// for his//////
		opConsultNoteToHisDto.setPatientID(opConsultNoteReqToEmrHis.getPatientId());
		opConsultNoteToHisDto.setHospCode("1100");
		opConsultNoteToHisDto.setDocumentCategory("OP Prescription");
		opConsultNoteToHisDto.setDocumentName("OP_Consult_Note");
		opConsultNoteToHisDto.setFileNamewithExtn("OP_Consult_Note.pdf");
		opConsultNoteToHisDto.setFileinBase64(opConsultNoteReqToEmrHis.getDocumentBase64());
		opConsultNoteToHisDto.setSavedBy(opConsultNoteReqToEmrHis.getAddedBy());
		opConsultNoteToHisDto.setDocumentSaveDatetime(opConsultNoteReqToEmrHis.getAddedDateTime());

		OpConsultNoteToEmrResponseDto opConsultNoteToEmrResponseDto = postOpConsultNoteToEmrFromPrismImpl(
				opConsultNoteToEmrDto, login.getAccess_token());

		DrugDiagnosticApiResponseDto drugDiagnosticApiResponseDto = postOpConsultNotetoHisFromPrismImpl(
				opConsultNoteToHisDto, login.getAccess_token());

		OpConsultResToPrismFromHisEmrDto opConsultResToPrismFromHisEmrDto = new OpConsultResToPrismFromHisEmrDto();

		opConsultResToPrismFromHisEmrDto.setIsUploadedHis(drugDiagnosticApiResponseDto.getStatus());
		opConsultResToPrismFromHisEmrDto.setIsUploadedToEmr(opConsultNoteToEmrResponseDto.getStatus());

		return opConsultResToPrismFromHisEmrDto;

	}

	// for emr ///////

	public OpConsultNoteToEmrResponseDto postOpConsultNoteToEmrFromPrismImpl(
			OpConsultNoteToEmrDto opConsultNoteToEmrDto, String token) {
		CompletableFuture<OpConsultNoteToEmrResponseDto> future = CompletableFuture.supplyAsync(() -> {
			// Blocking login method
			SystemSettingsEntity emrAPiKey = systemRepo.findByKey("EMR_API_KEY");

			if (token != null) {
				try {
					// Make the HTTP POST request to the EMR API with hpApp-Token header
					String response = emrClient.post().uri(HisApiUtil.POST_OP_CONSULT_NOTE_EMR_FROM_PRISM)
							.contentType(MediaType.APPLICATION_JSON).header("hpApp-Token", emrAPiKey.getValue()) // Updated
																													// header
							.body(BodyInserters.fromValue(opConsultNoteToEmrDto)).retrieve().bodyToMono(String.class)
							.block();

					// Parse the response JSON into OpConsultNoteToEmrResponseDto
					ObjectMapper objectMapper = new ObjectMapper();
					OpConsultNoteToEmrResponseDto opConsultNoteToEmrResponseDto = objectMapper.readValue(response,
							OpConsultNoteToEmrResponseDto.class);

					log.info("Received response: {}", opConsultNoteToEmrResponseDto);
					return opConsultNoteToEmrResponseDto;

				} catch (JsonProcessingException e) {
					log.error("Failed to parse API response JSON: ", e);
					return new OpConsultNoteToEmrResponseDto("ERROR", "Failed to parse API response JSON",
							e.getMessage());
				} catch (Exception e) {
					log.error("An error occurred while posting OP consult note to EMR: *****", e.getMessage());
					return new OpConsultNoteToEmrResponseDto("ERROR", "An error occurred during the POST request",
							e.getMessage());
				}
			} else {
				log.error("Failed to login or retrieve access token. Cannot proceed with posting orders.");
				return new OpConsultNoteToEmrResponseDto("ERROR", "Failed to login or retrieve access token", null);
			}
		});

		try {
			return future.get(); // Blocking to retrieve the CompletableFuture result
		} catch (Exception e) {
			log.error("Error while waiting for OP consult note CompletableFuture:****** ", e.getMessage());
			return new OpConsultNoteToEmrResponseDto("ERROR", "Error while waiting for response", e.getMessage());
		}
	}

	// for his //////////////////////

	public DrugDiagnosticApiResponseDto postOpConsultNotetoHisFromPrismImpl(OpConsultNoteToHisDto opConsultNoteToHisDto,
			String token) {
		CompletableFuture<DrugDiagnosticApiResponseDto> future = CompletableFuture.supplyAsync(() -> {

			if (token != null) {
				try {
					// Make the HTTP POST request to the HIS API
					String response = hisClient.post().uri(HisApiUtil.POST_OP_CONSULT_NOTE_HIS_FROM_PRISM)
							.contentType(MediaType.APPLICATION_JSON).header("Authorization", "Bearer " + token)
							.body(BodyInserters.fromValue(opConsultNoteToHisDto)).retrieve().bodyToMono(String.class)
							.block();

					// Manually map the response JSON to the DTO

					return mapResponseToDto(response);

				} catch (Exception e) {
					log.error("An error occurred while posting drug diagnostic orders: ", e.getMessage());
				}
			} else {
				log.error("Failed to login or retrieve access token. Cannot proceed with posting orders.");
			}

			return null;
		});

		try {
			return future.get(); // Blocking to retrieve the CompletableFuture result
		} catch (Exception e) {
			log.error("Error while waiting for drug diagnostic orders CompletableFuture: ", e.getMessage());
			return null;
		}

	}

	@Override
	public DrugDiagnosticApiResponseDto postDrugDiagnosticOrdersPrismImpl(
			DrugDiagnosticOrdersPrism drugDiagnosticOrdersPrism) {
		CompletableFuture<DrugDiagnosticApiResponseDto> future = CompletableFuture.supplyAsync(() -> {
			HisLoginResponseDto login = login(); // Blocking login method

			if (login != null && login.getAccess_token() != null) {
				try {
					// Make the HTTP POST request to the HIS API
					String response = hisClient.post().uri(HisApiUtil.POST_DRUG_DIAGNOSTIC)
							.contentType(MediaType.APPLICATION_JSON)
							.header("Authorization", "Bearer " + login.getAccess_token())
							.body(BodyInserters.fromValue(drugDiagnosticOrdersPrism)).retrieve()
							.bodyToMono(String.class).block();

					// Manually map the response JSON to the DTO
					return mapResponseToDto(response);

				} catch (Exception e) {
					log.error("An error occurred while posting drug diagnostic orders: ", e.getMessage());
				}
			} else {
				log.error("Failed to login or retrieve access token. Cannot proceed with posting orders.");
			}

			return null;
		});

		try {
			return future.get(); // Blocking to retrieve the CompletableFuture result
		} catch (Exception e) {
			log.error("Error while waiting for drug diagnostic orders CompletableFuture: ", e.getMessage());
			return null;
		}
	}

	private DrugDiagnosticApiResponseDto mapResponseToDto(String response) {
		try {
			JsonNode rootNode = new ObjectMapper().readTree(response);

			DrugDiagnosticApiResponseDto dto = new DrugDiagnosticApiResponseDto();

			// Map fields from the "response" object
			JsonNode responseNode = rootNode.get("response");
			if (responseNode != null) {
				dto.setOrderId(responseNode.get("orderID").asText());
				dto.setStatus(responseNode.get("status").asText());
			}

			// Map other fields
			dto.setSuccess(rootNode.get("isSuccess").asBoolean());
			dto.setSucceeded(rootNode.get("succeeded").asBoolean());
			dto.setMessage(rootNode.get("message").asText());
			dto.setResult(rootNode.get("result").asText());

			return dto;
		} catch (Exception e) {
			log.error("Error occurred while mapping response to DTO: ", e.getMessage());
			return null;
		}
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public HisResponseListDto<PatientDetailsDto> fetchPatientsByPhoneNo(String phoneNo) {
		HisLoginResponseDto login = self.login();
		String request = "{\"phoneNo\": \"" + phoneNo + "\"}";

		HisResponseListDto<PatientDetailsDto> patients = hisClient.post().uri(HisApiUtil.PATIENTS_BY_PHONE_NO)
				.contentType(MediaType.APPLICATION_JSON).header("Authorization", "Bearer " + login.getAccess_token())
				.body(BodyInserters.fromValue(request)).retrieve()
				.bodyToMono(new ParameterizedTypeReference<HisResponseListDto<PatientDetailsDto>>() {
				}).block();

		updateDummyPatient(patients, phoneNo);

		return patients;

		// return hisClient.post().uri(HisApiUtil.PATIENTS_BY_PHONE_NO)
		// .contentType(MediaType.APPLICATION_JSON)
		// .header("Authorization", "Bearer "+login.getAccess_token())
		// .body(BodyInserters.fromValue(request))
		// .retrieve().bodyToMono(HisResponseDto.class).block();

	}

	private void updateDummyPatient(HisResponseListDto<PatientDetailsDto> patients, String phoneNo) {
		List<PatientEntity> dummyPatients = patientRepo.findByPhoneNoAndUhIdIsNull(Long.valueOf(phoneNo));
		if (!CollectionUtils.isEmpty(dummyPatients) && patients != null && patients.isSucceeded()
				&& !CollectionUtils.isEmpty(patients.getResponse())) {
			PatientEntity patientEntity = aigOneMapper.toPatientEntity(patients.getResponse().get(0));
			patientEntity.setId(dummyPatients.get(0).getId());
			patientRepo.save(patientEntity);
		}
	}

	@Override
	public String registerPatient(PatientRegistrationRequestDto registrationRequestDto) {
		HisLoginResponseDto login = self.login();
		registrationRequestDto.setHospCode("1100");
		registrationRequestDto.setMobilePrefix("91");
		registrationRequestDto.setNationality("Indian");
		registrationRequestDto.setSourceofInfo("Others");
		registrationRequestDto.setSavedBy(userName);
		registrationRequestDto.setAbhaAddress("E2tQccrPfbWGyDW7wO");
		registrationRequestDto
				.setDateofBirth(DateUtil.format(DateUtil.convertStringToDate(registrationRequestDto.getDateofBirth(),
						DateUtil.FORMAT_YYYY_MM_DD_T_HH_MM_SS), DateUtil.FORMAT_DD_MM_YYYY_EIPHEN));

		ObjectMapper mapper = new ObjectMapper();
		String request = null;
		try {
			request = mapper.writeValueAsString(registrationRequestDto);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}

		String response = hisClient.post().uri(HisApiUtil.PATIENT_REGISTRATION).contentType(MediaType.APPLICATION_JSON)
				.header("Authorization", "Bearer " + login.getAccess_token()).body(BodyInserters.fromValue(request))
				.retrieve().bodyToMono(String.class).block();
		log.info(response);
		try {
			JSONObject registerResponse = new JSONObject(response);
			String success = registerResponse.getString("succeeded");
			if (success == "true") {
				JSONObject resp = new JSONObject(registerResponse.getString("response"));
				String uhId = resp.getString("registrationNo");
				HisResponseDto<PatientDetailsDto> fetchPatientDetailsByUhId = fetchPatientDetailsByUhId(uhId);
				updateDummyPatient(registrationRequestDto, uhId, fetchPatientDetailsByUhId.getResponse());
				return jsoService.pojoToJson(fetchPatientDetailsByUhId);
			}
		} catch (JSONException e) {
			log.error("Error while parsing:");
			throw new AigOneException("Error while fetching the patient:");
		}
		return null;
	}

	private void updateDummyPatient(PatientRegistrationRequestDto registrationRequestDto, String uhId,
			PatientDetailsDto patientDetails) {
		PatientEntity ptE = aigOneMapper.toPatientEntity(patientDetails);
		List<PatientEntity> patients = patientRepo
				.findByPhoneNoAndUhIdIsNull(Long.valueOf(registrationRequestDto.getMobile()));
		if (!CollectionUtils.isEmpty(patients)) {
			PatientEntity patientEntity = patients.get(0);
			ptE.setId(patientEntity.getId());
		}
		ptE.setCreatedFromApp(true);
		ptE.setPhoneNo(Long.valueOf(registrationRequestDto.getMobile()));
		patientRepo.save(ptE);

		// try {
		// JSONObject registerResponse = new JSONObject(patientDetails);
		// String success = registerResponse.getString("succeeded");
		// if(success == "true") {
		// String patientDetailsResponse = registerResponse.getString("response");
		// ObjectMapper mapper = new ObjectMapper();
		// PatientDetailsDto value = mapper.readValue(patientDetailsResponse,
		// PatientDetailsDto.class);
		// PatientEntity ptE = aigOneMapper.toPatientEntity(patientDetails);
		// List<PatientEntity> patients =
		// patientRepo.findByPhoneNoAndUhIdIsNull(Long.valueOf(registrationRequestDto.getMobile()));
		// if(!CollectionUtils.isEmpty(patients)) {
		// PatientEntity patientEntity = patients.get(0);
		// ptE.setId(patientEntity.getId());
		// }
		// ptE.setCreatedFromApp(true);
		// ptE.setPhoneNo(Long.valueOf(registrationRequestDto.getMobile()));
		// patientRepo.save(ptE);
		// }
		// } catch (JSONException | JsonProcessingException e) {
		// log.error("Error while parsing::"+e.getMessage());
		// }
	}

	@Override
	public List<String> fetchStates() {
		return hsDbRepo.fetchStates();
	}

	@Override
	public List<String> fetchCities(String stateName) {
		return hsDbRepo.fetchCities(stateName);
	}

	@Override
	public List<String> fetchTitle() {
		return hsDbRepo.fetchTitle();
	}

	@Override
	public List<String> fetchMaritalStatus() {
		return hsDbRepo.fetchMaritalStatus();
	}

	@Override
	public String fetchPaymentInfo(PaymentInfoDto paymentInfoDto) {
		String regNo = paymentInfoDto.getUhid().split("\\.")[1];
		String data = hsDbRepo.fetchPaymentInfo(regNo, paymentInfoDto.getAmount(), paymentInfoDto.getTime());
		if (data != null)
			return "Payment Success";
		else
			return "Payment Failed";
	}

	@Override
	public UserDto fetchDocotrDetailsByPhoneNo(long phoneNo) {
		UserDto doctorDetails = hsDbRepo.fetchDocotrDetailsByPhoneNo(phoneNo);
		if (doctorDetails == null) {
			throw new EntityNotFoundException("No Doctors found for given phoneNo: " + phoneNo);
		}
		return doctorDetails;
	}

	@Override
	public UserDto fetchAssistantDetailsByPhoneNo(long phoneNo) {
		UserDto assistantDetails = hsDbRepo.fetchAssistantDetailsByPhoneNo(phoneNo);
		if (assistantDetails == null) {
			throw new EntityNotFoundException("No Assistants found for given phoneNo: " + phoneNo);
		}
		return assistantDetails;
	}

	@Override
	public List<UserDto> fetchMappedDcotorsByAssistantId(long assistantId) {
		List<UserDto> doctorsDetails = hsDbRepo.fetchMappedDcotorsByAssistantId(assistantId);
		if (CollectionUtils.isEmpty(doctorsDetails)) {
			throw new EntityNotFoundException("No Mapped Doctors found for given assistant: " + assistantId);
		}
		return doctorsDetails;
	}

	@Override
	public int fetchOPCountByDoctorIdAndDate(long doctorId, Date date) {
		return hsDbRepo.fetchOpCountByDoctorIdAndDate(doctorId, date);
	}

	@Override
	public int fetchIPCountByDoctorId(long doctorId) {
		return hsDbRepo.fetchIPCountByDoctorId(doctorId);
	}

	@Override
	public List<PateintAppointmentsDto> fetchAppointmentsByUhIdAndDate(String uhId, Date date) {
		List<PateintAppointmentsDto> appointmentDetails = hsDbRepo.fetchAppointmentsByUhIdAndDate(uhId);
		// if(CollectionUtils.isEmpty(appointmentDetails)) {
		// throw new AigOneException("No Appointments found for given uhid: "+uhId +"
		// date: "+date);
		// }
		return appointmentDetails;
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public HisResponseDto<PatientDetailsDto> fetchPatientDetailsByUhId(String uhId) {
		HisLoginResponseDto login = self.login();
		String request = "{\"patientID\": \"" + uhId + "\"}";

		return hisClient.post().uri(HisApiUtil.PATIENT_BY_UHID).contentType(MediaType.APPLICATION_JSON)
				.header("Authorization", "Bearer " + login.getAccess_token()).body(BodyInserters.fromValue(request))
				.retrieve().bodyToMono(new ParameterizedTypeReference<HisResponseDto<PatientDetailsDto>>() {
				}).block();
	}

	@Override
	public List<OrderDto> fetchLabOrdersByUhId(String uhId) {
		// return prepareTestDataForOrders();
		return fetchUpcomingOrdersByUhId(uhId);
	}

	private List<OrderDto> prepareTestDataForOrders() {
		return IntStream.range(0, 3).mapToObj(i -> prepareOder(i)).toList();

	}

	private OrderDto prepareOder(int i) {
		OrderDto orderDto = new OrderDto();
		orderDto.setOrderId(Long.valueOf(i));
		orderDto.setOrderType(i == 1 ? "Lab Order" : (i == 2 ? "Pharmacy Order " : "Diagnostic Order"));
		orderDto.setDoctorName("Dr. Praneeth");
		orderDto.setAmount(1000);
		orderDto.setOrderedTime(new Date());
		orderDto.setVisitId(String.valueOf(i));
		orderDto.setItemDetails(preparItemDetais(i, orderDto));
		return orderDto;
	}

	private List<OrderItemDto> preparItemDetais(int i, OrderDto orderDto) {
		return IntStream.range(0, 3).mapToObj(j -> prepareItem(i, j, orderDto)).toList();
	}

	private OrderItemDto prepareItem(int i, int j, OrderDto order) {
		OrderItemDto orderItemDto = new OrderItemDto();
		orderItemDto.setAmount(100);
		orderItemDto.setItemId(String.valueOf(i));
		if (i == 1) {
			orderItemDto.setItemName(j == 1 ? "ANO RECTAL MANOMETRY"
					: (j == 2 ? "ASCITIC TAPPING  DIAGNOSTIC" : "TYMPANOMETRY (IMPEDENCE AUDIOMETRY)"));
		} else if (i == 2) {
			orderItemDto.setItemName(j == 1 ? "Dolo" : (j == 2 ? "Naprosyn" : "Paracetmol"));
		} else {
			orderItemDto.setItemName(j == 1 ? "XRAY" : (j == 2 ? "2D ECHO - SCREENING" : "UGI ENDOSCOPY"));
		}

		ProcedureEntity procedureEntity = procedureInfoRepo.findByProcedureCodes(orderItemDto.getItemName());
		if (procedureEntity != null && procedureEntity.getInformationGuide() != null) {
			orderItemDto.setUserGuide(procedureEntity.getInformationGuide().stream()
					.map(ig -> aigOneMapper.toProcedureInfoDto(ig)).toList());
		}
		return orderItemDto;
	}

	@Cacheable(value = AigOneUtil.CACHE_HIS, key = "{#root.methodName, #uhId,#employeeId}")
	@Override
	public List<PatientIPDetailsDto> fetchPatiennIpDetailsByUhId(String uhId, String employeeId) {

		List<PatientIPDetailsDto> fetchIpDetailsByUhId = hsDbRepo.fetchIpDetailsByUhId(uhId);

		if (!CollectionUtils.isEmpty(fetchIpDetailsByUhId) && employeeId != null) {
			List<MDTEntity> allOpenMdtCasesForGivenUhId = mdtRepo.loadAllOpenMdtCasesForGivenUhId(uhId);
			DoctorFavouritePatientsEntity byEmployeeIdAndUhIdAndIpNoAndStatus = favouritePatientsRepo
					.findByEmployeeIdAndUhIdAndIpNoAndStatus(employeeId, uhId,
							String.valueOf(fetchIpDetailsByUhId.get(0).getIpNo()), AigOneUtil.STATUS_FOLLOW);
			fetchIpDetailsByUhId = fetchIpDetailsByUhId.stream().map(ip -> {
				ip.setFollowStatus(byEmployeeIdAndUhIdAndIpNoAndStatus != null);
				ip.setMdtStatus(allOpenMdtCasesForGivenUhId != null && allOpenMdtCasesForGivenUhId.size() > 0);
				if (ip.isMdtStatus()) {
					MDTEntity mdtEntity = allOpenMdtCasesForGivenUhId.get(0);
					MdtDto mdtDto = aigOneMapper.toMdtDto(allOpenMdtCasesForGivenUhId.get(0));
					List<String> diggnosisList = Arrays.stream(mdtEntity.getDiagnosis().split(","))
							.collect(Collectors.toCollection(ArrayList::new));
					mdtDto.setDiagnosisList(diggnosisList);
					ip.setMdt(mdtDto);
				}
				return ip;
			}).toList();

		}
		return fetchIpDetailsByUhId;
	}

	private boolean validateAccess(UserEntity user) {
		boolean mdtAccess = false;
		Set<RoleEntity> roles = user.getRoles();
		if (!CollectionUtils.isEmpty(roles)) {
			mdtAccess = roles.stream().anyMatch(r -> r.getCode().equals("ROLE_MDT"));
		}
		return (user.isConsultant() || mdtAccess);
	}

	@Override
	public List<PatientIPDetailsDto> fetchPatiennIpDetailsByBedNo(String bedNo) {
		String employeeId = SecurityUtil.getCurrentUser().getEmployeeId();

		UserEntity user = userRepo.findByEmployeeIdAndActiveTrue(employeeId).orElseThrow(
				() -> new EntityNotFoundException("Employee Not Found with given employee id: " + employeeId));

		if (!validateAccess(user)) {
			return null;
		}

		List<PatientIPDetailsDto> fetchIpDetailsByUhId = hsDbRepo.fetchIpDetailsByBedNo(bedNo);

		if (CollectionUtils.isEmpty(fetchIpDetailsByUhId)) {
			throw new AigOneException("no ip patiens found for given bed no:" + bedNo);
		}

		PatientIPDetailsDto patientIPDetailsDto = fetchIpDetailsByUhId.get(0);
		List<MDTEntity> allOpenMdtCasesForGivenUhId = mdtRepo
				.loadAllOpenMdtCasesForGivenUhId(patientIPDetailsDto.getUhId());
		DoctorFavouritePatientsEntity byEmployeeIdAndUhIdAndIpNoAndStatus = favouritePatientsRepo
				.findByEmployeeIdAndUhIdAndIpNoAndStatus(employeeId, patientIPDetailsDto.getUhId(),
						String.valueOf(patientIPDetailsDto.getIpNo()), AigOneUtil.STATUS_FOLLOW);
		fetchIpDetailsByUhId = fetchIpDetailsByUhId.stream().map(ip -> {
			ip.setFollowStatus(byEmployeeIdAndUhIdAndIpNoAndStatus != null);
			ip.setMdtStatus(allOpenMdtCasesForGivenUhId != null && allOpenMdtCasesForGivenUhId.size() > 0);
			if (ip.isMdtStatus()) {
				ip.setMdt(aigOneMapper.toMdtDto(allOpenMdtCasesForGivenUhId.get(0)));

			}
			return ip;
		}).toList();

		return fetchIpDetailsByUhId;
	}

	@Override
	public List<PatientIPDetailsDto> fetchPatiennIpDetailsByDoctorId(Long doctorId) {
		return hsDbRepo.fetchIpDetailsByDoctorId(doctorId);
	}

	@Override
	public List<PatientIPDetailsDto> fetchPatiennIpDetailsByDoctorId(String doctorId) {
		return hsDbRepo.fetchIpDetailsByDoctorId(doctorId);
	}

	@Override
	public List<PatientOPDetailsDto> fetchPatientOpDetailsByDoctorId(String empId, Date date) {
		return hsDbRepo.fetchOpDetailsByDoctorIdAndDate(empId, date);
	}

	@Override
	public String updateAttendance(String employeeId, String type) {
		Optional<UserEntity> user = userRepo.findByEmployeeIdAndActiveTrue(employeeId);
		if (user.isEmpty()) {
			throw new AigOneException("User Not Found");
		}
		if (type.equals("CHECKIN")) {
			UserAttendanceEntity userAttendance = new UserAttendanceEntity();
			userAttendance.setUser(user.get());
			userAttendance.setCheckInTime(new Date());
			userAttendanceRepo.save(userAttendance);

			return "Checkin Successfull";
		} else if (type.equals("CHECKOUT")) {
			Optional<UserAttendanceEntity> lastCheckin = userAttendanceRepo.findLatestCheckInForUser(user.get().getId(),
					new Date());
			if (!lastCheckin.isEmpty()) {
				UserAttendanceEntity checkout = lastCheckin.get();
				checkout.setCheckOutTime(new Date());
				userAttendanceRepo.save(checkout);
			} else {
				throw new AigOneException("Checkin record not found");
			}

			return "Checkout Successfull";
		}
		return null;
	}

	@Override
	public void processExcelFile(MultipartFile file) {
		Workbook workbook = null;
		try {
			workbook = new XSSFWorkbook(file.getInputStream());
		} catch (IOException e) {
			e.printStackTrace();
		}
		Sheet sheet = workbook.getSheetAt(0);
		Iterator<Row> rows = sheet.iterator();

		// Skip the header row
		if (rows.hasNext()) {
			rows.next();
		}

		while (rows.hasNext()) {
			Row currentRow = rows.next();
			Cell procedureNameCell = currentRow.getCell(0);
			Cell descriptionCell = currentRow.getCell(2);
			Cell preparationCell = currentRow.getCell(3);
			Cell floorCell = currentRow.getCell(4);
			Cell towerCell = currentRow.getCell(5);
			Cell locationCell = currentRow.getCell(6);
			Cell procedureTimeCell = currentRow.getCell(7);
			Cell reportDispatchTimeCell = currentRow.getCell(8);
			Cell reportCollectionCell = currentRow.getCell(9);
			Cell preRequisitesCell = currentRow.getCell(10);

			if (procedureNameCell != null) {
				String procedureName = procedureNameCell.getStringCellValue().trim();

				ProcedureEntity procedure = procedureInfoRepo.findByProcedureName(procedureName);
				if (procedure != null) {

					String location = "Tower " + (getCellValue(towerCell) != null ? getCellValue(towerCell) : "")
							+ " - " + (getCellValue(floorCell) != null ? getCellValue(floorCell) : "") + " Floor "
							+ (getCellValue(locationCell) != null ? getCellValue(locationCell) : "");

					updateProcedureInfoGuide(procedure, "DESCRIPTION", getCellValue(descriptionCell));
					updateProcedureInfoGuide(procedure, "LOCATION", location);
					updateProcedureInfoGuide(procedure, "PREPARATION",
							"Specific Procedure Preparation/Instruction: " + getCellValue(preparationCell));
					updateProcedureInfoGuide(procedure, "PROCEDURE_TIME",
							"Complete Procedure Time(End to End): " + getCellValue(procedureTimeCell));
					updateProcedureInfoGuide(procedure, "DISPATCH_TIME", "Report Dispatch Time: "
							+ getCellValue(reportDispatchTimeCell) + " (" + getCellValue(reportCollectionCell) + ")");
					updateProcedureInfoGuide(procedure, "PRE_REQUISITES",
							"Pre Requisties: " + getCellValue(preRequisitesCell));
				}
			}
		}

		try {
			workbook.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	private void updateProcedureInfoGuide(ProcedureEntity procedure, String title, String value) {
		if (value != null && !value.isEmpty()) {
			ProcedureInfoGuideEntity guideEntity = procedureInfoGuideRepo.findByProcedureIdAndTitle(procedure.getId(),
					title);
			if (guideEntity != null) {
				if ("LOCATION".equals(title)) {
					if (guideEntity.getDescription() != null && guideEntity.getDescription().contains(value)) {
						return;
					} else {
						String existingDescription = guideEntity.getDescription();
						if (existingDescription != null && !existingDescription.isEmpty()) {
							value = existingDescription + ", " + value;
						}
					}
				}
				guideEntity.setDescription(value);
				procedureInfoGuideRepo.save(guideEntity);
			} else {
				guideEntity = new ProcedureInfoGuideEntity();
				guideEntity.setProcedure(procedure);
				guideEntity.setTitle(title);
				guideEntity.setDescription(value);
				procedureInfoGuideRepo.save(guideEntity);
			}
		}
	}

	private String getCellValue(Cell cell) {
		if (cell == null || cell.getCellType() == CellType.BLANK) {
			return null;
		}
		return cell.getStringCellValue().trim();
	}

	@Override
	public List<FaqEntity> fetchFaqsByType(String type) {
		List<FaqEntity> response = faqRepo.findByTypeOrderByPriorityAsc(FaqTypeEnum.valueOf(type));
		return response;
	}

	@Override
	public List<PatientIPDetailsDto> fetchPatiennIpDetailsByIpId(Integer ipNo) {
		return hsDbRepo.fetchIpDetailsByIpId(ipNo);
	}

	@Override
	public List<OrderDto> fetchUpcomingOrdersByUhId(String uhId) {

		HisLoginResponseDto login = self.login();
		String request = "{\"uhid\": \"" + uhId + "\"}";

		String response = hisClient.post().uri(HisApiUtil.PATIENT_UPCOMING_ORDERS)
				.contentType(MediaType.APPLICATION_JSON).header("Authorization", "Bearer " + login.getAccess_token())
				.body(BodyInserters.fromValue(request)).retrieve().bodyToMono(String.class).block();
//		log.info(String.format("fetchUpcomingOrdersByUhId Response: %s, for uhid:%s ",response , uhId));

		try {
			JSONObject registerResponse = new JSONObject(response);
			String success = registerResponse.getString("succeeded");
			if (success.equals("true")) {
				String enableMitrPayment = systemSettingsService
						.getSettingValue(SystemSettingsKey.ENABLE_MITHR_PAYMENT.getName());

				String patientDetailsResponse = registerResponse.getString("response");
				ObjectMapper mapper = new ObjectMapper();

				List<OrderDto> orders = mapper.readValue(patientDetailsResponse, new TypeReference<List<OrderDto>>() {
				});

				if (!CollectionUtils.isEmpty(orders)) {

//					Date previousDayByGivenNo = DateUtil.getPreviousDayByGivenNo(new Date(), 3);

					orders = orders.stream()
							.filter(o -> DateUtil.getPreviousDayByGivenNo(new Date(), 3).before(o.getOrderedTime()))
							.toList();

//					orders.sort(Comparator.comparing(OrderDto::getOrderedTime));

					orders.stream().forEach(o -> {
						List<OrderItemDto> filteredItems = new ArrayList<>();
						AtomicReference<Float> totalAmount = new AtomicReference<>(0.0f);
						o.getItemDetails().stream().forEach(id -> {
							ProcedureEntity procedureEntity = procedureInfoRepo.findByProcedureCodes(id.getItemName());
							if (procedureEntity != null && procedureEntity.getInformationGuide() != null) {
								id.setUserGuide(procedureEntity.getInformationGuide().stream()
										.map(ig -> aigOneMapper.toProcedureInfoDto(ig))
										.sorted(Comparator.comparingInt(ProcedureInfoGuideDto::getOrder)).toList());
							}
							if (enableMitrPayment.equals("true")) {
								id.setPayable(o.getOrderType().equals("Pharmacy Order") ? false
										: payableServiceRepo.findByProcedureName(id.getItemName()) != null);
							}

							if (o.getOrderType().equals("Pharmacy Order") || id.isPayable()) {
								filteredItems.add(id);
								totalAmount.updateAndGet(v -> v + id.getNetAmount());
							}
						});
						o.setAmount(totalAmount.get());
//				        o.setItemDetails(filteredItems);
//				        o.setPayable(!o.getOrderType().equals("Pharmacy Order") && !filteredItems.isEmpty());
						if (enableMitrPayment.equals("true")) {
							o.setPayable(!o.getOrderType().equals("Pharmacy Order") && !filteredItems.isEmpty());
						}
					});
				}
				List<OrderDto> list = orders.stream().filter(o -> !o.getItemDetails().isEmpty()).toList();

				return list;
			} else {
				log.error("fetchUpcomingOrdersByUhId is not successful: " + registerResponse.getString("message"));
			}
		} catch (JSONException | JsonProcessingException e) {
			log.error("Error while parsing::" + e.getMessage());

		}
		return null;

		// return hisClient.post().uri(HisApiUtil.PATIENT_UPCOMING_ORDERS)
		// .contentType(MediaType.APPLICATION_JSON)
		// .header("Authorization", "Bearer "+login.getAccess_token())
		// .body(BodyInserters.fromValue(request))
		// .retrieve().bodyToMono(String.class).block();

	}

	@Override
	public List<OrderDto> fetchCompletedOrdersByUhId(String uhId) {
		List<OrderDetailsQueryResults> completedOrders = hsDbRepo.fetchCompletedOrdersByUhId(uhId);
		List<OrderDto> orderDetails = new ArrayList<>();
		if (CollectionUtils.isEmpty(completedOrders)) {
			log.info("No completed orders found for user: " + uhId);
			return orderDetails;
		}

		Map<Integer, List<OrderDetailsQueryResults>> ordersList = completedOrders.stream()
				.collect(Collectors.groupingBy(OrderDetailsQueryResults::getOrderId));
		ordersList.forEach((k, v) -> {
			OrderDto dto = new OrderDto();
			dto.setOrderId(Long.valueOf(k));
			dto.setBillNo(v.get(0).getBillNo());
			dto.setOrderedTime(v.get(0).getOrderedTime());
			if (v.get(0).getPaidAmount() != null && v.get(0).getDepositAmount() != null) {
				dto.setAmount(v.get(0).getPaidAmount().floatValue() + v.get(0).getDepositAmount().floatValue());
			}
			// dto.setAmount(v.get(0).getPaidAmount() != null ?
			// v.get(0).getPaidAmount().floatValue():0);
			Map<String, List<OrderDetailsQueryResults>> serviceCodes = v.stream()
					.collect(Collectors.groupingBy(OrderDetailsQueryResults::getServiceCode));
			dto.setOrderType(serviceCodes.keySet().stream()
					.map(key -> key.equals("LAB") ? "Lab Test" : (key.equals("PR") ? "Procedure" : "Dignostics"))
					.collect(Collectors.joining(",")));
			// dto.setOrderType(serviceCodes.keySet().stream().collect(Collectors.joining(",")));

			// prepare Order Items
			List<OrderItemDto> items = new ArrayList<>();
			v.stream().forEach(oli -> {
				OrderItemDto itemDto = new OrderItemDto();
				itemDto.setAmount(oli.getItemAmount() != null ? oli.getItemAmount().floatValue() : 0);
				itemDto.setItemName(oli.getItemName());
				items.add(itemDto);
			});
			dto.setItemDetails(items);
			orderDetails.add(dto);
			orderDetails.sort(Comparator.comparing(OrderDto::getOrderedTime).reversed());
			// orderDetails.stream().sorted((o1, o2) -> (o2.getOrderedTime() >
			// o1.getOrderedTime())).toList();

		});
		return orderDetails;
	}

	@Override
	public HisResponseListDto<OrderResponseDto> createOrder(OrderDto orderDto) {
		
		System.out.println(orderDto.toString()+"orderDto before sending to his ");
		HisLoginResponseDto login = self.login();

		HisResponseListDto<OrderResponseDto> response = hisClient.post().uri(HisApiUtil.CREATE_ORDER)
				.contentType(MediaType.APPLICATION_JSON).header("Authorization", "Bearer " + login.getAccess_token())
				.body(BodyInserters.fromValue(orderDto)).retrieve()
				.bodyToMono(new ParameterizedTypeReference<HisResponseListDto<OrderResponseDto>>() {
				}).block();
		log.info("Order Response for::" + response);

		return response;
	}
	
	@Override
	public HisResponseDto<AppointmentRequestResponseDto> saveAppointmentRequest(AppointmentRequestDto appReqDto) {
		HisLoginResponseDto login = self.login();
		HisResponseDto<AppointmentRequestResponseDto> response = hisClient.post().uri(HisApiUtil.SAVE_APPOINTMENT_REQUEST)
				.contentType(MediaType.APPLICATION_JSON).header("Authorization", "Bearer " + login.getAccess_token())
				.body(BodyInserters.fromValue(appReqDto)).retrieve()
				.bodyToMono(new ParameterizedTypeReference<HisResponseDto<AppointmentRequestResponseDto>>() {
				}).block();
		log.info("Appointment Request Response for::" + response);

		return response;
	}
	
	@Override
	public HisResponseDto<OrderResponseDto> createOldOrder(OrderDto orderDto) {
		HisLoginResponseDto login = self.login();

		HisResponseDto<OrderResponseDto> response = hisClient.post().uri(HisApiUtil.CREATE_ORDER)
				.contentType(MediaType.APPLICATION_JSON).header("Authorization", "Bearer " + login.getAccess_token())
				.body(BodyInserters.fromValue(orderDto)).retrieve()
				.bodyToMono(new ParameterizedTypeReference<HisResponseDto<OrderResponseDto>>() {
				}).block();
		log.info("Order Response for::" + response);

		return response;
	}

	@Override
	public List<PateintPreviousAppointmentsDto> fetchPreviousAppointmentsByUhId(String uhId) {
		List<PateintPreviousAppointmentsDto> appointmentDetails = hsDbRepo.fetchPreviousAppointmentsByUhId(uhId);
		return appointmentDetails;
	}

	@Override
	public List<String> fetchDoctorSchedulesForSpecificDays(DoctorScheduleRequestDto requestDto) {
		requestDto.setDays(30);
		requestDto.setDateofAppointment(new Date());
		HisLoginResponseDto login = self.login();
		String response = hisClient.post().uri(HisApiUtil.GET_DOCTOR_SCHEDULE_FOR_SPECIFIC_DAYS)
				.contentType(MediaType.APPLICATION_JSON).header("Authorization", "Bearer " + login.getAccess_token())
				.body(BodyInserters.fromValue(requestDto)).retrieve().bodyToMono(String.class).block();
		List<String> dates = new ArrayList<>();
		try {
			JSONObject registerResponse = new JSONObject(response);
			String success = registerResponse.getString("succeeded");
			if (success == "true") {
				String patientDetailsResponse = registerResponse.getString("response");
				List<DoctorAppointmentDetailsDaywiseRequestDto> appointments = jsoService.jsonToPojo(
						patientDetailsResponse, new TypeReference<List<DoctorAppointmentDetailsDaywiseRequestDto>>() {
						});
				appointments.stream().forEach(a -> {
					String s = DateUtil.format(a.getDate(), DateUtil.FORMAT_DD_MM_YY);
					String[] split = s.split("/");
					dates.add(split[0] + "/" + split[1] + "/20" + split[2]);
				});

			} else {
				log.error("fetchUpcomingOrdersByUhId is nor successful: " + registerResponse.getString("message"));
			}
			return dates;
		} catch (JSONException e) {
			log.error("Error while parsing::" + e.getMessage());

		}
		return null;
	}

	@Override
	public List<DoctorAvailableSlotsDto> fetchDoctorAvailableSlotsDaywise(
			DoctorAppointmentDetailsDaywiseRequestDto requestDto) {
		requestDto.setFromDate(requestDto.getRequestDate());
		requestDto.setToDate(requestDto.getRequestDate());
		HisLoginResponseDto login = self.login();
		String response = hisClient.post().uri(HisApiUtil.GET_DOCTOR_APPOINTMENT_DETAILS_DAY_WISE)
				.contentType(MediaType.APPLICATION_JSON).header("Authorization", "Bearer " + login.getAccess_token())
				.body(BodyInserters.fromValue(requestDto)).retrieve().bodyToMono(String.class).block();
		log.info("GET_DOCTOR_APPOINTMENT_DETAILS_DAY_WISE Response::" + response);

		try {
			JSONObject registerResponse = new JSONObject(response);
			String success = registerResponse.getString("succeeded");
			if (success == "true") {
				String patientDetailsResponse = registerResponse.getString("response");
				// System.out.println(patientDetailsResponse);
				List<DoctorAppointmentDetailsDaywiseResponseDto> appointments = jsoService.jsonToPojo(
						patientDetailsResponse, new TypeReference<List<DoctorAppointmentDetailsDaywiseResponseDto>>() {
						});
				return prepareDoctorSlots(appointments, 2);
			} else {
				log.error("fetchUpcomingOrdersByUhId is nor successful: " + registerResponse.getString("message"));
			}
		} catch (JSONException e) {
			log.error("Error while parsing::" + e.getMessage());

		}

		return null;
	}

	private List<DoctorAvailableSlotsDto> prepareDoctorSlots(
			List<DoctorAppointmentDetailsDaywiseResponseDto> appointments, int noOfHours) {
		// Map<Integer, List<DoctorAppointmentDetailsDaywiseResponseDto>> groupedByHours
		// = timeIntervals.stream()
		// .collect(Collectors.groupingBy(timeInterval ->
		// getHour(timeInterval.getFrom()) / groupByHours));

		List<DoctorAvailableSlotsDto> slots = new ArrayList<>();
		if (!CollectionUtils.isEmpty(appointments)) {
			List<String> timeSlots = generateTimeSlots(2);
			appointments = appointments.stream().filter(ap -> {
				return StringUtils.isBlank(ap.getMobileNumber()) && ap.getFromTime().after(new Date());
			}).toList();
			appointments = appointments.stream()
					.sorted(Comparator.comparing(DoctorAppointmentDetailsDaywiseResponseDto::getFromTime)).toList();
			// System.out.println("pojo to json"+jsoService.pojoToJson(appointments));

			Map<String, List<DoctorAppointmentDetailsDaywiseResponseDto>> availableSlots = appointments.stream()
					.collect(Collectors.groupingBy(date -> {
						int hour = date.getFromTime().getHours();
						return timeSlots.stream().filter(slot -> {
							String[] parts = slot.split("-");
							int start = Integer.parseInt(parts[0]);
							int end = Integer.parseInt(parts[1]);
							return hour >= start && hour < end;
						}).findFirst().orElse("No suitable slot found");
					}, LinkedHashMap::new, Collectors.toList()));

			availableSlots.forEach((k, v) -> {
				DoctorAvailableSlotsDto d = new DoctorAvailableSlotsDto();
				d.setName(convertToFullTime(k));
				List<String> slotsString = new ArrayList<>();
				v.stream().forEach(s -> {
					String from = DateUtil.format(s.getFromTime(), DateUtil.FORMAT_HH_MM);
					String to = DateUtil.format(s.getToTime(), DateUtil.FORMAT_HH_MM);
					slotsString.add(from + "-" + to);
				});

				d.setSlots(slotsString);
				d.setAvailableSlots(v);
				slots.add(d);

			});
		}
		return slots;
	}

	private String convertToFullTime(String input) {
		String[] parts = input.split("-");

		if (parts.length != 2) {
			throw new IllegalArgumentException("Input should be in the format 'start-end'");
		}

		String startTime = parts[0].trim() + ":00";
		String endTime = parts[1].trim() + ":00";
		return startTime + "-" + endTime;
	}

	@Override
	public String savePatientAppointmentWithDoctor(DoctorAvailableSlotsDto requestDto) {
		SavePatientAppointmentRequestDto d = new SavePatientAppointmentRequestDto();
		d.setPatientID(requestDto.getUhId());
		d.setHospCode(requestDto.getHospitalCode());
		d.setDcotorCode(requestDto.getDoctorCode());
		d.setFromDateTime(requestDto.getAvailableSlots().get(0).getFromTime());
		d.setToDateTime(requestDto.getAvailableSlots().get(0).getToTime());
		d.setConsultationType("NEW CONSULTATION");
		d.setSourceofAppointment("Patient Portal");
//		d.setAppointmentSaveDatetime(DateUtil.getPreviousDay(new Date()));
		d.setAppointmentSaveDatetime(DateUtil.getPreviousDay(new Date()));
		d.setSavedBy(userName);
		d.setRemarks("Saved By " + userName);

		HisLoginResponseDto login = self.login();
		return hisClient.post().uri(HisApiUtil.SAVE_PATIENT_APPOINTMENT_WITH_DOCTOR)
				.contentType(MediaType.APPLICATION_JSON).header("Authorization", "Bearer " + login.getAccess_token())
				.body(BodyInserters.fromValue(d)).retrieve().bodyToMono(String.class).block();
	}

	private List<String> generateTimeSlots(int duration) {
		int limit = 24;
		return IntStream.iterate(0, n -> n + duration).limit(limit / duration)
				.mapToObj(start -> start + "-" + (start + duration)).collect(Collectors.toList());
	}

	@Override
	public List<WellnessPackageDto> fetchWellnessPackage() {
		List<WellnessPackageDto> response = hsDbRepo.fetchWellnessPackage();
		Map<String, WellnessPackageDto> packageMap = new HashMap<>();

		for (WellnessPackageDto dto : response) {
			List<ServiceDto> serviceDtoList = new ArrayList<>();
			String packageName = dto.getName();
			List<String> serviceList = Arrays.asList((dto.getAllServices()).split("\\|"));
			ServiceDto serviceDto = new ServiceDto(dto.getCategoryName(), serviceList);
			serviceDtoList.add(serviceDto);
			if (packageMap.containsKey(packageName)) {
				WellnessPackageDto existingPackageDto = packageMap.get(packageName);
				existingPackageDto.getServices().addAll(serviceDtoList);
			} else {
				WellnessPackageDto newPackageDto = new WellnessPackageDto(dto.getName(), dto.getAmount(),
						serviceDtoList);
				packageMap.put(packageName, newPackageDto);
			}
		}

		List<WellnessPackageDto> formattedResponse = new ArrayList<>(packageMap.values());
		formattedResponse.sort(Comparator.comparingInt(WellnessPackageDto::getAmount));
		return formattedResponse;
	}

//	@Override
//	public List<TicketQuestionsEntity> fetchTicketQuestions(String question) {
//		TicketQuesEnum type = SecurityUtil.getCurrentUser().getRequestedFrom().equals(
//				RequestedFromEnum.AIG_MITHR.name()) ? TicketQuesEnum.HOME_SERVICES : TicketQuesEnum.EMPLOYEE_SERVICES;
//
//		return (question.isBlank() || question.isEmpty()) ? TicketQuesRepo.findByType(type)
//				: TicketQuesRepo.findByTypeAndQuestionContainingIgnoreCase(type, question);
//	}
	
	
	@Override
	public List<TicketQuestionsEntity> fetchTicketQuestions(String question) {
	    TicketQuesEnum type = SecurityUtil.getCurrentUser().getRequestedFrom().equals(
	            RequestedFromEnum.AIG_MITHR.name()) ? TicketQuesEnum.HOME_SERVICES : TicketQuesEnum.EMPLOYEE_SERVICES;

	    return (question == null || question.isBlank()) ? 
	            TicketQuesRepo.findByTypeAndShowInAigTrue(type) : 
	            TicketQuesRepo.findByTypeAndQuestionContainingIgnoreCaseAndShowInAigTrue(type, question);
	}
	
	
	@Override
	public List<EmployeeRosterDto> fetchEmployeeRosterDateWise(String date, String department, String subDepartment) {
	    Date formated_date = DateUtil.convertStringToDate(date, DateUtil.FORMAT_YYYY_MM_DD);
	    List<EmployeeRosterEntity> rosterEntities;

	    if ((subDepartment == null) || subDepartment.isEmpty()) {
	        rosterEntities = EmployeeRosterRepo.findByDateAndDepartmentOrderBySubDepartmentAscPriorityAsc(
	                formated_date, EmployeeRosterDepartmentEnum.valueOf(department));
	    } else {
	        rosterEntities = EmployeeRosterRepo.findByDateAndDepartmentAndSubDepartmentIgnoreCaseOrderByPriority(
	                formated_date, EmployeeRosterDepartmentEnum.valueOf(department), subDepartment);
	    }

	    // Always show full day & night details
	    List<EmployeeRosterDto> formattedEntities = rosterEntities.stream().map(entity -> {
	        EmployeeRosterDto dto = aigOneMapper.toEmployeeRosterDto(entity);
	        dto.setDayTimeLabel("9 AM to 5 PM");
	        dto.setNightTimeLable("5 PM to 9 AM");
	        return dto;
	    }).collect(Collectors.toList());

	    return formattedEntities;
	}


//	@Override
//	public List<EmployeeRosterDto> fetchEmployeeRosterDateWise(String date, String department, String subDepartment) {
//		Date formated_date = DateUtil.convertStringToDate(date, DateUtil.FORMAT_YYYY_MM_DD);
//		List<EmployeeRosterEntity> rosterEntities;
//
//		if ((subDepartment == "") || (subDepartment == null)) {
//			rosterEntities = EmployeeRosterRepo.findByDateAndDepartmentOrderBySubDepartmentAscPriorityAsc(formated_date,
//					EmployeeRosterDepartmentEnum.valueOf(department));
//		} else {
//			rosterEntities = EmployeeRosterRepo.findByDateAndDepartmentAndSubDepartmentIgnoreCaseOrderByPriority(
//					formated_date, EmployeeRosterDepartmentEnum.valueOf(department), subDepartment);
//		}
//		boolean isToday = DateUtil.isToday(formated_date);
//		StringBuilder employeeTime = new StringBuilder();
//
//		if (isToday) {
//			LocalTime currentTime = LocalTime.now();
//			LocalTime fivePM = LocalTime.of(17, 0);
//			employeeTime.append(currentTime.isBefore(fivePM) ? "MORNING" : "NIGHT");
//
//		}
//		List<EmployeeRosterDto> formattedEntities = rosterEntities.stream().map(entity -> {
//			EmployeeRosterDto dto = aigOneMapper.toEmployeeRosterDto(entity);
//			if (employeeTime.length() > 0) {
//				if (employeeTime.toString().equals("MORNING")) {
//					dto.setDayTimeLabel("9 AM to 5 PM");
//					dto.setNightEmployee(null);
//					dto.setNightEmployeePhoneNo(null);
//				} else {
//					dto.setNightTimeLable("5PM TO 9 AM");
//					dto.setDayEmployee(null);
//					dto.setDayEmployeePhoneNo(null);
//				}
//			} else {
//				dto.setDayTimeLabel("9 AM to 5 PM");
//				dto.setNightTimeLable("5 PM to 9 AM");
//			}
//			return dto;
//		}).filter(employee -> {
//			if (employeeTime.length() > 0) {
//				return employeeTime.toString().equals("MORNING") ? employee.getDayEmployee() != null
//						: employee.getNightEmployee() != null;
//			}
//			return true;
//		}).collect(Collectors.toList());
//
//		return formattedEntities;
//
//	}

	@Override
	public String deleteAppointment(Long appointmentId) {
		List<PateintAppointmentsDto> appointments = hsDbRepo.fecthAppointmentByAppointmentId(appointmentId);
		if (CollectionUtils.isEmpty(appointments)) {
			throw new AigOneException("No Appointments found for this appointment id: " + appointmentId);
		}
		PateintAppointmentsDto pateintAppointmentsDto = appointments.get(0);
		DeleteAppointmentRequestDto req = new DeleteAppointmentRequestDto();
		req.setAppointmentDeleteDatetime(new Date());
		req.setAppointmentID(appointmentId);
		req.setPatientID(pateintAppointmentsDto.getUhid());
		req.setDcotorCode(pateintAppointmentsDto.getDoctorId());
		req.setSavedBy(userName);
		req.setRemarks("Deleted By " + userName);
		req.setHospCode(pateintAppointmentsDto.getBranchLocationId());

		HisLoginResponseDto login = self.login();
		return hisClient.post().uri(HisApiUtil.DELETE_PATIENT_APPOINTMENT_WITH_DOCTOR)
				.contentType(MediaType.APPLICATION_JSON).header("Authorization", "Bearer " + login.getAccess_token())
				.body(BodyInserters.fromValue(req)).retrieve().bodyToMono(String.class).block();
	}

	@Override
	public List<String> fetchSpecialities() {
		return EmployeeRosterRepo.findDistinctSubDepartments();
	}

	@Override
	public List<ProcedureGuideDto> fetchProcedureGuide() {
		List<ProcedureEntity> procedureEntities = procedureInfoRepo.findAllByOrderByProcedureNameAsc();

		return procedureEntities.stream().map(entity -> {
			ProcedureGuideDto dto = aigOneMapper.toProcedureGuideDto(entity);
			List<ProcedureInfoGuideDto> userGuideDtos = entity.getInformationGuide().stream()
					.map(ig -> aigOneMapper.toProcedureInfoDto(ig))
					.sorted(Comparator.comparingInt(ProcedureInfoGuideDto::getOrder)).toList();
			dto.setUserGuide(userGuideDtos);
			return dto;
		}).collect(Collectors.toList());
	}

	@Override
	public List<PateintAppointmentsDto> fecthAppointmentByAppointmentId(Long appointmentId) {
		List<PateintAppointmentsDto> appointments = hsDbRepo.fecthAppointmentByAppointmentId(appointmentId);
		if (CollectionUtils.isEmpty(appointments)) {
			throw new AigOneException("No Appointments found for this appointment id: " + appointmentId);
		}
		return appointments;
	}

	@Override
	public List<AigAssistantEntity> fetchAigAssistantDetails(String type) {
		return aigAssistantRepo.findByAssistantTypeOrderByPriorityAsc(AigAssistantTypeEnum.valueOf(type));
	}

	@Override
	public List<ChecklistTemplatesDto> createChecklist(ChecklistTemplateDetailsDto createTempDto, long phoneNo) {
		ChecklistTemplateEntity checklistEntity = new ChecklistTemplateEntity();
		checklistEntity.setName(createTempDto.getName());
		checklistEntity.setStatus(StatusEnum.ACTIVE);
		checklistEntity.setDescription(createTempDto.getDescription());

		List<ChecklistFieldEntity> checklistFields = createTempDto.getFields().stream().map(fieldDto -> {
			ChecklistFieldEntity checklistField = new ChecklistFieldEntity();
			checklistField.setCheckList(checklistEntity);
			checklistField.setFieldName(fieldDto.getFieldName());
			checklistField.setMandatory(fieldDto.isMandatory());
			checklistField.setDescription(fieldDto.getDescription());
			List<ChecklistFieldOptionsEntity> ctFieldOptions = fieldDto.getOptions().stream().map(optionDto -> {
				LookupOptionsMasterEntity fieldOption = lookupOptionsRepo.findById(optionDto.getOptionId())
						.orElseThrow(() -> new RuntimeException("Master Option not found"));
				ChecklistFieldOptionsEntity fieldOptions = new ChecklistFieldOptionsEntity();
				fieldOptions.setChecklistField(checklistField);
				fieldOptions.setFieldOption(fieldOption);
				fieldOptions.setLookupCode(optionDto.getLookupCode());
				fieldOptions.setValidationType(optionDto.getValidationType());
				fieldOptions.setDescription(optionDto.getDescription());
				return fieldOptions;
			}).collect(Collectors.toList());

			checklistField.setFieldOptions(ctFieldOptions);
			return checklistField;
		}).collect(Collectors.toList());

		checklistEntity.setFields(checklistFields);
		ctRepo.save(checklistEntity);
		return fetchChecklistTemplates(phoneNo);

	}

	@Override
	public List<ChecklistSubmittedEntity> submitChecklist(CTSubmitRequestDto ctSubmitDto, Long phoneNo) {
		UserEntity user = userRepo.findByPhoneNoAndActiveTrue(phoneNo)
				.orElseThrow(() -> new EntityNotFoundException("User Not Found with Phone Number: " + phoneNo));
		ChecklistSubmittedEntity CTSubmitted;
		if (ctSubmitDto.getChecklistSubmittedId() == null) {
			ChecklistTemplateEntity checklist = ctRepo.findById((long) ctSubmitDto.getTemplateId())
					.orElseThrow(() -> new RuntimeException("Template not found"));
			CTSubmitted = new ChecklistSubmittedEntity();
			CTSubmitted.setChecklist(checklist);
			CTSubmitted.setCreatedUser(user);
			CTSubmitted.setId(ctSubmittedRepo.save(CTSubmitted).getId());
		} else {
			CTSubmitted = ctSubmittedRepo.findById(ctSubmitDto.getChecklistSubmittedId())
					.orElseThrow(() -> new EntityNotFoundException(
							"Checklist Submission not found with ID: " + ctSubmitDto.getChecklistSubmittedId()));
		}
//		CTSubmitted.setStatus(ctSubmitDto.isSubmitted()? ChecklistSubmittedStatusEnum.SUBMITTED: ChecklistSubmittedStatusEnum.PARTIALLY_SUBMITTED);
		CTSubmitted.setStatus((ctSubmitDto.getStatus() != null && ctSubmitDto.getStatus().equals("SUBMITTED"))
				? ChecklistSubmittedStatusEnum.SUBMITTED
				: ChecklistSubmittedStatusEnum.PARTIALLY_SUBMITTED);
		List<ChecklistSubmittedFieldOptionValuesEntity> CTSubmittedFields = ctSubmitDto.getFields().stream()
				.flatMap(fieldDto -> fieldDto.getOptions().stream().map(optionDto -> {
					ChecklistFieldEntity ctField = ctFieldRepo.findById((long) fieldDto.getFieldId())
							.orElseThrow(() -> new RuntimeException("Field not found"));
					LookupOptionsMasterEntity ctOption = lookupOptionsRepo.findById((long) optionDto.getOptionId())
							.orElseThrow(() -> new RuntimeException("Master Option not found"));
					ChecklistSubmittedFieldOptionValuesEntity checklistField;
					checklistField = ctSubmittedFieldsRepo.findByCheckListSubmitted_IdAndField_IdAndOption_Id(
							CTSubmitted.getId(), ctField.getId(), ctOption.getId());
					if (checklistField == null) {
						checklistField = new ChecklistSubmittedFieldOptionValuesEntity();
						checklistField.setCheckListSubmitted(CTSubmitted);
						checklistField.setValue(optionDto.getValue());
						checklistField.setField(ctField);
						checklistField.setOption(ctOption);
					}
					checklistField.setCheckListSubmitted(CTSubmitted);
					if (ctOption.getName().equals("IMAGE")) {
						String[] split = optionDto.getFileName().split("\\.");
						String fileName = CTSubmitted.getId() + "_" + ctField.getFieldName() + "."
								+ split[split.length - 1];
						fileUploadService.upload(optionDto.getValue(), fileName, "checklists");
						checklistField.setValue(fileName);
					} else {
						checklistField.setValue(optionDto.getValue());
					}
					checklistField.setField(ctField);
					checklistField.setOption(ctOption);
					return checklistField;
				})).collect(Collectors.toList());

		CTSubmitted.setFields(CTSubmittedFields);
		ctSubmittedRepo.save(CTSubmitted);
		return Collections.singletonList(CTSubmitted);

	}

	@Override
	public List<LookupOptionsMasterEntity> fetchLookupOptions() {
		return lookupOptionsRepo.findAllByStatus(StatusEnum.ACTIVE);
	}

	@Override
	public List<LookupValuesMasterEntity> fetchLookupValues(String type) {
		return lookupValuesRepo.findAllByStatusAndType(StatusEnum.ACTIVE, type);
	}

	@Override
	public List<ChecklistTemplatesDto> fetchChecklistTemplates(long phoneNo) {
		UserEntity user = userRepo.findByPhoneNoAndActiveTrue(phoneNo)
				.orElseThrow(() -> new EntityNotFoundException("User Not Found with Phone Number: " + phoneNo));
		List<ChecklistTemplateEntity> entities = user.getRoles().stream().flatMap(role -> role.getTemplates().stream())
				.collect(Collectors.toList());
		if (entities.isEmpty()) {
			entities = ctRepo.findAll();
		}
		return entities.stream().filter(template -> StatusEnum.ACTIVE.equals(template.getStatus()))
				.map(aigOneMapper::toChecklistTemplatesDto).collect(Collectors.toList());
	}

	@Override
	public String uploadDoc(UploadRequest request) {
		log.info("upload 1");

//		PatientEntity patient = patientRepo.findByUhId(request.getPatientID()).orElseThrow(
//				() -> new EntityNotFoundException("Patient not found with the UHID: " + request.getPatientID()));
//		log.info("patient ");

		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		SystemSettingsEntity validFileExtensions = systemRepo.findByKey("VALID_FILE_EXTENSIONS");
		String values = validFileExtensions.getValue();
		String fileExtension = request.getFileNamewithExtn()
				.substring(request.getFileNamewithExtn().lastIndexOf('.') + 1);
		if (!(values.contains(fileExtension))) {
			throw new AigOneException("The " + fileExtension + " file type is not supported");
		}
		HisLoginResponseDto login = self.login();
		request.setSavedBy(userName);
		request.setDocumentSaveDatetime(new Date());
		request.setHospCode(request.getHospCode());
		String hisResponse = hisClient.post().uri(HisApiUtil.SAVE_PATIENT_DOCUMENTS)
				.contentType(MediaType.APPLICATION_JSON).header("Authorization", "Bearer " + login.getAccess_token())
				.body(BodyInserters.fromValue(request)).retrieve().bodyToMono(String.class).block();

		log.info("HIS Document Upload Response::" + hisResponse);

		SystemSettingsEntity emrAPiKey = systemRepo.findByKey("EMR_API_KEY");

		String fileNamewithExtn = request.getFileNamewithExtn();

		String[] split = fileNamewithExtn.split("\\.");
		String fileExt = split[split.length - 1];
		String fileFormat = null;
		String contentType=null;
		if (fileExt != null && (fileExt.contains("pdf") || fileExt.contains("PDF"))) {
			contentType= "pdf";
			fileFormat = "PDF";
		} else {
			contentType= "image/"+fileExt;
			fileFormat = "IMAGE";
		}

		EmrUploadRequest emrUploadRequest = new EmrUploadRequest();
		emrUploadRequest.setPatientId(request.getPatientID());
		emrUploadRequest.setDocumentId(AigOneUtil.generateTransactionId(10));
		emrUploadRequest.setDocumentName(request.getFileNamewithExtn());
		emrUploadRequest.setDocumentType(request.getDocumentCategory());
		emrUploadRequest.setDocumentSource("PATIENT");
		emrUploadRequest.setDocumentBase64(request.getFileinBase64());
		emrUploadRequest.setDocumentContentType(contentType);
		emrUploadRequest.setPractAccessYn("Y");
		emrUploadRequest.setAddedBy(currentUser.getName());
		emrUploadRequest.setAddedDateTime(DateUtil.format(new Date(), DateUtil.FORMAT_YYYY_MM_DD_HH_MM_SS));
		emrUploadRequest.setStatus("E");
		emrUploadRequest.setDocumentFormat(fileFormat);

//		try {
//			ObjectMapper objectMapper = new ObjectMapper();
//			//log.info("EMR Document Upload Request::"+objectMapper.writeValueAsString(emrUploadRequest));
//		} catch (JsonProcessingException e) {
//			e.printStackTrace();
//		}

		String emrResponse = emrClient.post().uri(HisApiUtil.EMR_UPLOAD_DOC).contentType(MediaType.APPLICATION_JSON)
				.header("hpApp-Token", emrAPiKey.getValue()).body(BodyInserters.fromValue(emrUploadRequest)).retrieve()
				.bodyToMono(String.class).block();

		log.info("EMR Document Upload Response::" + emrResponse);

		return hisResponse;
	}
	
	@Override
	public HisResponseListDto<PatientReportsResponseDto> fetchPatientReportsWithoutBlocking(PatientReportsRequestDto reportDto) {
//		log.info(reportDto.toString());
	    return Mono.fromCallable(() -> {
	        HisLoginResponseDto login = self.login();	        
	        return hisClient.post()
	                .uri(HisApiUtil.GET_ALL_PATIENT_REPORTS)
	                .contentType(MediaType.APPLICATION_JSON)
	                .header("Authorization", "Bearer " + login.getAccess_token())
	                .body(BodyInserters.fromValue(reportDto))
	                .retrieve()
	                .bodyToMono(new ParameterizedTypeReference<HisResponseListDto<PatientReportsResponseDto>>() {})
	                .block();
	    }).subscribeOn(Schedulers.boundedElastic()).block();
	}
	


	@Override
	public HisResponseListDto<PatientReportsResponseDto> fetchPatientReports(String type, String uhId) {
		HisLoginResponseDto login = self.login();
		PatientReportsRequestDto reportDto = new PatientReportsRequestDto();
		reportDto.setHospCode("1100");
		reportDto.setType(type);
		reportDto.setPatientID(uhId);
		HisResponseListDto<PatientReportsResponseDto> reportsJson = hisClient.post()
				.uri(HisApiUtil.GET_ALL_PATIENT_REPORTS).contentType(MediaType.APPLICATION_JSON)
				.header("Authorization", "Bearer " + login.getAccess_token()).body(BodyInserters.fromValue(reportDto))
				.retrieve().bodyToMono(new ParameterizedTypeReference<HisResponseListDto<PatientReportsResponseDto>>() {
				}).block();
		if (!reportsJson.getResponse().isEmpty()) {
			List<Integer> ipOrderIds = new ArrayList<>();
			List<Integer> ipTestIds = new ArrayList<>();
			List<Integer> opOrderIds = new ArrayList<>();
			List<Integer> opTestIds = new ArrayList<>();
			List<Integer> erOrderIds = new ArrayList<>();
			List<Integer> erTestIds = new ArrayList<>();
			for (PatientReportsResponseDto report : reportsJson.getResponse()) {
				if (report.getPatType() == 4 || report.getPatType() == 5 || report.getOrderNo().startsWith("AESC")) {
				    erOrderIds.add(report.getOrderID());
				    erTestIds.add(report.getTestID());
				}else if (report.getPatType() == 9 || report.getPatType() == 1) {
				    ipOrderIds.add(report.getOrderID());
				    ipTestIds.add(report.getTestID());
				} else {
				    opOrderIds.add(report.getOrderID());
				    opTestIds.add(report.getTestID());
				}
			}
			List<PatientReportSummaryDto> allReportDetails = new ArrayList<>();
			if (!ipOrderIds.isEmpty()) {
				allReportDetails.addAll(hsDbRepo.fetchPatientReportDetails(ipOrderIds, ipTestIds, "IP"));
			}
			if (!opOrderIds.isEmpty()) {
				allReportDetails.addAll(hsDbRepo.fetchPatientReportDetails(opOrderIds, opTestIds, "OP"));
			}
			if (!erOrderIds.isEmpty()) {
				allReportDetails.addAll(hsDbRepo.fetchPatientReportDetails(erOrderIds, erTestIds, "ER"));
			}
			Set<Integer> uniqueTestIds = new LinkedHashSet<>(ipTestIds);
			uniqueTestIds.addAll(opTestIds);
			uniqueTestIds.addAll(erTestIds);
			List<Integer> allTestIds = new ArrayList<>(uniqueTestIds);
			List<LabReportTestEntity> configTestDetails = labReportTestRepo.findByTestIdIn(allTestIds);
			Map<Integer, LabReportTestEntity> testIdToEntity = configTestDetails.stream()
				    .filter(e ->e.getTestViewType() != null)
				    .collect(Collectors.toMap(
				    		e -> (Integer) e.getTestId(),
				            e -> e,
				        (v1, v2) -> v1 
				    ));
			reportsJson.getResponse().forEach(report -> {
				report.setType(type);
				LabReportTestEntity entity = testIdToEntity.get(report.getTestID());
				if (entity != null) {
			        report.setTestViewType(entity.getTestViewType() != null ? entity.getTestViewType().name() : "PDF");
			        report.setImage(entity.getImage());
			        report.setOutSourceReport(entity.isOutSourceReport());
				}
				List<PatientReportSummaryDto> details = allReportDetails.stream()
						.filter(d -> d.getOrderId() == report.getOrderID() && d.getTestId() == report.getTestID())
						.collect(Collectors.toList());
				if (details.size() > 0) {
					report.setSampleCollDateTime(details.get(0).getSampleCollDateTime());
					PatientReportSummaryDto patientReportSummaryDto = details.get(0);
					report.setSampleCollDateTime(patientReportSummaryDto.getSampleCollDateTime());
					report.setSampleVerifiedDateTime(patientReportSummaryDto.getSampleVerifiedDateTime());
					
					Set<Integer> allowedResultTypes = Set.of(2, 3, 4);
					report.setReportDetails(
							details.stream().filter(d -> (
									d.getCondition() != null 
									&& !d.getCondition().trim().isEmpty() 
									&& !d.getCondition().equals("N")
									&& allowedResultTypes.contains(d.getResultTypeId())))
									.collect(Collectors.toList()));
				}
			});
		}
		return reportsJson;

	}

	@Override
	public String fetchBase64Report(ViewReportRequestDto viewReportDto) {
		return fetchReportLink(viewReportDto).getBase64();
	}

	private PatientReportDetailsDto fetchReportLink(ViewReportRequestDto viewReportDto) {
		PatientReportDetailsDto reportDetailsDto = new PatientReportDetailsDto();

		HisLoginResponseDto login = self.login();
		viewReportDto.setHospCode("1100");
//		viewReportDto.setShowlogo(true);
		ViewReportHisResponseDto<ViewReportResponseDto> reportResponse = hisClient.post()
				.uri(HisApiUtil.GET_BASE64_REPORT).contentType(MediaType.APPLICATION_JSON)
				.header("Authorization", "Bearer " + login.getAccess_token())
				.body(BodyInserters.fromValue(viewReportDto)).retrieve()
				.bodyToMono(new ParameterizedTypeReference<ViewReportHisResponseDto<ViewReportResponseDto>>() {
				}).block();

		if (reportResponse.getResponse() != null) {
			log.info("Base64 report Response report Link::" + reportResponse.getResponse().getReportLink());
		}

		String base64Report = "";
		if (reportResponse.isSucceeded() == false) {
			base64Report = "";
		} else {
			if (reportResponse.getResponse().getReportBase64() != "") {
				base64Report = reportResponse.getResponse().getReportBase64();
				reportDetailsDto.setBase64(base64Report);
			} else if (reportResponse.getResponse().getReportLink() != "") {
				String reportLink = reportResponse.getResponse().getReportLink();
				base64Report = fetchBase64FromUrl(reportLink);
				reportDetailsDto.setBase64(base64Report);
				if (reportLink != null) {
					String accessionnumber = StringUtils.substringBetween(reportLink,
							"http://risweb:84/SHARED/ReportFilePath/", ".pdf");
					reportDetailsDto.setPacsLink(
							"http://aigpacs/explore.asp?path=All%20Studies/accessionnumber=" + accessionnumber);
				}

//					Map<String, String> queryParams = new HashMap<>();
//					try {
//						URI uri = new URI(reportLink);
//						String[] pairs = uri.getQuery().split("&");
				//
//						for (String pair : pairs) {
//							String[] keyValue = pair.split("=");
//							queryParams.put(keyValue[0], keyValue[1]);
//						}
//						base64Report = fetchRisReport(queryParams.get("token"),queryParams.get("accessionNo"));
//					} catch (Exception e) {
//						e.printStackTrace();
//					}

			}
		}
		return reportDetailsDto;
	}

	private String fetchRisReport(String token, String accessionNo) throws Exception {
		String base64Report = "";
		String body = "{ \"mode\": \"21\", \"dsData\": { \"dtCriteria\": [ { \"AccessionNo\": \"" + accessionNo
				+ "\", \"Token\": \"" + token + "\" } ] } }";
		String resJson = risClient.post().uri(HisApiUtil.GET_RISORDER_ID).contentType(MediaType.APPLICATION_JSON)
				.body(BodyInserters.fromValue(body)).retrieve().bodyToMono(String.class).block();
		if (resJson != null) {
			ObjectMapper objectMapper = new ObjectMapper();
			JsonNode data = objectMapper.readTree(resJson);

			if (data.has("StatusCode") && data.get("StatusCode").asInt() == 200) {
				String risOrderId = data.get("Result").get(0).get("RisOrderId").asText();

				body = "{ \"mode\": \"39\", \"dsData\": { \"dtCriteria\": [ { \"RisOrderId\": \"" + risOrderId
						+ "\" } ] } }";
				String risReportJson = risClient.post().uri(HisApiUtil.GET_RISORDER_REPORT)
						.contentType(MediaType.APPLICATION_JSON).body(BodyInserters.fromValue(body)).retrieve()
						.bodyToMono(String.class).block();

				if (risReportJson != null) {
					JsonNode risReportData = objectMapper.readTree(risReportJson);

					if (risReportData.has("StatusCode") && risReportData.get("StatusCode").asInt() == 200) {
						base64Report = risReportData.get("Result").get(0).get("ImageUrlpath").asText();
					}
				}
			}
		}
		return base64Report;
	}

	@Override
	public List<PatientDocumentsDto> fetchPatientDocuments(String uhId) {
		return hsDbRepo.fetchPatientDocuments(uhId, userName);
	}

	@Override
	public List<PatientDocumentsDto> fetchPatientConsultations(String uhId) {
		return hsDbRepo.fetchPatientConsultations(uhId, userName);
	}

	@Override
	public String fetchBase64Document(PatientDocumentsDto patientDocDto) {
		String folderPath = "/" + patientDocDto.getUhId() + "/" + patientDocDto.getDocumentId() + "."
				+ patientDocDto.getDocumentTypeId() + patientDocDto.getDocumentExtension();
		String documentUrl = hisDocumentsBaseUrl + folderPath;
		String base6Report = fetchBase64FromUrl(documentUrl);
		return base6Report;

	}

	@Override
	public CTFieldDto fetchChecklistFieldDetails(int templateId, int fieldId) {
		ChecklistFieldEntity fieldEntity = ctFieldRepo.findByIdAndCheckList_Id(Long.valueOf(fieldId),
				Long.valueOf(templateId));
		if (fieldEntity != null) {
			CTFieldDto dto = aigOneMapper.toCTFieldDto(fieldEntity);
			List<LookupOptionsDto> options = fieldEntity.getFieldOptions().stream().map(option -> {
				LookupOptionsDto lookupOptionsDto = new LookupOptionsDto();
				lookupOptionsDto.setOptionId(option.getFieldOption().getId());
				lookupOptionsDto.setOptionName(option.getFieldOption().getName());
				lookupOptionsDto.setLookupCode(option.getLookupCode());
				lookupOptionsDto.setValidationType(option.getValidationType());
				lookupOptionsDto.setDescription(option.getDescription());
				if (option.getLookupCode() != null) {
					List<LookupValuesMasterEntity> values = lookupValuesRepo.findAllByStatusAndType(StatusEnum.ACTIVE,
							option.getLookupCode());
					if (!CollectionUtils.isEmpty(values)) {
						List<String> list = values.stream().map(lv -> lv.getName()).toList();
						lookupOptionsDto.setLookupValues(list);
					}
				}
				return lookupOptionsDto;
			}).collect(Collectors.toList());

			dto.setOptions(options);
			return dto;
		} else {
			throw new AigOneException("Template Field not found");
		}
	}

	@Override
	public ChecklistTemplateDetailsDto fetchChecklistTemplateDetails(int templateId, long phoneNo) {
		UserEntity user = userRepo.findByPhoneNoAndActiveTrue(phoneNo)
				.orElseThrow(() -> new EntityNotFoundException("User Not Found with Phone Number: " + phoneNo));
		ChecklistTemplateEntity checklistEntity = ctRepo.findById(Long.valueOf(templateId))
				.orElseThrow(() -> new AigOneException("ChecklistTemplateEntity not found for id: " + templateId));
		ChecklistTemplateDetailsDto dto = aigOneMapper.toChecklistTemplateDetailsDto(checklistEntity);

		ChecklistSubmittedEntity submittedEntity = ctSubmittedRepo
				.findTopByStatusAndChecklist_IdAndCreatedUser_IdOrderByCreationDateDesc(
						ChecklistSubmittedStatusEnum.PARTIALLY_SUBMITTED, checklistEntity.getId(), user.getId());
		dto.setChecklistSubmittedId(submittedEntity != null ? submittedEntity.getId() : null);

		return setupTemplateDetails(submittedEntity, checklistEntity, dto);

	}

	@Override
	public List<ChecklistTemplateDetailsDto> fetchSubmittedChecklists(long phoneNo) {
		UserEntity user = userRepo.findByPhoneNoAndActiveTrue(phoneNo)
				.orElseThrow(() -> new EntityNotFoundException("User Not Found with Phone Number: " + phoneNo));
		List<String> userRoles = user.getRoles().stream().map(RoleEntity::getCode).collect(Collectors.toList());
		List<Long> templateIds = user.getRoles().stream().flatMap(role -> role.getTemplates().stream())
				.map(ChecklistTemplateEntity::getId).collect(Collectors.toList());
		boolean supervisor = (userRoles.contains("HOUSE_KEEPING_SUPERVISOR") ? true : false);
		List<ChecklistSubmittedEntity> submittedEntities;
		if (supervisor) {
			submittedEntities = ctSubmittedRepo.findByChecklist_IdInAndStatusNot(templateIds,
					ChecklistSubmittedStatusEnum.PARTIALLY_SUBMITTED);
		} else {
			submittedEntities = ctSubmittedRepo
					.findByStatusNotAndCreatedUser_Id(ChecklistSubmittedStatusEnum.PARTIALLY_SUBMITTED, user.getId());
		}
		if (CollectionUtils.isEmpty(submittedEntities)) {
			return Collections.emptyList();
		} else {
			return submittedEntities.stream().map(submittedEntity -> {
				ChecklistTemplateEntity checklistEntity = submittedEntity.getChecklist();
				ChecklistTemplateDetailsDto dto = aigOneMapper.toChecklistTemplateDetailsDto(checklistEntity);
				dto.setChecklistStatus(submittedEntity.getStatus());
				dto.setSupervisor(supervisor);
				dto.setSubmittedDate(submittedEntity.getCreationDate());
				dto.setChecklistSubmittedId(submittedEntity.getId());
				dto.setSubmittedUser(aigOneMapper.toUserDto(submittedEntity.getCreatedUser()));
				dto.setReviewUser(submittedEntity.getReviewUser() != null
						? aigOneMapper.toUserDto(submittedEntity.getCreatedUser())
						: null);
				return setupTemplateDetails(submittedEntity, checklistEntity, dto);
			}).collect(Collectors.toList());
		}
	}

	private ChecklistTemplateDetailsDto setupTemplateDetails(ChecklistSubmittedEntity submittedEntity,
			ChecklistTemplateEntity checklistEntity, ChecklistTemplateDetailsDto dto) {
		checklistEntity.getFields().forEach(fieldEntity -> {
			List<LookupOptionsDto> options = fieldEntity.getFieldOptions().stream().map(optionEntity -> {
				LookupOptionsDto optionDto = new LookupOptionsDto();
				optionDto.setOptionId(optionEntity.getFieldOption().getId());
				optionDto.setOptionName(optionEntity.getFieldOption().getName());
				optionDto.setLookupCode(optionEntity.getLookupCode());
				optionDto.setValidationType(optionEntity.getValidationType());
				optionDto.setDescription(optionEntity.getDescription());
				if (optionEntity.getLookupCode() != null) {
					List<LookupValuesMasterEntity> fetchLookupValues = fetchLookupValues(optionEntity.getLookupCode());
					if (!CollectionUtils.isEmpty(fetchLookupValues)) {
						List<String> list = fetchLookupValues.stream().map(lv -> lv.getName()).toList();
						optionDto.setLookupValues(list);
					}
				}

//                if(submittedEntity != null) {
//                	ChecklistSubmittedFieldOptionValuesEntity ctSubmittedFieldEntity = ctSubmittedFieldsRepo.findByCheckListSubmitted_IdAndField_IdAndOption_Id(submittedEntity.getId(),fieldEntity.getId(),optionEntity.getFieldOption().getId());
//                	optionDto.setAnswer(ctSubmittedFieldEntity != null? ctSubmittedFieldEntity.getValue():null);
//                }

				if (submittedEntity != null) {
					ChecklistSubmittedFieldOptionValuesEntity ctSubmittedFieldEntity = ctSubmittedFieldsRepo
							.findByCheckListSubmitted_IdAndField_IdAndOption_Id(submittedEntity.getId(),
									fieldEntity.getId(), optionEntity.getFieldOption().getId());
					if (ctSubmittedFieldEntity != null
							&& ctSubmittedFieldEntity.getOption().getName().equals("IMAGE")) {
						String download = fileUploadService.download(ctSubmittedFieldEntity.getValue());
						optionDto.setAnswer(download);
					} else {
						optionDto.setAnswer(ctSubmittedFieldEntity != null ? ctSubmittedFieldEntity.getValue() : null);
					}
				}
				if (optionDto.getAnswer() != null && !optionDto.getAnswer().isEmpty()) {
					optionDto.setFieldStatus(!optionDto.getOptionName().equals("RADIO") ? CTFieldOptionTypeEnum.GREEN
							: optionDto.getAnswer().equalsIgnoreCase("Yes") ? CTFieldOptionTypeEnum.GREEN
									: CTFieldOptionTypeEnum.RED);
				} else {
					optionDto.setFieldStatus(CTFieldOptionTypeEnum.GREY);
				}

				return optionDto;
			}).collect(Collectors.toList());

			dto.getFields().stream().filter(fieldDto -> fieldDto.getId() == fieldEntity.getId()).findFirst()
					.ifPresent(fieldDto -> {
						fieldDto.setFieldStatus(fieldDto.getFieldStatus());
						fieldDto.setOptions(options);
					});
		});
		dto.setFields(dto.getFields().stream().sorted(Comparator.comparingLong(CTFieldDto::getId))
				.collect(Collectors.toList()));

		return dto;
	}

	@Override
	public String fetchUserTokenEndoscopy(String uhId) {
		SystemSettingsEntity systemProperties = systemRepo.findByKey("ENDOSCOPY_BASEURL");
		String graphQLUrl = systemProperties.getValue();
		RestTemplate restTemplate = new RestTemplate();
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set("device_type", "STAFF");
		String query = "query GetUserToken($uhid: String!) {\n" + "  getUserToken(uhid: $uhid) {\n" + "    data {\n"
				+ "            id\n" + "            userQueue {\n" + "                status\n"
				+ "                endTime\n" + "                steps {\n" + "                    id\n"
				+ "                    name\n" + "                    code\n" + "                    createdAt\n"
				+ "                    completedAt\n" + "                    status\n"
				+ "                    avgStepTime\n" + "                }\n" + "                queueName\n"
				+ "                tokenNo\n" + "                counterName\n" + "                stepStartTime\n"
				+ "                createdAt\n" + "            }\n" + "            tokenNo\n" + "            umrNo\n"
				+ "            userName\n" + "			   services{\n" + "                 serviceName\n"
				+ "             }\n" + "        }\n" + "    success\n" + "    message\n" + "  }\n" + "}";

		Map<String, Object> requestBody = new HashMap<>();
		requestBody.put("query", query);
		Map<String, Object> variables = new HashMap<>();
		variables.put("uhid", uhId);

		requestBody.put("variables", variables);
		HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

		try {
			ResponseEntity<String> response = restTemplate.postForEntity(graphQLUrl, entity, String.class);
			return response.getBody().toString();
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}

	}
	

	@Override
	public String reviewChecklist(String checklistSubmittedId, String status, long phoneNo) {
		ChecklistSubmittedEntity entity = ctSubmittedRepo.findById(Long.valueOf(checklistSubmittedId))
				.orElseThrow(() -> new AigOneException("Entity not found for id: " + checklistSubmittedId));
		UserEntity user = userRepo.findByPhoneNoAndActiveTrue(phoneNo)
				.orElseThrow(() -> new EntityNotFoundException("User Not Found with Phone Number: " + phoneNo));
		entity.setStatus(ChecklistSubmittedStatusEnum.valueOf(status));
		entity.setReviewUser(user);
		ctSubmittedRepo.save(entity);
		return "Checklist Reviewed Successfully";
	}

	@Override
	public List<PatientAttendantDto> savePatientAttendant(PatientAttendantDto attendantDto) {
		PatientEntity patient = patientRepo.findByUhId(attendantDto.getUhId()).orElseThrow(
				() -> new EntityNotFoundException("Patient not found with the UHID: " + attendantDto.getUhId()));
		if (attendantDto.getPhoneNo().equals(patient.getPhoneNo())) {
			throw new AigOneException("Attendant and Patient numbers can't be same. Please provide another.");
		}
		PatientAttendantEntity attendant = new PatientAttendantEntity();
		attendant.setName(attendantDto.getName());
		attendant.setPhoneNo(attendantDto.getPhoneNo());
		attendant.setRelation(attendantDto.getRelation());
		attendant.setPatient(patient);
		patientAttendantRepo.save(attendant);
		smsService.sendSms(String.valueOf(attendantDto.getPhoneNo()),
				String.valueOf(RequestedFromEnum.PATIENT_ATTENDANT), null, patient.getName(), patient.getUhId());
		return fetchPatientAttendants(attendantDto.getUhId());
	}

	@Override
	public List<PatientAttendantDto> fetchPatientAttendants(String uhId) {
		PatientEntity patient = patientRepo.findByUhId(uhId)
				.orElseThrow(() -> new EntityNotFoundException("Patient not found with the UHID: " + uhId));
		return patient.getAttendants().stream().map(aigOneMapper::toPatientAttendantDto).toList();
	}

	@Override
	public String deletePatientAttendant(Long attendantId) {
		PatientAttendantEntity attendant = patientAttendantRepo.findById(attendantId)
				.orElseThrow(() -> new EntityNotFoundException("Attendant not found"));
		patientAttendantRepo.delete(attendant);
		return "Attendant Removed Succesfully";
	}

	@Override
	public HisResponseListDto fetchAttendantPatients(Long phoneNo) {
		List<PatientAttendantEntity> attendants = patientAttendantRepo.findByPhoneNo(phoneNo);
		HisResponseListDto responseDto = new HisResponseListDto();
		if (!CollectionUtils.isEmpty(attendants)) {
			List<PatientEntity> patients = attendants.stream().map(a -> a.getPatient()).distinct().toList();
			List<PatientDetailsDto> patientDto = patients.stream().map(p -> {
				PatientDetailsDto dto = aigOneMapper.toPatientDetailsDto(p);
				dto.setAttendantName(attendants.get(0).getName());
				dto.setAttendantPhoneNo(attendants.get(0).getPhoneNo());
				dto.setAttendantRelation(attendants.get(0).getRelation());
				return dto;
			}).toList();
			responseDto.setResponse(patientDto);
			responseDto.setMessage("Success");
			responseDto.setResult("Success");
			responseDto.setSucceeded(true);
		} else {
			responseDto.setResponse(Collections.emptyList());
			responseDto.setMessage("No Patients Found");
			responseDto.setResult("Success");
			responseDto.setSucceeded(true);
		}
		return responseDto;
	}

	@Override
	public String uploadRoster(MultipartFile file) {
		String fileExtension = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf('.') + 1);
		if (!(Arrays.asList("xlsx", "xls").contains(fileExtension.toLowerCase()))) {
			throw new AigOneException(
					"Please provide valid excel file " + fileExtension.toUpperCase() + " is not supported");
		}
		try {
			Workbook workbook = new XSSFWorkbook(file.getInputStream());
			Sheet sheet = workbook.getSheetAt(0);
			Iterator<Row> rows = sheet.iterator();

			// Skip the header row
			if (rows.hasNext()) {
				rows.next();
			}

			while (rows.hasNext()) {
				Row currentRow = rows.next();
				Cell dateCell = currentRow.getCell(0);
				Cell departmentCell = currentRow.getCell(1);
				Cell subDepCell = currentRow.getCell(2);
				Cell dayEmpCell = currentRow.getCell(3);
				Cell dayPhoneNoCell = currentRow.getCell(4);
				Cell nightEmpCell = currentRow.getCell(5);
				Cell nightPhoneNoCell = currentRow.getCell(6);
				Cell typeCell = currentRow.getCell(7);
				Date rosterDate = dateCell.getDateCellValue();
				String departmentCellValue = departmentCell.getStringCellValue().strip();
				departmentCellValue = (departmentCellValue.toLowerCase().equals("er")
						|| departmentCellValue.toLowerCase().equals("emergency")) ? "EMERGENCY"
								: departmentCell.getStringCellValue().strip();
				EmployeeRosterDepartmentEnum rosterDep = EmployeeRosterDepartmentEnum
						.valueOf(departmentCellValue.toUpperCase());
				String rosterSubDep = subDepCell.getStringCellValue().strip();
				if (rosterDate != null && rosterDep != null && !rosterSubDep.trim().isEmpty()) {
					EmployeeRosterEntity entity = EmployeeRosterRepo
							.findTopByDateAndDepartmentAndSubDepartmentOrderByPriorityDesc(rosterDate, rosterDep,
									rosterSubDep);
					String priority = "1";
					if (entity != null) {
						priority = String.valueOf(Integer.parseInt(entity.getPriority()) + 1);
					}
					EmployeeRosterEntity roster = new EmployeeRosterEntity();
					roster.setDate(rosterDate);
					roster.setDepartment(rosterDep);
					roster.setSubDepartment(rosterSubDep);
					if (dayEmpCell != null) {
						roster.setDayEmployee(
								dayEmpCell.getStringCellValue() != "" ? dayEmpCell.getStringCellValue().strip() : null);
						roster.setDayEmployeePhoneNo(saveRosterPhoneNo(dayPhoneNoCell));
					}
					if (nightEmpCell != null) {
						roster.setNightEmployee(
								nightEmpCell.getStringCellValue() != "" ? nightEmpCell.getStringCellValue().strip()
										: null);
						roster.setNightEmployeePhoneNo(saveRosterPhoneNo(nightPhoneNoCell));
					}
					roster.setEmployeeType(typeCell.getStringCellValue().strip());
					roster.setPriority(priority);
					EmployeeRosterRepo.save(roster);
				}
			}
			return "Roster data Updated Successfully";
		} catch (Exception e) {
			e.printStackTrace();
			throw new AigOneException("Failed to upload the file");
		}
	}

	private String saveRosterPhoneNo(Cell cellValue) {
		CellType PhoneNoType = cellValue.getCellType();
		String phoneNo = null;
		if (PhoneNoType != CellType.BLANK) {
			if (PhoneNoType == CellType.NUMERIC) {
				phoneNo = String.valueOf((long) cellValue.getNumericCellValue());
			} else {
				phoneNo = cellValue.getStringCellValue().strip();
			}
		}
		return phoneNo;
	}

	@Override
	public String patientRequestAppointment(PatientRequestAppointmentDto appointmentDto) {
		if (appointmentDto.getDoctorId() == 0
				&& (appointmentDto.getEmployeeId() == null || appointmentDto.getEmployeeId().isBlank())) {
			throw new AigOneException("Please provide valid DoctorId or EmployeeId");
		}
		Object[] doctor = hsDbRepo.fetchDoctor(appointmentDto.getDoctorId(), appointmentDto.getEmployeeId());
		PatientEntity patient = patientRepo.findByUhId(appointmentDto.getUhId())
				.orElseThrow(() -> new AigOneException("Patient Not Found"));
		PatientRequestAppointmentEntity entity = aigOneMapper.toPatientRequestAppointmentEntity(appointmentDto);
		entity.setStatus(PatientRequestAppointmentStatusEnum.PENDING);
		entity.setCreatedpatient(patient);
		entity.setDoctorId((int) doctor[0]);
		entity.setEmployeeId((String) doctor[1]);
		patientReqAppoRepo.save(entity);
		return "Appointment Requested Successfuly";
	}

	@Override
	public List<PatientRequestAppointmentDto> fetchDoctorRequestedAppointments(int doctorId, Date date) {
		List<PatientRequestAppointmentEntity> entites = patientReqAppoRepo.findByDoctorIdAndAppointmentDate(doctorId,
				date);
		return entites.stream().map(op -> {
			PatientRequestAppointmentDto dto = aigOneMapper.toPatientRequestAppointmentDto(op);
			List<PatientRequestAppointmentLogDto> dtoLogs = op.getLogs().stream().map(logs -> {
				PatientRequestAppointmentLogDto logDto = aigOneMapper.toPatientRequestAppointmentLogDto(logs);
				return logDto;
			}).toList();
			dto.setLogs(dtoLogs);
			return dto;
		}).toList();
	}

	@Override
	public String reviewRequestedAppointment(PatientRequestAppointmentLogDto dto, long phoneNo) {
		UserEntity user = userRepo.findByPhoneNoAndActiveTrue(phoneNo)
				.orElseThrow(() -> new EntityNotFoundException("User Not Found with Phone Number: " + phoneNo));
		PatientRequestAppointmentEntity entity = patientReqAppoRepo.findById(dto.getRequestAppointmentId())
				.orElseThrow(() -> new EntityNotFoundException("Record not found"));
		entity.setStatus(dto.getStatus());
		if (entity.getStatus() == PatientRequestAppointmentStatusEnum.APPROVED) {
			entity.setApprovedUser(user);
			entity.setApprovedTime(new Date());
		} else if (entity.getStatus() == PatientRequestAppointmentStatusEnum.REJECTED
				|| entity.getStatus() == PatientRequestAppointmentStatusEnum.CANCELLED) {
			entity.setRejectedUser(user);
			entity.setRejectedTime(new Date());
		}
		PatientRequestAppointmentLogEntity logEntity = new PatientRequestAppointmentLogEntity();
		logEntity.setStatus(dto.getStatus());
		logEntity.setRemarks(dto.getRemarks());
		logEntity.setPatientReqAppointment(entity);
		logEntity.setCreatedUser(user);
		entity.addLog(logEntity);
		patientReqAppoRepo.save(entity);
		return "Appointment Reviewed Successfully";
	}

	@Override
	public List<METPatientDto> fetchMETPatients(Integer mewsScore) {
		LocalDateTime startOfYesterday = LocalDate.now().minusDays(1).atStartOfDay();
		Date startOfYesterdayDate = Date.from(startOfYesterday.atZone(ZoneId.systemDefault()).toInstant());
		List<METPatientEntity> patients;
		patients = metPatientRepo.fetchAllMetPatients();

		if (patients.isEmpty()) {
			return Collections.emptyList();
		} else {
			return patients.stream().map(p -> {
				METPatientDto dto = aigOneMapper.toMETPatientDto(p);
				List<METPatientLogDto> logs = p.getLogs().stream().map(aigOneMapper::toMETPatientLogDto).toList();
				dto.setLogs(logs);
				return dto;
			}).toList();
		}

	}

	@Override
	public String addMETPatient(METPatientDto dto) {
		METPatientEntity metPatient = metPatientRepo
				.findTopByUhIdAndCompletedOrderByLatestStartDateTimeDesc(dto.getUhId(), false);
		if (metPatient != null) {
			throw new AigOneException("Patient Already Added");
		}
		System.out.println("1");
		METPatientDto patient = emrDbRepo.getMETPatient(dto.getUhId());
		System.out.println(patient);
		METPatientEntity entity = aigOneMapper.toMEDPatientEntity(patient);
		entity.setStatus(dto.getStatus());
		entity.setSentTo(dto.getSentTo());
		entity.setSentTime(new Date());
		entity.setComments(dto.getRemarks());
		metPatientRepo.save(entity);
		updateMETLogs(entity);
		return "Patient added";
	}

	@Override
	public String acknowledgePatientCondition(METPatientRequestDto dto) {
		METPatientEntity entity = metPatientRepo.findById(dto.getId())
				.orElseThrow(() -> new AigOneException("Record not found"));
		if (dto.getStatus().toString().equals(METPatientACKStatusEnum.ACKNOWLEDGED.name())) {
			entity.setAckBy(dto.getAckBy());
			entity.setAckTime(new Date());
		} else if (dto.getStatus().toString().equals(METPatientACKStatusEnum.COMPLETED.name())) {
			entity.setCompleted(true);
		}
		entity.setStatus(dto.getStatus());
		entity.setSentTo((dto.getSentTo() != null && dto.getSentTo() != "") ? dto.getSentTo() : entity.getSentTo());
		entity.setSentTime((dto.getSentTo() != null && dto.getSentTo() != "") ? new Date() : entity.getSentTime());
		entity.setPhysicalBedNo(
				(dto.getPhysicalBedNo() != null && dto.getPhysicalBedNo() != "") ? dto.getPhysicalBedNo()
						: entity.getPhysicalBedNo());
		entity.setComments(
				(dto.getComments() != null && dto.getComments() != "") ? dto.getComments() : entity.getComments());
		metPatientRepo.save(entity);
		updateMETLogs(entity);
		return "Patient Condition Acknowledged";
	}

	public void updateMETLogs(METPatientEntity entity) {
		METPatientLogEntity logEntity = new METPatientLogEntity();
		logEntity.setMETPatient(entity);
		logEntity.setCreatedTime(new Date());
		logEntity.setStatus(entity.getStatus());
		logEntity.setSentTo(entity.getSentTo());
		logEntity.setComments(entity.getComments());
		logEntity.setPhysicalBedNo(entity.getPhysicalBedNo());
		metPatientLogRepo.save(logEntity);
	}

	private void validatePass(PatientIPDetailsDto ipDetails, PatientVisitorPassDto visitorDto) {
		SystemSettingsEntity visitorTimings = systemRepo.findByKey("VISITOR_TIMINGS");
		SystemSettingsEntity visitsThreshold = systemRepo.findByKey("VISITS_THERSHOLD");
		ObjectMapper mapper = new ObjectMapper();
		try {
			VisitorPassConfigDto configs = mapper.readValue(visitorTimings.getValue(), VisitorPassConfigDto.class);

			// validate floors
			boolean floorValidation = configs.getRestrictedFloors().stream()
					.anyMatch(t -> t.equalsIgnoreCase(ipDetails.getFloor()));
			if (floorValidation) {
				throw new AigOneException("Visitors are not allowed in this floor:: " + ipDetails.getFloor());
			}

			VisitorPassFloorConfigDto visitorPassFloorConfigDto = configs.getAllowedFloors().stream()
					.filter(af -> af.getFloorNos().stream().anyMatch(f -> f.equalsIgnoreCase(ipDetails.getFloor())))
					.findFirst().orElseThrow(() -> new AigOneException(
							"Visitors are not allowed in this floor:" + ipDetails.getFloor()));

//			VisitorPassFloorConfigDto visitorPassFloorConfigDto = first.get();
			VisitorPassBedsConfigDto visitorPassBedsConfigDto = visitorPassFloorConfigDto.getBedTypes().stream().filter(
					bt -> bt.getBedTypes().stream().anyMatch(b -> b.equalsIgnoreCase(ipDetails.getNursingStation())))
					.findFirst().get();
			TimeSlot timeSlot = visitorPassBedsConfigDto.getTimeSlots().stream().filter(ts -> {
				LocalTime slotStart = LocalTime.parse(ts.getStartTime());
				slotStart = slotStart.plusMinutes(-configs.getThreshold());
				LocalTime slotEnd = LocalTime.parse(ts.getEndTime());
				slotEnd = slotEnd.plusMinutes(configs.getThreshold());
				return (visitorDto.getVisitStartTime().isAfter(slotStart)
						&& visitorDto.getVisitEndTime().isBefore(slotEnd)
						|| visitorDto.getVisitStartTime().isBefore(slotStart)
						|| visitorDto.getVisitStartTime().equals(slotStart));
			}).findFirst().orElseThrow(() -> new AigOneException("No slots available for this day."));
			System.out.println(timeSlot);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
	}

	@Override
	public String createVisitorPass(PatientVisitorPassDto visitorDto) {
		PatientEntity patientEntity = patientRepo.findByUhId(visitorDto.getUhId())
				.orElseThrow(() -> new AigOneException("Patient Not Found"));

//		int countExpiredPasses = vistorPassRepo.countExpiredPasses();

		int activePassesforGivenMobileNo = vistorPassRepo
				.countByPatientIdAndIpNumberAndVisitDateAndVisitorPhoneNoAndStatusOrderByVisitDate(
						patientEntity.getId(), visitorDto.getIpNumber(), new Date(),
						Long.valueOf(visitorDto.getVisitorPhoneNo()), StatusEnum.ACTIVE);
		if (activePassesforGivenMobileNo > 0) {
			throw new AigOneException("The given visitor has active pass");
		}

		int activePasses = vistorPassRepo.countByPatientIdAndIpNumberAndVisitDateAndStatusOrderByVisitDate(
				patientEntity.getId(), visitorDto.getIpNumber(), visitorDto.getVisitDate(), StatusEnum.ACTIVE);
		SystemSettingsEntity visitorTimings = systemRepo.findByKey("VISITOR_TIMINGS");
		SystemSettingsEntity visitsThreshold = systemRepo.findByKey("VISITS_THERSHOLD");
		if (activePasses == Integer.valueOf(visitsThreshold.getValue()))
			throw new AigOneException("Max limit for visitor pass creation has been exceeded.");

		List<PatientIPDetailsDto> patientIpDetails = fetchPatiennIpDetailsByUhId(visitorDto.getUhId(),
				SecurityUtil.getCurrentUser().getEmployeeId());
		if (CollectionUtils.isEmpty(patientIpDetails)) {
			throw new AigOneException("IP Details not found for the given UH ID:: " + visitorDto.getUhId());
		}
		PatientIPDetailsDto ipDetails = patientIpDetails.get(0);

		if (visitorDto.getVisitStartTime() == null) {
			visitorDto.setVisitStartTime(LocalTime.now());
		} else if (visitorDto.getVisitEndTime() != null
				&& (visitorDto.getVisitStartTime().isAfter(visitorDto.getVisitEndTime()))) {
			throw new AigOneException("Please give valid time slots");
		}
		visitorDto
				.setVisitEndTime(visitorDto.getVisitEndTime() != null ? visitorDto.getVisitEndTime() : LocalTime.now());

		ObjectMapper mapper = new ObjectMapper();
		try {
			VisitorPassConfigDto configs = mapper.readValue(visitorTimings.getValue(), VisitorPassConfigDto.class);

			// validate floors
			boolean floorValidation = configs.getRestrictedFloors().stream()
					.anyMatch(t -> t.equalsIgnoreCase(ipDetails.getFloor()));
			if (floorValidation) {
				throw new AigOneException("Visitors are not allowed in this floor");
			}

			VisitorPassFloorConfigDto visitorPassFloorConfigDto = configs.getAllowedFloors().stream()
					.filter(af -> af.getFloorNos().stream().anyMatch(f -> f.equalsIgnoreCase(ipDetails.getFloor())))
					.findFirst().orElseThrow(() -> new AigOneException("Visitors are not allowed in this floor"));

			Optional<VisitorPassBedsConfigDto> first = visitorPassFloorConfigDto.getBedTypes().stream().filter(
					bt -> bt.getBedTypes().stream().anyMatch(b -> b.equalsIgnoreCase(ipDetails.getNursingStation())))
					.findFirst();

			if (!first.isPresent()) {
				first = visitorPassFloorConfigDto.getBedTypes().stream()
						.filter(bt -> bt.getBedTypes().stream().anyMatch(b -> b.equalsIgnoreCase("GW"))).findFirst();
			}

//			VisitorPassBedsConfigDto visitorPassFloorConfigDto = first.get();
			VisitorPassBedsConfigDto visitorPassBedsConfigDto = first.get();

//			VisitorPassFloorConfigDto visitorPassFloorConfigDto = first.get();
//			VisitorPassBedsConfigDto visitorPassBedsConfigDto = visitorPassFloorConfigDto.getBedTypes().stream().filter(bt->bt.getBedTypes().stream().anyMatch(b->b.equalsIgnoreCase(ipDetails.getNursingStation()))).findFirst().get();
			TimeSlot timeSlot = visitorPassBedsConfigDto.getTimeSlots().stream().filter(ts -> {
				LocalTime slotStart = LocalTime.parse(ts.getStartTime());
//				slotStart = slotStart.plusMinutes(-configs.getThreshold());
				LocalTime slotEnd = LocalTime.parse(ts.getEndTime());
//				slotEnd = slotEnd.plusMinutes(configs.getThreshold());
//				return (visitorDto.getVisitStartTime().isAfter(slotStart) && visitorDto.getVisitEndTime().isBefore(slotEnd) || 
//						visitorDto.getVisitStartTime().isBefore(slotStart)||visitorDto.getVisitStartTime().equals(slotStart));
				return visitorDto.getVisitStartTime().isBefore(slotEnd);
			}).findFirst().orElseThrow(() -> new AigOneException("No slots available for this day."));

			visitorDto.setVisitStartTime(LocalTime.parse(timeSlot.getStartTime()));
			visitorDto.setVisitEndTime(LocalTime.parse(timeSlot.getEndTime()));
			visitorDto.setVisitDate(new Date());

			PatientVisitorPassEntity entity = aigOneMapper.toPatientVisitorPassEntity(visitorDto);
			entity.setPatient(patientEntity);
			entity.setPassColor(visitorPassBedsConfigDto.getPassColor());
			entity.setPassColorCode(visitorPassBedsConfigDto.getPassColorCode());
			entity.setStatus(
					visitorDto.getVisitStartTime().isAfter(LocalTime.now()) ? StatusEnum.INACTIVE : StatusEnum.ACTIVE);
			entity.setNsName(ipDetails.getNsName());
			;
			vistorPassRepo.save(entity);
			smsService.sendSms(String.valueOf(visitorDto.getVisitorPhoneNo()),
					String.valueOf(RequestedFromEnum.VISITOR_PASS), null, null, null);
			return "Visitor pass created Successfully";
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		return "Failed to create Visitor pass";

//		ObjectMapper objectMapper = new ObjectMapper();
//		JsonNode timeSlots;
//		try {
//			timeSlots = objectMapper.readTree(visitorTimings.getValue()).get("timeSlots");
//		} catch (Exception e) {
//			throw new AigOneException("Error parsing visitor timings from system settings");
//		}
//
//		if (visitorDto.getVisitStartTime() == null) {
//			visitorDto.setVisitStartTime(LocalTime.now());
//		}else if(visitorDto.getVisitEndTime() != null && (visitorDto.getVisitStartTime().isAfter(visitorDto.getVisitEndTime()))) {
//				throw new AigOneException("Please give valid time slots");
//		}
//		visitorDto.setVisitEndTime(visitorDto.getVisitEndTime() != null?visitorDto.getVisitEndTime():LocalTime.now());
//		JsonNode validSlot = StreamSupport.stream(timeSlots.spliterator(), false)
//				.filter(slot -> {
//					LocalTime slotStart = LocalTime.parse(slot.get("startTime").asText());
//					LocalTime slotEnd = LocalTime.parse(slot.get("endTime").asText());
//					return (visitorDto.getVisitStartTime().isAfter(slotStart) && visitorDto.getVisitEndTime().isBefore(slotEnd) || 
//							visitorDto.getVisitStartTime().isBefore(slotStart)||visitorDto.getVisitStartTime().equals(slotStart));
//				})
//				.findFirst()
//				.orElseThrow(() -> new AigOneException("No slots available for this day."));

//			visitorDto.setVisitStartTime(LocalTime.parse(validSlot.get("startTime").asText()));
//			visitorDto.setVisitEndTime(LocalTime.parse(validSlot.get("endTime").asText()));
//
//			PatientVisitorPassEntity entity = aigOneMapper.toPatientVisitorPassEntity(visitorDto);
//			entity.setPatient(patientEntity);
//			vistorPassRepo.save(entity);
//			smsService.sendSms(String.valueOf(visitorDto.getVisitorPhoneNo()), String.valueOf(RequestedFromEnum.VISITOR_PASS),null,null,null);
//			return "Visitor pass created Successfully";
	}

	private boolean validateVisitingTime(PatientVisitorPassEntity pass, int threshholdTime) {
		LocalTime visitStartTime = pass.getVisitStartTime();
		LocalTime visitEndTime = pass.getVisitEndTime();
		visitStartTime = visitStartTime.plusMinutes(-threshholdTime);
		visitEndTime = visitEndTime.plusMinutes(threshholdTime);
		LocalTime currentTime = LocalTime.now();
		return currentTime.isAfter(visitStartTime) && currentTime.isBefore(visitEndTime);
	}

	@Override
	public List<PatientVisitorPassDto> fetchVisitorPass(Long visitorPhoneNumber, String uhId, boolean validate) {
		PatientEntity patientEntity = patientRepo.findByUhId(uhId)
				.orElseThrow(() -> new AigOneException("Patient Not Found"));
//		vistorPassRepo.updateAllExpiredPasses();

		SystemSettingsEntity visitingTimeThreshold = systemRepo.findByKey("VISITING_TIME_THERSHOLD");
		int countExpiredPasses = vistorPassRepo.countExpiredPasses(Integer.valueOf(visitingTimeThreshold.getValue()));
		if (countExpiredPasses > 0) {
			vistorPassRepo.updateAllExpiredPasses(Integer.valueOf(visitingTimeThreshold.getValue()));
		}
//		List<PatientVisitorPassEntity> entities = vistorPassRepo.findByPatient_IdAndVisitorPhoneNoAndStatusOrderByVisitDate(patientEntity.getId(),visitorPhoneNumber,StatusEnum.ACTIVE);
		PatientVisitorPassEntity patientVisitorPassEntity = vistorPassRepo
				.findTop1ByPatientIdAndVisitorPhoneNoOrderByCreationDateDesc(patientEntity.getId(), visitorPhoneNumber);
		if (patientVisitorPassEntity.getStatus().equals(StatusEnum.INACTIVE)
				&& validateVisitingTime(patientVisitorPassEntity, Integer.valueOf(visitingTimeThreshold.getValue()))) {
			patientVisitorPassEntity.setStatus(StatusEnum.ACTIVE);
			vistorPassRepo.save(patientVisitorPassEntity);
		}
		;

		PatientVisitorPassDto dto = aigOneMapper.toPatientVisitorPassDto(patientVisitorPassEntity);

//		List<PatientVisitorPassDto> dtos = entities.stream().map(e->{
//			PatientVisitorPassDto dto = aigOneMapper.toPatientVisitorPassDto(e);
//			dto.setUhId(e.getPatient().getUhId());
//			return dto;
//		}).toList();
		if (validate && patientVisitorPassEntity.getVisitedTime() == null) {
//			PatientVisitorPassEntity patientVisitorPassEntity = entities.get(0);
//			patientVisitorPassEntity.setStatus(StatusEnum.INACTIVE);
			patientVisitorPassEntity.setVisitedTime(new Date());
			vistorPassRepo.save(patientVisitorPassEntity);
		}
		return dto != null ? Arrays.asList(dto) : null;
	}

	@Override
	public List<PateintPreviousAppointmentsDto> fetchPreviousAppointmentsOfDotor(String doctorId, Date fromDate,
			Date toDate) {
		if (fromDate == null) {
			Date today = new Date();
			SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
			try {
				fromDate = formatter.parse(formatter.format(today));
				toDate = formatter.parse(formatter.format(today));
			} catch (ParseException e) {
				e.printStackTrace();
			}
		}
		if (toDate.before(fromDate)) {
			throw new AigOneException("End date must not precede the start date.");
		}
		Optional<UserEntity> byEmployeeId = userRepo.findByEmployeeIdAndActiveTrue(doctorId);

		if (!byEmployeeId.isPresent()) {
			throw new AigOneException("doctor not found with this employee Id: " + doctorId);
		}
		Integer dbId = byEmployeeId.get().getDbId();

		long start = System.currentTimeMillis();
		log.info("Fetching previous appointments Start " + start);
		List<PateintPreviousAppointmentsDto> appointmentDetails = hsDbRepo
				.fetchPreviousAppointmentsOfDoctor(List.of(doctorId), fromDate, toDate);
		long timeTaken = System.currentTimeMillis() - start;
		log.info("Fetching previous appointments End " + timeTaken);
		if (CollectionUtils.isEmpty(appointmentDetails)) {
			throw new AigOneException("no appointments found for this doctor " + doctorId);
		}

		start = System.currentTimeMillis();

		log.info("Fetching Opconsultaion status Start " + start);

		List<Integer> billIds = appointmentDetails.stream().map(PateintPreviousAppointmentsDto::getBillId)
				.collect(Collectors.toList());

		List<OpConsultationEntity> opConsultations = opConsultationRepo.findAllByBillIdIn(billIds);
		timeTaken = System.currentTimeMillis() - start;
		log.info("Fetching Opconsultaion status End " + timeTaken);

		start = System.currentTimeMillis();
		log.info("Iteration Opconsultaion status Start " + start);

//		appointmentDetails = appointmentDetails.stream().map(ad -> {
//			Optional<OpConsultationEntity> firstAfterMatch = opConsultations.stream()
//					.filter(oc -> oc.getBillId().intValue() == ad.getBillId().intValue()) // Replace with your condition
//					.findFirst();
//			ad.setConsultationStatus("PENDING");
//			firstAfterMatch.ifPresent(row -> {
//				if (row.getStatus() != null) {
//					ad.setConsultationStatus(row.getStatus().name());
//				}
//			});
//			return ad;
//		}).toList();
		
		
		
		Map<Integer, OpConsultationEntity> latestConsultationMap = opConsultations.stream()
			    .collect(Collectors.groupingBy(
			        OpConsultationEntity::getBillId,
			        Collectors.collectingAndThen(
			            Collectors.maxBy(Comparator.comparing(OpConsultationEntity::getCreationDate)),
			            optional -> optional.orElse(null)
			        )
			    ));

			appointmentDetails = appointmentDetails.stream().map(ad -> {
			    OpConsultationEntity row = latestConsultationMap.get(ad.getBillId());
			    ad.setConsultationStatus(row != null && row.getStatus() != null
			        ? row.getStatus().name()
			        : "PENDING");
			    return ad;
			}).toList();

		timeTaken = System.currentTimeMillis() - start;
		log.info("Iteration Opconsultaion status End " + timeTaken);

//		appointmentDetails.forEach(a->{
//			OpConsultationEntity entity = opConsultationRepo.findByDoctorEmployeeIdAndUhIdAndBillId(a.getDoctorEmployeeId(),a.getUhid(),a.getBillId());
//			a.setConsultationStatus(entity == null?"PENDING":"COMPLETED");
//		});
		return appointmentDetails;
	}
	


	@Override
	public List<PatientPlannedDetailsDto> fetchPlannedAppointmentsOfDotor(String doctorId, Date fromDate,String action) {
		if (fromDate == null) {
			Date today = new Date();
			SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
			try {
				fromDate = formatter.parse(formatter.format(today));
			} catch (ParseException e) {
				e.printStackTrace();
			}
		}
		List<OpConsultationEntity> opConsultations = opConsultationRepo.findAllByPlannedDateAndDoctorEmployeeId(fromDate, doctorId);
		List<String> uhids = opConsultations.stream().map(OpConsultationEntity::getUhId)
		.collect(Collectors.toList());
		List<PatientFlaggedDetailsDto> patientsList= hsDbRepo.fetchPatientDetailsByUhIds(uhids, doctorId);
		List<PatientPlannedDetailsDto> plannedList = patientsList.stream()
			    .map(aigOneMapper::toPatientPlannedResponseDto).map(ad ->{
			    	Optional<OpConsultationEntity> firstAfterMatch = opConsultations.stream()
							.filter(oc -> oc.getUhId().equals(ad.getUhId())) // Replace with your condition
							.findFirst();
					firstAfterMatch.ifPresent(row -> {
						if (row.getStatus() != null) {
							ad.setPlannedTime(row.getPlannedTime());
						}
					});
					return ad;
			    })
			    .collect(Collectors.toList());
	    return plannedList;
	}
	
	@Override
	public List<OpConsultationDto> fetchPatientConsultationNotes(String doctorEmployeeId, String uhId) {
		List<OpConsultationEntity> entities = opConsultationRepo.findByUhIdOrderByCreationDateDesc(uhId);
		return entities.stream().map(aigOneMapper::toOpConsultationDto).toList();
	}

	@Override
	public String updateEmployeeRoster(EmployeeRosterDto rosterDto) {
		if (rosterDto.getId() != null && rosterDto.getId() != 0) {
			EmployeeRosterEntity existingEntity = EmployeeRosterRepo.findById(rosterDto.getId())
					.orElseThrow(() -> new AigOneException("Entity not found"));
			existingEntity.setDayEmployee(
					rosterDto.getDayEmployee() != null && rosterDto.getDayEmployee() != "" ? rosterDto.getDayEmployee()
							: existingEntity.getDayEmployee());
			existingEntity.setDayEmployeePhoneNo(
					rosterDto.getDayEmployeePhoneNo() != null && rosterDto.getDayEmployeePhoneNo() != ""
							? rosterDto.getDayEmployeePhoneNo()
							: existingEntity.getDayEmployeePhoneNo());
			existingEntity.setNightEmployee(rosterDto.getNightEmployee() != null && rosterDto.getNightEmployee() != ""
					? rosterDto.getNightEmployee()
					: existingEntity.getNightEmployee());
			existingEntity.setNightEmployeePhoneNo(
					rosterDto.getNightEmployeePhoneNo() != null && rosterDto.getNightEmployeePhoneNo() != ""
							? rosterDto.getNightEmployeePhoneNo()
							: existingEntity.getNightEmployeePhoneNo());
			EmployeeRosterRepo.save(existingEntity);
		} else {
			EmployeeRosterEntity newEntity = aigOneMapper.toEmployeeRosterEntity(rosterDto);
			EmployeeRosterEntity entity = EmployeeRosterRepo
					.findTopByDateAndDepartmentAndSubDepartmentOrderByPriorityDesc(rosterDto.getDate(),
							rosterDto.getDepartment(), rosterDto.getSubDepartment());
			String priority = "1";
			if (entity != null) {
				priority = String.valueOf(Integer.parseInt(entity.getPriority()) + 1);
			}
			newEntity.setPriority(priority);
			EmployeeRosterRepo.save(newEntity);
		}
		return "Roster Update Successfully";
	}

	@Override
	public List<PatientVisitorPassDto> fetchActiveIPVistorPass(String uhId, Long ipNumber) {
		PatientEntity patientEntity = patientRepo.findByUhId(uhId)
				.orElseThrow(() -> new AigOneException("Patient Not Found"));
		SystemSettingsEntity visitingTimeThreshold = systemRepo.findByKey("VISITING_TIME_THERSHOLD");
		int countExpiredPasses = vistorPassRepo.countExpiredPasses(Integer.valueOf(visitingTimeThreshold.getValue()));
		if (countExpiredPasses > 0) {
			vistorPassRepo.updateAllExpiredPasses(Integer.valueOf(visitingTimeThreshold.getValue()));
		}
		List<PatientVisitorPassEntity> entities = vistorPassRepo
				.findByPatientIdAndIpNumberAndVisitDateOrderByCreationDateAsc(patientEntity.getId(), ipNumber,
						new Date());
		List<PatientVisitorPassDto> dtos = entities.stream().map(e -> {
			if (e.getStatus().equals(StatusEnum.INACTIVE)
					&& validateVisitingTime(e, Integer.valueOf(visitingTimeThreshold.getValue()))) {
				e.setStatus(StatusEnum.ACTIVE);
				vistorPassRepo.save(e);
			}
			PatientVisitorPassDto dto = aigOneMapper.toPatientVisitorPassDto(e);
			dto.setUhId(e.getPatient().getUhId());
			return dto;
		}).toList();
		return dtos;
	}

	@Override
	public List<TicketQuestionsEntity> fetchTicketQuestionsByType(TicketQuesEnum type) {
		return TicketQuesRepo.findByType(type);
	}

	@Override
	public List<PatientIPDetailsDto> fetchFavouritePatientsByDoctorId() {
		String employeeId = SecurityUtil.getCurrentUser().getEmployeeId();
		List<DoctorFavouritePatientsEntity> followupPatients = favouritePatientsRepo
				.findAllByStatusAndEmployeeIdOrderByCreationDateDesc(AigOneUtil.STATUS_FOLLOW, employeeId);

		if (CollectionUtils.isEmpty(followupPatients)) {
			throw new AigOneException("No favourite patients found ");
		}

		List<PatientIPDetailsDto> paitentIpDetails = new ArrayList<>();
		followupPatients.stream().forEach(fp -> {
			List<MDTEntity> allOpenMdtCasesForGivenUhId = mdtRepo.loadAllOpenMdtCasesForGivenUhId(fp.getUhId());
			boolean hasMdt = allOpenMdtCasesForGivenUhId != null && allOpenMdtCasesForGivenUhId.size() > 0;
			List<PatientIPDetailsDto> fetchPatiennIpDetailsByUhId = fetchPatiennIpDetailsByUhId(fp.getUhId(),
					SecurityUtil.getCurrentUser().getEmployeeId());
			if (!CollectionUtils.isEmpty(fetchPatiennIpDetailsByUhId)) {
				fetchPatiennIpDetailsByUhId.stream().map(ip -> {
					ip.setMdtStatus(hasMdt);
					return ip;
				});
			}
			paitentIpDetails.addAll(fetchPatiennIpDetailsByUhId);
		});

		return paitentIpDetails;
	}

	@Override
	public List<PatientFlaggedDetailsDto> fetchAllFavouritePatientsByDoctorId(String doctorId) {
		List<DoctorFavouritePatientsEntity> followupPatients = favouritePatientsRepo
				.findAllByStatusAndEmployeeIdOrderByCreationDateDesc(AigOneUtil.STATUS_FOLLOW, doctorId);
		List<String> uhids = followupPatients.stream().map(patient -> patient.getUhId()).toList();
		System.out.println(uhids);
		System.out.println(doctorId);

		return hsDbRepo.fetchPatientDetailsByUhIds(uhids, doctorId);
//		List<PatientFlaggedDetailsDto> paitentDetails = fetchPatiennIpDetailsByUhId(followupPatients,
	}

	@Override
	public List<PatientFlaggedDetailsDto> searchPatients(String searchString, String doctorId) {
		return hsDbRepo.searchPatientDetails(searchString, doctorId);
//		List<PatientFlaggedDetailsDto> paitentDetails = fetchPatiennIpDetailsByUhId(followupPatients,
	}

	@Override
	public void saveFavouritePatient(String uhId, String doctorId, boolean follow) {
//		String employeeId = SecurityUtil.getCurrentUser().getEmployeeId();
		DoctorFavouritePatientsEntity favPatient = favouritePatientsRepo.findByEmployeeIdAndUhId(doctorId, uhId);
		if (favPatient != null) {
			favPatient.setStatus(follow ? AigOneUtil.STATUS_FOLLOW : AigOneUtil.STATUS_UN_FOLLOW);
			favouritePatientsRepo.save(favPatient);
		} else {
			DoctorFavouritePatientsEntity patient = new DoctorFavouritePatientsEntity();
			patient.setEmployeeId(doctorId);
			patient.setStatus(follow ? AigOneUtil.STATUS_FOLLOW : AigOneUtil.STATUS_UN_FOLLOW);
			patient.setUhId(uhId);
			DoctorFavouritePatientsEntity save = favouritePatientsRepo.save(patient);
		}

	}

	private String fetchBase64FromUrl(String fileUrl) {
		try {
			URL url = new URI(fileUrl).toURL();
			HttpURLConnection connection = (HttpURLConnection) url.openConnection();
			connection.setRequestMethod("GET");
			connection.connect();
			log.info(connection.getContentType());
//			if (connection.getResponseCode() == 200 && "application/pdf".equals(connection.getContentType())) {
			if (connection.getResponseCode() == 200) {
				InputStream inputStream = connection.getInputStream();
				ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
				byte[] buffer = new byte[1024];
				int bytesRead;
				while ((bytesRead = inputStream.read(buffer)) != -1) {
					byteArrayOutputStream.write(buffer, 0, bytesRead);
				}

				String base64Document = Base64.getEncoder().encodeToString(byteArrayOutputStream.toByteArray());

				inputStream.close();
				byteArrayOutputStream.close();
				return base64Document;
			} else {
				log.info(String.format("Document URL-> %s Response: %s %s ", fileUrl, connection.getResponseCode(),
						connection.getResponseMessage()));
				return null;
			}
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	@Override
	public PatientReportDetailsDto fetchBase64AndPacsReport(ViewReportRequestDto viewReportDto) {
		return fetchReportLink(viewReportDto);
	}

	@Override
	public List<ReportStatusDto> getReportStatus(String billId) {
		// TODO Auto-generated method stub
		return hsDbRepo.getReportStatus(billId);
	}

	 @Transactional
	    public HisResponseListDto<PatientReportsResponseDto> fetchAllPatientReports(String uhId) {
	        // Fetch all patient report details
	        List<PatientReportSummaryDto> allReportDetails = hsDbRepo.fetchAllPatientReportDetails(uhId);

	        // Initialize response DTO
	        HisResponseListDto<PatientReportsResponseDto> responseDto = new HisResponseListDto<>();
	        List<PatientReportsResponseDto> reports = allReportDetails.stream()
	                .map(detail -> {
	                    PatientReportsResponseDto report = new PatientReportsResponseDto();
	                    report.setOrderID(detail.getOrderId());
	                    report.setTestID(detail.getTestId());
	                    report.setSampleCollDateTime(detail.getSampleCollDateTime());
	                    report.setSampleVerifiedDateTime(detail.getSampleVerifiedDateTime());
	                    report.setReportDetails(allReportDetails.stream()
	                            .filter(d -> d.getOrderId() == detail.getOrderId() && d.getTestId() == detail.getTestId())
	                            .collect(Collectors.toList()));
	                    return report;
	                })
	                .sorted(Comparator.comparing(PatientReportsResponseDto::getSampleVerifiedDateTime))
	                .collect(Collectors.toList());

	        responseDto.setResponse(reports);
	        return responseDto;
	    }

	 @Override
	 public List<PatientTestDTO> getPatientsWithCBPCBC(String date, String uhid, Boolean isCaptured) {
	     return hsDbRepo.getPatientsWithCBPCBC(date, uhid, isCaptured);
	 }

	 

//	  @Override
//	    public String capturePatientDetails(PatientAmnesiaDetails patientDetails) {
//	        patientDetailsRepository.save(patientDetails);
//	        return "Patient details captured successfully";
//	    }
//	  
	 
	 
	 
	 @Override
	    public String capturePatientDetails(PatientAmnesiaDetails patientDetails) {
		  
		  String leftEyePath = anemiaImageStorageService.saveImage(patientDetails.getLeftEye());
		  String rightEyePath= anemiaImageStorageService.saveImage(patientDetails.getRightEye());
		  String rightHandPath = anemiaImageStorageService.saveImage(patientDetails.getRightHand());
		  String leftHandPath = anemiaImageStorageService.saveImage(patientDetails.getLeftHand());
		  String tonguePath = anemiaImageStorageService.saveImage(patientDetails.getTongue());
		  String nailspath = anemiaImageStorageService.saveImage(patientDetails.getNails());
		  String signaturePath = anemiaImageStorageService.saveImage(patientDetails.getPatientSignature());
		  
		  patientDetails.setLeftEye(leftEyePath);
		  patientDetails.setRightEye(rightEyePath);
		  patientDetails.setRightHand(rightHandPath);
		  patientDetails.setLeftHand(leftHandPath);
		  patientDetails.setTongue(tonguePath);
		  patientDetails.setNails(nailspath);
		  patientDetails.setPatientSignature(signaturePath);

		  patientDetailsRepository.save(patientDetails);
		  
	        return "Patient details captured successfully";
	    }

	  

//	  @Override
//	  public List<PatientAmnesiaDetailsDTO> getCapturedDetailsByUhid(String uhid) {
//	      List<PatientAmnesiaDetails> patients = patientDetailsRepository.findByUhid(uhid);
//
//	      if (patients.isEmpty()) {
//	          throw new RuntimeException("No patient details found for UHID: " + uhid);
//	      }
//
//	      // Convert each entity to DTO and return a list
//	      return patients.stream().map(patient -> new PatientAmnesiaDetailsDTO(
//	              patient.getId(),
//	              patient.getUhid(),
//	              patient.getName(),
//	              patient.getAge(),
//	              patient.getGender(),
//	              patient.getTestId(),
//	              patient.getOrderId(),
//	              patient.getTest(),
//	
//	              patient.getLeftEye(),
//	              patient.getRightEye(),
//	              patient.getTongue(),
//	              patient.getNails(),
//	              patient.getLeftHand(),
//	              patient.getRightHand(),
//	              patient.getRemarks(),
//	              patient.getPatientSignature(),
//	              
//	              // Mapping Auditable fields
//	              patient.getCreatedBy(),
//	              patient.getCreationDate(),
//	              patient.getLastModifiedBy(),
//	              patient.getLastModifiedDate()
//	      )).collect(Collectors.toList());
//	  }

	  
	  
	  @Override
	  public List<PatientAmnesiaDetailsDTO> getCapturedDetailsByUhid(String uhid) {
	      List<PatientAmnesiaDetails> patients = patientDetailsRepository.findByUhid(uhid);

	      if (patients.isEmpty()) {
	          throw new RuntimeException("No patient details found for UHID: " + uhid);
	      }

	      // Use java.util.Date
	      Date cutoffDate;
	      try {
	          cutoffDate = new SimpleDateFormat("yyyy-MM-dd").parse("2025-04-16");
	      } catch (ParseException e) {
	          throw new RuntimeException("Invalid cutoff date format");
	      }

	      return patients.stream().map(patient -> {
	          boolean isBeforeCutoff = patient.getCreationDate() != null &&
	                                   !patient.getCreationDate().before(cutoffDate);

	          if (isBeforeCutoff) {
	              String leftEye = anemiaImageStorageService.getImageAsBase64(patient.getLeftEye());
	              String rightEye = anemiaImageStorageService.getImageAsBase64(patient.getRightEye());
	              String tongue = anemiaImageStorageService.getImageAsBase64(patient.getTongue());
	              String nails = anemiaImageStorageService.getImageAsBase64(patient.getNails());
	              String leftHand = anemiaImageStorageService.getImageAsBase64(patient.getLeftHand());
	              String rightHand = anemiaImageStorageService.getImageAsBase64(patient.getRightHand());
	              String signature = anemiaImageStorageService.getImageAsBase64(patient.getPatientSignature());

	              return new PatientAmnesiaDetailsDTO(
	                      patient.getId(), patient.getUhid(), patient.getName(), patient.getAge(), patient.getGender(),
	                      patient.getTestId(), patient.getOrderId(), patient.getTest(),
	                      leftEye, rightEye, tongue, nails, leftHand, rightHand, patient.getRemarks(), signature,
	                      patient.getCreatedBy(), patient.getCreationDate(),
	                      patient.getLastModifiedBy(), patient.getLastModifiedDate()
	              );
	          } else {
	              return new PatientAmnesiaDetailsDTO(
	                      patient.getId(), patient.getUhid(), patient.getName(), patient.getAge(), patient.getGender(),
	                      patient.getTestId(), patient.getOrderId(), patient.getTest(),
	                      patient.getLeftEye(), patient.getRightEye(), patient.getTongue(), patient.getNails(),
	                      patient.getLeftHand(), patient.getRightHand(), patient.getRemarks(), patient.getPatientSignature(),
	                      patient.getCreatedBy(), patient.getCreationDate(),
	                      patient.getLastModifiedBy(), patient.getLastModifiedDate()
	              );
	          }
	      }).collect(Collectors.toList());
	  }

//	  
//	  @Override
//	  public PatientAmnesiaDetailsDTO getCapturedDetailsById(Long id) {
//	      PatientAmnesiaDetails patient = patientDetailsRepository.findById(id)
//	              .orElseThrow(() -> new RuntimeException("No patient details found for ID: " + id));
//
//	      // Convert Entity to DTO manually
//	      return new PatientAmnesiaDetailsDTO(
//	              patient.getId(),
//	              patient.getUhid(),
//	              patient.getName(),
//	              patient.getAge(),
//	              patient.getGender(),
//	              patient.getTestId(),
//	              patient.getOrderId(),
//	              patient.getTest(),
//	
//	              patient.getLeftEye(),
//	              patient.getRightEye(),
//	              patient.getTongue(),
//	              patient.getNails(),
//	              patient.getLeftHand(),
//	              patient.getRightHand(),
//	              patient.getRemarks(),
//	              patient.getPatientSignature(),
//
//	              // Mapping Auditable fields
//	              patient.getCreatedBy(),
//	              patient.getCreationDate(),
//	              patient.getLastModifiedBy(),
//	              patient.getLastModifiedDate()
//	      );
//	  }
//

	  
	  @Override
	  public PatientAmnesiaDetailsDTO getCapturedDetailsById(Long id) {
	      PatientAmnesiaDetails patient = patientDetailsRepository.findById(id)
	              .orElseThrow(() -> new RuntimeException("No patient details found for ID: " + id));

	      Date cutoffDate;
	      try {
	          cutoffDate = new SimpleDateFormat("yyyy-MM-dd").parse("2025-04-16");
	      } catch (ParseException e) {
	          throw new RuntimeException("Invalid cutoff date format");
	      }

	      boolean isBeforeCutoff = patient.getCreationDate() != null &&
	                               patient.getCreationDate().before(cutoffDate);

	      if (isBeforeCutoff) {
	          return new PatientAmnesiaDetailsDTO(
	                  patient.getId(), patient.getUhid(), patient.getName(), patient.getAge(), patient.getGender(),
	                  patient.getTestId(), patient.getOrderId(), patient.getTest(),
	                  anemiaImageStorageService.getImageAsBase64(patient.getLeftEye()),
	                  anemiaImageStorageService.getImageAsBase64(patient.getRightEye()),
	                  anemiaImageStorageService.getImageAsBase64(patient.getTongue()),
	                  anemiaImageStorageService.getImageAsBase64(patient.getNails()),
	                  anemiaImageStorageService.getImageAsBase64(patient.getLeftHand()),
	                  anemiaImageStorageService.getImageAsBase64(patient.getRightHand()),
	                  patient.getRemarks(),
	                  anemiaImageStorageService.getImageAsBase64(patient.getPatientSignature()),
	                  patient.getCreatedBy(), patient.getCreationDate(),
	                  patient.getLastModifiedBy(), patient.getLastModifiedDate()
	          );
	      } else {
	          return new PatientAmnesiaDetailsDTO(
	                  patient.getId(), patient.getUhid(), patient.getName(), patient.getAge(), patient.getGender(),
	                  patient.getTestId(), patient.getOrderId(), patient.getTest(),
	                  patient.getLeftEye(), patient.getRightEye(), patient.getTongue(), patient.getNails(),
	                  patient.getLeftHand(), patient.getRightHand(), patient.getRemarks(), patient.getPatientSignature(),
	                  patient.getCreatedBy(), patient.getCreationDate(),
	                  patient.getLastModifiedBy(), patient.getLastModifiedDate()
	          );
	      }
	  }

//
//	    @Override
//	    public PatientAmnesiaDetails updatePatientDetails(Long id, PatientAmnesiaDetails updatedDetails) {
//	        Optional<PatientAmnesiaDetails> existingPatientOpt = patientDetailsRepository.findById(id);
//
//	        if (existingPatientOpt.isPresent()) {
//	            PatientAmnesiaDetails existingPatient = existingPatientOpt.get();
//
//	            // Update fields only if new values are provided
//	            existingPatient.setUhid(updatedDetails.getUhid());
//	            existingPatient.setName(updatedDetails.getName());
//	            existingPatient.setAge(updatedDetails.getAge());
//	            existingPatient.setGender(updatedDetails.getGender());
//	            existingPatient.setTestId(updatedDetails.getTestId());
//	            existingPatient.setOrderId(updatedDetails.getOrderId());
//	            existingPatient.setTest(updatedDetails.getTest());
//           // existingPatient.setDate(updatedDetails.getDate());
//	            existingPatient.setLeftEye(updatedDetails.getLeftEye());
//	            existingPatient.setRightEye(updatedDetails.getRightEye());
//	            existingPatient.setTongue(updatedDetails.getTongue());
//	            existingPatient.setNails(updatedDetails.getNails());
//	            existingPatient.setLeftHand(updatedDetails.getLeftHand());
//	            existingPatient.setRightHand(updatedDetails.getRightHand());
//	            existingPatient.setRemarks(updatedDetails.getRemarks());
//	            existingPatient.setPatientSignature(updatedDetails.getPatientSignature());
//	            
//
//	            return patientDetailsRepository.save(existingPatient);
//	        } else {
//	            throw new RuntimeException("Patient with ID " + id + " not found.");
//	        }
//	    }
	    
	  
	  
	  @Override
	  public PatientAmnesiaDetails updatePatientDetails(Long id, PatientAmnesiaDetails updatedDetails) {
	      return patientDetailsRepository.findById(id).map(existingPatient -> {

	          // Update basic info
	          existingPatient.setUhid(updatedDetails.getUhid());
	          existingPatient.setName(updatedDetails.getName());
	          existingPatient.setAge(updatedDetails.getAge());
	          existingPatient.setGender(updatedDetails.getGender());
	          existingPatient.setTestId(updatedDetails.getTestId());
	          existingPatient.setOrderId(updatedDetails.getOrderId());
	          existingPatient.setTest(updatedDetails.getTest());
	          existingPatient.setRemarks(updatedDetails.getRemarks());

	          // Save new images and store paths
	          String leftEyePath = anemiaImageStorageService.saveImage(updatedDetails.getLeftEye());
	          String rightEyePath = anemiaImageStorageService.saveImage(updatedDetails.getRightEye());
	          String rightHandPath = anemiaImageStorageService.saveImage(updatedDetails.getRightHand());
	          String leftHandPath = anemiaImageStorageService.saveImage(updatedDetails.getLeftHand());
	          String tonguePath = anemiaImageStorageService.saveImage(updatedDetails.getTongue());
	          String nailspath = anemiaImageStorageService.saveImage(updatedDetails.getNails());
	          String signaturePath = anemiaImageStorageService.saveImage(updatedDetails.getPatientSignature());

	          existingPatient.setLeftEye(leftEyePath);
	          existingPatient.setRightEye(rightEyePath);
	          existingPatient.setRightHand(rightHandPath);
	          existingPatient.setLeftHand(leftHandPath);
	          existingPatient.setTongue(tonguePath);
	          existingPatient.setNails(nailspath);
	          existingPatient.setPatientSignature(signaturePath);

	          // Save updated entity
	          PatientAmnesiaDetails savedPatient = patientDetailsRepository.save(existingPatient);

	          // Convert stored image paths to Base64 for retur
	          return new PatientAmnesiaDetails(
	                  savedPatient.getId(),
	                  savedPatient.getUhid(),
	                  savedPatient.getName(),
	                  savedPatient.getAge(),
	                  savedPatient.getGender(),
	                  savedPatient.getTestId(),
	                  savedPatient.getOrderId(),
	                  savedPatient.getTest(),

	                  anemiaImageStorageService.getImageAsBase64(savedPatient.getLeftEye()),
	                  anemiaImageStorageService.getImageAsBase64(savedPatient.getRightEye()),
	                  anemiaImageStorageService.getImageAsBase64(savedPatient.getTongue()),
	                  anemiaImageStorageService.getImageAsBase64(savedPatient.getNails()),
	                  anemiaImageStorageService.getImageAsBase64(savedPatient.getLeftHand()),
	                  anemiaImageStorageService.getImageAsBase64(savedPatient.getRightHand()),
	                  savedPatient.getRemarks(),
	                  anemiaImageStorageService.getImageAsBase64(savedPatient.getPatientSignature()),

	                  savedPatient.getCreatedBy(),
	                  savedPatient.getCreationDate(),
	                  savedPatient.getLastModifiedBy(),
	                  savedPatient.getLastModifiedDate()
	          );

	      }).orElseThrow(() -> new RuntimeException("Patient with ID " + id + " not found."));
	  }


	    public List<String> getInterfaceIdsByVisitId(Long visitId) {
	        return hisRepository.findInterfaceIdsByVisitId(visitId);
	    }

		@Override
		public HisResponseListDto<PatientAbnoramlReportsResponseDto> fetchAllAbnormalLabValues(String upperCase) {
			 List<PatientReportAbnormalDto> allReportDetails = hsDbRepo.fetchAllAbnormalReportDetails(upperCase);
             log.info(upperCase);			    // Group reports by orderId and testId, and filter out entries with condition = 'N'
			    Map<AbstractMap.SimpleEntry<Integer, Integer>, List<PatientReportAbnormalDto>> groupedReports = 
			        allReportDetails.stream()
			            // Filter out entries where condition is 'N' upfront
			            .filter(dto -> !"N".equals(dto.getCondition()))
			            .collect(Collectors.groupingBy(
			                d -> new AbstractMap.SimpleEntry<>(d.getOrderId(), d.getTestId())
			            ));

			    // Map grouped entries to PatientReportsResponseDto
			    List<PatientAbnoramlReportsResponseDto> reports = groupedReports.entrySet().stream()
			        .map(entry -> {
			            AbstractMap.SimpleEntry<Integer, Integer> key = entry.getKey();
			            List<PatientReportAbnormalDto> groupDetails = entry.getValue();

			            // Create the response DTO
			            PatientAbnoramlReportsResponseDto report = new PatientAbnoramlReportsResponseDto();
			            report.setOrderID(key.getKey());
			            report.setTestID(key.getValue());
			            
			            // Set sample times from the first entry in the group (if available)
			            if (!groupDetails.isEmpty()) {
			            	PatientReportAbnormalDto firstDetail = groupDetails.get(0);
			                report.setSampleCollDateTime(firstDetail.getSampleCollDateTime());
			                report.setSampleVerifiedDateTime(firstDetail.getSampleVerifiedDateTime());
			            }

			            // Assign filtered details (already excluded 'N' during grouping)
			            report.setReportDetails(groupDetails);
			            return report;
			        })
			        .sorted(Comparator.comparing(PatientAbnoramlReportsResponseDto::getSampleVerifiedDateTime))
			        .collect(Collectors.toList());

			    // Build and return the response
			    HisResponseListDto<PatientAbnoramlReportsResponseDto> responseDto = new HisResponseListDto<>();
			    responseDto.setResponse(reports);
			    return responseDto;
		}
		
//  	1.0
//		@Override
//		public List<PateintPreviousAppointmentsDto> fetchPreviousAppointmentsOfDotorByTeam(String doctorId,
//				Date fromDate, Date toDate) {
//			
//			 // Default to today if fromDate is null
//		    if (fromDate == null) {
//		        Date today = new Date();
//		        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
//		        try {
//		            fromDate = formatter.parse(formatter.format(today));
//		            toDate = formatter.parse(formatter.format(today));
//		        } catch (ParseException e) {
//		            throw new AigOneException("Failed to parse date");
//		        }
//		    }
//
//		    // Validate date range
//		    if (toDate.before(fromDate)) {
//		        throw new AigOneException("End date must not precede the start date.");
//		    }
//
//		    // Validate doctor IDs
//		    Optional<UserEntity> byEmployeeId = userRepo.findByEmployeeId(doctorId);
//
//			if (!byEmployeeId.isPresent()) {
//				throw new AigOneException("doctor not found with this employee Id: " + doctorId);
//			}
//			
//			List<String> doctorIdsFromTeam = prismTeamsService.getEmployeeIdsByTeam(byEmployeeId.get().getId());
//
//		    long start = System.currentTimeMillis();
//		    log.info("Fetching previous appointments Start: {}", start);
//		    
//		    List<PateintPreviousAppointmentsDto> appointmentDetails =
//		            hsDbRepo.fetchPreviousAppointmentsOfDoctor(doctorIdsFromTeam, fromDate, toDate);
//
//		    long timeTaken = System.currentTimeMillis() - start;
//		    log.info("Fetching previous appointments End - Time taken: {}ms", timeTaken);
//
//		    if (CollectionUtils.isEmpty(appointmentDetails)) {
//		        throw new AigOneException("No appointments found for doctor(s): " + doctorIdsFromTeam);
//		    }
//
//		    // Fetch consultation status
//		    start = System.currentTimeMillis();
//		    log.info("Fetching OpConsultation status Start: {}", start);
//
//		    List<Integer> billIds = appointmentDetails.stream()
//		            .map(PateintPreviousAppointmentsDto::getBillId)
//		            .collect(Collectors.toList());
//
//		    List<OpConsultationEntity> opConsultations = opConsultationRepo.findAllByBillIdIn(billIds);
//
//		    timeTaken = System.currentTimeMillis() - start;
//		    log.info("Fetching OpConsultation status End - Time taken: {}ms", timeTaken);
//
//		    // Map consultation status to each appointment
//		    start = System.currentTimeMillis();
//		    log.info("Mapping OpConsultation status Start: {}", start);
//
//		    Map<Integer, OpConsultationEntity> billIdToConsultation = opConsultations.stream()
//		            .collect(Collectors.toMap(OpConsultationEntity::getBillId, oc -> oc, (a, b) -> a)); // handle duplicates safely
//
//		    appointmentDetails.forEach(ad -> {
//		        OpConsultationEntity entity = billIdToConsultation.get(ad.getBillId());
//		        ad.setConsultationStatus(entity != null && entity.getStatus() != null
//		                ? entity.getStatus().name()
//		                : "PENDING");
//		    });
//
//		    timeTaken = System.currentTimeMillis() - start;
//		    log.info("Mapping OpConsultation status End - Time taken: {}ms", timeTaken);
//
//		    return appointmentDetails;
//		}
		
//		1.1 from prism teams created 
//		@Override
//		public List<PateintPreviousAppointmentsDto> fetchPreviousAppointmentsOfDotorByTeam(String doctorId,
//		                                                                                   Date fromDate,
//		                                                                                   Date toDate) {
//
//		    // Default to today if fromDate is null
//		    if (fromDate == null) {
//		        Date today = new Date();
//		        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
//		        try {
//		            fromDate = formatter.parse(formatter.format(today));
//		            toDate = formatter.parse(formatter.format(today));
//		        } catch (ParseException e) {
//		            throw new AigOneException("Failed to parse date");
//		        }
//		    }
//
//		    // Validate date range
//		    if (toDate.before(fromDate)) {
//		        throw new AigOneException("End date must not precede the start date.");
//		    }
//
//		    // ✅ Step 1: Get logged-in user
//		    UserSessionVO currentUser = SecurityUtil.getCurrentUser();
//		    if (currentUser == null || currentUser.getEmployeeId() == null) {
//		        throw new AigOneException("Unauthorized access: No session found");
//		    }
//
//		    UserEntity viewer = userRepo.findByEmployeeId(currentUser.getEmployeeId())
//		        .orElseThrow(() -> new AigOneException("Logged-in doctor not found"));
//
//		    // ✅ Step 2: Get viewer's team
//		    PrismTeams viewerTeam = prismTeamsRepository.findByUserId(viewer.getId())
//		        .orElseThrow(() -> new AigOneException("No team found for doctor"));
//
//		    Long teamId = viewerTeam.getTeamMaster().getId();
//
//		   
//			// ✅ Step 3: Fetch delegates who allowed this viewer
//		    List<PrismDoctorDelegateAccess> delegates = delegateAccessRepo
//		        .findByDelegateDoctor_IdAndTeamMaster_IdAndStatusTrue(viewer.getId(), teamId);
//
//		    // ✅ Step 4: Collect their employeeIds
//		    List<String> allowedDoctorEmpIds = delegates.stream()
//		        .map(entry -> entry.getDoctor().getEmployeeId())
//		        .filter(Objects::nonNull)
//		        .distinct()
//		        .toList();
//
//		    if (allowedDoctorEmpIds.isEmpty()) {
//		        return Collections.emptyList(); // Or throw if preferred
//		    }
//
//		    // ✅ Step 5: Fetch previous consultations
//		    long start = System.currentTimeMillis();
//		    log.info("Fetching previous appointments Start: {}", start);
//
//		    List<PateintPreviousAppointmentsDto> appointmentDetails =
//		            hsDbRepo.fetchPreviousAppointmentsOfDoctor(allowedDoctorEmpIds, fromDate, toDate);
//
//		    long timeTaken = System.currentTimeMillis() - start;
//		    log.info("Fetching previous appointments End - Time taken: {}ms", timeTaken);
//
//		    if (CollectionUtils.isEmpty(appointmentDetails)) {
//		        throw new AigOneException("No appointments found for team doctors");
//		    }
//
//		    // ✅ Step 6: Add consultation status
//		    List<Integer> billIds = appointmentDetails.stream()
//		            .map(PateintPreviousAppointmentsDto::getBillId)
//		            .collect(Collectors.toList());
//
//		    List<OpConsultationEntity> opConsultations = opConsultationRepo.findAllByBillIdIn(billIds);
//
//		    Map<Integer, OpConsultationEntity> billIdToConsultation = opConsultations.stream()
//		            .collect(Collectors.toMap(OpConsultationEntity::getBillId, oc -> oc, (a, b) -> a));
//
//		    appointmentDetails.forEach(ad -> {
//		        OpConsultationEntity entity = billIdToConsultation.get(ad.getBillId());
//		        ad.setConsultationStatus(entity != null && entity.getStatus() != null
//		                ? entity.getStatus().name()
//		                : "PENDING");
//		    });
//
//		    log.info("Mapping OpConsultation status End");
//
//		    return appointmentDetails;
//		}
		
//		1.2 from his		
		@Override
		public List<PateintPreviousAppointmentsDto> fetchPreviousAppointmentsOfDotorByTeam(
		                                                                                   Date fromDate,
		                                                                                   Date toDate) {

		    // Default to today if fromDate is null
		    if (fromDate == null) {
		        Date today = new Date();
		        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		        try {
		            fromDate = formatter.parse(formatter.format(today));
		            toDate = formatter.parse(formatter.format(today));
		        } catch (ParseException e) {
		            throw new AigOneException("Failed to parse date");
		        }
		    }

		    // Validate date range
		    if (toDate.before(fromDate)) {
		        throw new AigOneException("End date must not precede the start date.");
		    }

		    // ✅ Step 1: Get logged-in user
		    UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		    if (currentUser == null || currentUser.getEmployeeId() == null) {
		        throw new AigOneException("Unauthorized access: No session found");
		    }

		    // ✅ Step 2: Fetch doctor employee IDs based on assistant relationship
		    List<String> allowedDoctorEmpIds = hsDbRepo.fetchDoctorEmployeeIdsByAssistant(currentUser.getEmployeeId());

		    if (allowedDoctorEmpIds.isEmpty()) {
		        log.warn("No doctors found for assistant: {}", currentUser.getEmployeeId());
		        return Collections.emptyList(); // Or throw if preferred
		    }

		    log.info("Found {} doctors for assistant {}: {}", 
		             allowedDoctorEmpIds.size(), 
		             currentUser.getEmployeeId(), 
		             allowedDoctorEmpIds);

		    // ✅ Step 3: Fetch previous consultations
		    long start = System.currentTimeMillis();
		    log.info("Fetching previous appointments Start: {}", start);

		    List<PateintPreviousAppointmentsDto> appointmentDetails =
		            hsDbRepo.fetchPreviousAppointmentsOfDoctor(allowedDoctorEmpIds, fromDate, toDate);

		    long timeTaken = System.currentTimeMillis() - start;
		    log.info("Fetching previous appointments End - Time taken: {}ms", timeTaken);

		    if (CollectionUtils.isEmpty(appointmentDetails)) {
		        throw new AigOneException("No appointments found for assigned doctors");
		    }

		    // ✅ Step 4: Add consultation status
		    List<Integer> billIds = appointmentDetails.stream()
		            .map(PateintPreviousAppointmentsDto::getBillId)
		            .collect(Collectors.toList());

		    List<OpConsultationEntity> opConsultations = opConsultationRepo.findAllByBillIdIn(billIds);

//		    Map<Integer, OpConsultationEntity> billIdToConsultation = opConsultations.stream()
//		            .collect(Collectors.toMap(OpConsultationEntity::getBillId, oc -> oc, (a, b) -> a));
		    
		    Map<Integer, OpConsultationEntity> billIdToLatestConsultation = opConsultations.stream()
		    	    .collect(Collectors.groupingBy(
		    	        OpConsultationEntity::getBillId,
		    	        Collectors.collectingAndThen(
		    	            Collectors.maxBy(Comparator.comparing(OpConsultationEntity::getCreationDate)),
		    	            optional -> optional.orElse(null)
		    	        )
		    	    ));


		    appointmentDetails.forEach(ad -> {
		        OpConsultationEntity entity = billIdToLatestConsultation.get(ad.getBillId());
		        ad.setConsultationStatus(entity != null && entity.getStatus() != null
		                ? entity.getStatus().name()
		                : "PENDING");
		    });

		    log.info("Mapping OpConsultation status End");

		    return appointmentDetails;
		}

		
		@Override
		public String fetchHisBase64ReportWithoutBlocking(ViewReportRequestDto viewReportDto) {
	    	if(viewReportDto.getHospCode()== null || viewReportDto.getHospCode().isEmpty()){
	    		viewReportDto.setHospCode("1100");
	    	}
		    return Mono.fromCallable(() -> {
		        PatientReportDetailsDto reportDetailsDto = new PatientReportDetailsDto();
		        HisLoginResponseDto login = self.login();
		        ViewReportHisResponseDto<ViewReportResponseDto> reportResponse = hisClient.post()
		                .uri(HisApiUtil.GET_BASE64_REPORT)
		                .contentType(MediaType.APPLICATION_JSON)
		                .header("Authorization", "Bearer " + login.getAccess_token())
		                .body(BodyInserters.fromValue(viewReportDto))
		                .retrieve()
		                .bodyToMono(new ParameterizedTypeReference<ViewReportHisResponseDto<ViewReportResponseDto>>() {})
		                .block();

		        if (reportResponse.getResponse() != null) {
		            log.info("Base64 report Response report Link::" + reportResponse.getResponse().getReportLink());
		        }

		        String base64Report = "";
		        if (reportResponse.isSucceeded() == false) {
		            base64Report = "";
		        } else {
		            if (reportResponse.getResponse().getReportBase64() != "") {
		                base64Report = reportResponse.getResponse().getReportBase64();
		                reportDetailsDto.setBase64(base64Report);
		            } else if (reportResponse.getResponse().getReportLink() != "") {
		                String reportLink = reportResponse.getResponse().getReportLink();
		                base64Report = fetchBase64FromUrl(reportLink);
		                reportDetailsDto.setBase64(base64Report);
		                if (reportLink != null) {
		                    String accessionnumber = StringUtils.substringBetween(reportLink,
		                            "http://risweb:84/SHARED/ReportFilePath/", ".pdf");
		                    reportDetailsDto.setPacsLink(
		                            "http://aigpacs/explore.asp?path=All%20Studies/accessionnumber=" + accessionnumber);
		                }
		            }
		        }
		        return reportDetailsDto.getBase64();
		    }).subscribeOn(Schedulers.boundedElastic()).block();
		}
		
		
		public Map<String, Object> getOPbillListFromUHID(PatientOpBillsListRequestDto requestDto) {
			Map<String, Object> response = new LinkedHashMap<>();

			
			try {
				log.info("Fetching OP bill list for UHID: {}, HospCode: {}", requestDto.getPatientID(),
						requestDto.getHospCode());

				HisLoginResponseDto login = self.login();
				if (login == null || login.getAccess_token() == null) {
					throw new IllegalStateException("Failed to obtain a valid access token from login.");
				}
				log.info("Successfully retrieved access token for HIS login.");

				response = hisClient.post().uri(HisApiUtil.PATIENT_OP_BILLS_LIST).contentType(MediaType.APPLICATION_JSON)
						.header("Authorization", "Bearer " + login.getAccess_token())
						.body(BodyInserters.fromValue(requestDto)).retrieve()
						.bodyToMono(new ParameterizedTypeReference<Map<String, Object>>() {
						}).block();

				log.info("Successfully fetched OP bill list for UHID: {}", requestDto.getPatientID());
			} catch (Exception ex) {
				log.error("Failed to fetch OP bill list for UHID: {} due to: {}", requestDto.getPatientID(),
						ex.getMessage(), ex);
				throw new IllegalStateException("Unable to retrieve OP bill list from HIS system.", ex);
			}
			return response;
		}

}
