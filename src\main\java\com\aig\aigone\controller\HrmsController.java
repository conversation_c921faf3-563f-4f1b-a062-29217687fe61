package com.aig.aigone.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.model.dto.hrms.AssociateFamilyDto;
import com.aig.aigone.model.entity.aigone.HrmsEmpDetailsForIcare;

import com.aig.aigone.service.HrmsService;

@RestController
@RequestMapping("/api/hrms")
public class HrmsController {
	
	@Autowired
	HrmsService hrmsService;
	
	
	
	
	@GetMapping("/familyDetails")
	public List<AssociateFamilyDto>  getAssociateFamilyDetails() {
		return hrmsService.getAssociateFamilyDetails();
	}
	
	@GetMapping("/empdetails/forIcare")
	public List<HrmsEmpDetailsForIcare> fetchEmpDetailsForIcare() {
		
		return hrmsService.getEmpDetailsForIcareImpl();
		
	}

}
