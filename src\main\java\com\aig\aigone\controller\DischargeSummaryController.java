package com.aig.aigone.controller;

import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.model.dto.DischargeSumPdfDto;
import com.aig.aigone.model.dto.DischargeSumPdfReqDto;
import com.aig.aigone.model.dto.DischargeSummaryDto;
import com.aig.aigone.model.dto.emr.PatientDataForDischargeSumDto;
import com.aig.aigone.model.entity.aigone.DischargeSumDrafts;
import com.aig.aigone.model.entity.aigone.DischargeSumPdf;
import com.aig.aigone.repository.aigone.DischargeSummaryForEmrRepo;
import com.aig.aigone.service.DischargeSummaryService;
import com.aig.aigone.service.impl.DishcargeSummarySvcImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;

import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/dischargeSum")
public class DischargeSummaryController {

	@Autowired
	DishcargeSummarySvcImpl dischargeSummarySvcImpl;

	@Autowired
	DischargeSummaryService disChargeSummaryService;

	@Autowired
	DischargeSummaryForEmrRepo dischargeSummaryForEmrRepo;

	@GetMapping("/drafts/{patientId}/{encounterId}")
	public List<DischargeSumDrafts> fetchDischargeSummaryDrafts(
			@PathVariable(name = "patientId", required = true) @NotBlank String patientId,
			@PathVariable(name = "encounterId", required = true) @NotBlank String encounterId) {

		List<DischargeSumDrafts> dischargeSumDraftsList = dischargeSummarySvcImpl
				.fetchDischargeSummaryDraftsImpl(patientId, encounterId);

		return dischargeSumDraftsList;

	}

	@GetMapping("/patientDetails/{patientId}/{encounterId}")
	public List<PatientDataForDischargeSumDto> fetchPatientDataForDischargeSumm(@PathVariable String patientId,
			@PathVariable String encounterId) {
		List<PatientDataForDischargeSumDto> listPatientDataForDischargeSumDto = dischargeSummarySvcImpl
				.fetchPatientDataForDischargeSummFromEmrImpl(patientId, encounterId);
		return listPatientDataForDischargeSumDto;

	}

	@PostMapping("/drafts/save")
	public DischargeSumDrafts saveDischargeSummaryDrafts(@RequestBody DischargeSumDrafts dischargeSumDrafts) {
		dischargeSummarySvcImpl.saveDischargeSummaryDraftsImpl(dischargeSumDrafts);
         log.info("hi");
		return dischargeSumDrafts;

	}

	@PostMapping("/drafts/update")
	public DischargeSumDrafts updateDischargeSummaryDrafts(@RequestBody DischargeSumDrafts dischargeSumDrafts) {
		dischargeSummarySvcImpl.updateDischargeSummaryDraftsImpl(dischargeSumDrafts);
		return dischargeSumDrafts;

	}

//	@DeleteMapping("/delete/{id}")
//	public void deleteDishargeSummDraft(@PathVariable Integer id) {
//		dischargeSummarySvcImpl.deleteDishargeSummDraftImpl(id);
//	}

	@PostMapping("/pdf/save")
	public DischargeSumPdf saveDischargeSummPdf(@RequestBody DischargeSumPdfReqDto dischargeSumPdfReqDto)
			throws JsonMappingException, JsonProcessingException {
		DischargeSumPdf dischargeSumPdf = dischargeSummarySvcImpl.saveDischargeSummPdfImpl(dischargeSumPdfReqDto);
		return dischargeSumPdf;
	}

	@GetMapping("/drafts/patientDetails/{patientId}/{encounterId}")
	public ResponseEntity<List<DischargeSumPdfDto>> fetchPatientData(@PathVariable String patientId,
			@PathVariable String encounterId) {

		log.info("Fetching discharge summary for Patient ID: {} and Encounter ID: {}", patientId, encounterId);
		List<DischargeSumPdfDto> dischargeSummaries = dischargeSummarySvcImpl.getDischargeSummary(patientId, encounterId);

		if (dischargeSummaries.isEmpty()) {
			return ResponseEntity.noContent().build();
		}

		return ResponseEntity.ok(dischargeSummaries);
	}

	@GetMapping("/fetch/pdf/{id}")
	public ResponseEntity<byte[]> fetchDischargeSummaryPdf(@PathVariable Integer id) {
		Optional<DischargeSumPdf> dischargeSumPdf = dischargeSummaryForEmrRepo.findById(id);

		if (!dischargeSumPdf.isPresent()) {
			return ResponseEntity.notFound().build();
		}

		DischargeSumPdf dischargeSumPdf1 = dischargeSumPdf.get();
		String base64 = dischargeSumPdf1.getBase64();

		if (base64 == null || base64.isEmpty()) {
			return ResponseEntity.badRequest().build();
		}

		byte[] pdfBytes = Base64.getDecoder().decode(base64);

		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_PDF);
		headers.setContentDispositionFormData("attachment", "discharge_summary.pdf");

		return ResponseEntity.ok().headers(headers).body(pdfBytes);
	}

//	@GetMapping("/getDischangaeSummary/{patientId}/{encounterId}")
//	public Map<String, Object> fetchDischargeSummaryFormImpl(@PathVariable String patientId,
//			@PathVariable String encounterId) {
//		return disChargeSummaryService.fetchDischargeSummaryFormImpl(patientId, encounterId);
//	}
	
	@GetMapping("/getDischangaeSummary/mapped/{patientId}/{encounterId}")
	public DischargeSummaryDto fetchDischargeSummarymapped(@PathVariable String patientId,
			@PathVariable String encounterId) {
		return disChargeSummaryService.fetchDischargeSummaryMapped(patientId, encounterId);
	}

	@DeleteMapping("/delete/{id}")
	public String softDelete(@PathVariable Integer id) {
		disChargeSummaryService.softDeleteDischargeSummary(id);
		return "Record soft deleted successfully";
	}

	@PutMapping("/update/{id}")
	public ResponseEntity<Object> updateDischargeSummary(@PathVariable Integer id,
			@RequestBody DischargeSumPdf dischargeSumPdf) {
		return ResponseEntity.ok(disChargeSummaryService.updateDischargeSummary(id, dischargeSumPdf));
	}

}
