package com.aig.aigone.queue.service.impl;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.client.RestTemplate;

import com.aig.aigone.common.AigOneUtil;
import com.aig.aigone.queue.apiUtil.QueueApiUtil;
import com.aig.aigone.queue.dto.AddUserToQueueResponseDto;
import com.aig.aigone.queue.dto.AllotedQueuesResponseDto;
import com.aig.aigone.queue.dto.CommonResponseDto;
import com.aig.aigone.queue.dto.CounterDetailsResponseDto;
import com.aig.aigone.queue.dto.GenericResponse;
import com.aig.aigone.queue.dto.GenericResponse1;
import com.aig.aigone.queue.dto.PatientQueueDto;
import com.aig.aigone.queue.dto.QRCodeDetailsRequestDto;
import com.aig.aigone.queue.dto.QueueWeightAgeDto;
import com.aig.aigone.queue.dto.TokenDetailsResponseDto;
import com.aig.aigone.queue.dto.TokenResponseDto;
import com.aig.aigone.queue.dto.UserQueueAssesmentRequestDto;
import com.aig.aigone.queue.dto.UserQueueDto;
import com.aig.aigone.queue.dto.UserServicesDto;
import com.aig.aigone.queue.dto.GenerateBarCodeResponseDto;
import com.aig.aigone.queue.service.QueueCacheService;
import com.aig.aigone.queue.service.QueueService;
import com.aig.aigone.service.CacheService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Scope(proxyMode = ScopedProxyMode.TARGET_CLASS)
public class QueueServiceImpl implements QueueService{
	
	@Value("${aig.queue.baseurl}")
	private String queueBaseUrl;
	
	@Value("${aig.queue.username}")
	private String queueUserName;
	
	@Value("${aig.queue.password}")
	private String queueUserpwd;
	
	@Autowired
	private QueueCacheService queueCacheService;
	
	@Autowired
	private CacheService cacheService;
	
	@Override
	public HttpHeaders getQueueAuthToken(String empId) {
		log.info("Queue Login called");
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("device_type", "STAFF");
        String authToken = "";
		try {
	        Map<String, Object> requestBody = new HashMap<>();
	        requestBody.put("query", QueueApiUtil.STAFF_AUTH_LOGIN);
	        
	        Map<String, Object> variables = new HashMap<>();
			variables.put("empId", empId);
			variables.put("superUserName", queueUserName);
			variables.put("superUserPwd", queueUserpwd);

			requestBody.put("variables", variables);
		        
	        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
	        RestTemplate restTemplate = new RestTemplate();
	        ResponseEntity<String> response = restTemplate.postForEntity(queueBaseUrl, entity, String.class);
	        ObjectMapper objectMapper = new ObjectMapper();
	        JsonNode rootNode = objectMapper.readTree(response.getBody());

	        authToken = rootNode.path("data").path("authTokenService").path("data").asText();
	        headers.set("authorization", "Bearer "+authToken);
	        
		} catch (RestClientResponseException e) {
			log.error(String.format("Request failed with response: %s", e.getStatusText()));
			return null;
	    }catch (Exception e) {
	    	e.printStackTrace();
	    	return null;
	    }
		return headers;
	}
	
	private ResponseEntity<String> makeApiCall(String query,String empId,Map<String,Object> variables) {

		HttpHeaders headers= queueCacheService.getQueueStaffLogin(empId);
//		log.info(""+headers);
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("query", query);
        if(variables !=null) {
        	requestBody.put("variables", variables);
        }
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
        RestTemplate restTemplate = new RestTemplate();
        try {
            return restTemplate.postForEntity(queueBaseUrl, entity, String.class);
        } catch (HttpClientErrorException e) {
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                log.warn("401 Unauthorized! Refreshing token and retrying...");
                headers = cacheService.updateQueueStaffLogin(AigOneUtil.CACHE_QUEUE_LOGIN,"getQueueStaffLogin,"+empId,empId);
                log.info(""+headers);
 
                entity = new HttpEntity<>(requestBody, headers);
                return restTemplate.postForEntity(queueBaseUrl, entity, String.class);
            } else {
                throw e;
            }
        }
       
    }	
			
	private <T> GenericResponse<T> getFinalData(ResponseEntity<String> response, String methodName, Class<T> clazz) {
	    try {
	        GenericResponse<T> finalResponse = new GenericResponse<T>();
	        ObjectMapper objectMapper = new ObjectMapper();
	        JsonNode rootNode = objectMapper.readTree(response.getBody());
	        JsonNode dataNode = rootNode.path("data");
 
	        if (dataNode.has(methodName)) {
	            Map<String, Map<String, GenericResponse<T>>> responseMap =
	                objectMapper.readValue(response.getBody(), new TypeReference<Map<String, Map<String, GenericResponse<T>>>>() {});
	            finalResponse = responseMap.get("data").get(methodName);
	        } else {
	            Map<String, GenericResponse<T>> responseMap =
	                objectMapper.readValue(response.getBody(), new TypeReference<Map<String, GenericResponse<T>>>() {});
	            finalResponse = responseMap.get("data");
	        }
 
	        return finalResponse;
	    } catch (Exception e) {
	        e.printStackTrace();
	        return null;
	    }
	}
	
	private <T> GenericResponse1<T> getFinalData1(ResponseEntity<String> response, String methodName, Class<T> clazz) {
	    try {
	        GenericResponse1<T> finalResponse = new GenericResponse1<T>();
	        ObjectMapper objectMapper = new ObjectMapper();
	        JsonNode rootNode = objectMapper.readTree(response.getBody());
	        JsonNode dataNode = rootNode.path("data");

	        if (dataNode.has(methodName)) {
	            Map<String, Map<String, GenericResponse1<T>>> responseMap =
	                objectMapper.readValue(response.getBody(), new TypeReference<Map<String, Map<String, GenericResponse1<T>>>>() {});
	            finalResponse = responseMap.get("data").get(methodName);
	        } else {
	            Map<String, GenericResponse1<T>> responseMap =
	                objectMapper.readValue(response.getBody(), new TypeReference<Map<String, GenericResponse1<T>>>() {});
	            finalResponse = responseMap.get("data");
	        }

	        return finalResponse;
	    } catch (Exception e) {
	        e.printStackTrace();
	        return null;
	    }
	}

	private <T> GenericResponse<List<T>> getFinalDataList(ResponseEntity<String> response, String methodName, Class<T> clazz) {
	    try {
	        GenericResponse<List<T>> finalResponse = new GenericResponse<List<T>>();
	        ObjectMapper objectMapper = new ObjectMapper();
	        JsonNode rootNode = objectMapper.readTree(response.getBody());
	        JsonNode dataNode = rootNode.path("data");

	        if (dataNode.has(methodName)) {
	            Map<String, Map<String, GenericResponse<List<T>>>> responseMap =
	                objectMapper.readValue(response.getBody(), new TypeReference<Map<String, Map<String, GenericResponse<List<T>>>>>() {});
	            finalResponse = responseMap.get("data").get(methodName);
	        } else {
	            Map<String, GenericResponse<List<T>>> responseMap =
	                objectMapper.readValue(response.getBody(), new TypeReference<Map<String, GenericResponse<List<T>>>>() {});
	            finalResponse = responseMap.get("data");
	        }

	        return finalResponse;
	    } catch (Exception e) {
	        e.printStackTrace();
	        return null;
	    }
	}
	
	@Override
	public GenericResponse<AllotedQueuesResponseDto> fetchStaffUserAllocatedQueues(String empId) {
		
		try {
			ResponseEntity<String> response = makeApiCall(QueueApiUtil.STAFF_ALLOCATED_QUEUES,empId,null);
			return getFinalData(response,"getStaffUserAllocatedQueues",AllotedQueuesResponseDto.class);
		} catch (HttpClientErrorException e) { 
	        return new GenericResponse<>(false, "Client Error: " + e.getStatusText(), null);
	    } catch (HttpServerErrorException e) {
	        return new GenericResponse<>(false, "Server Error: " + e.getStatusText(), null);
	    } catch (Exception e) {
	    	e.printStackTrace();
	        return new GenericResponse<>(false, "An unexpected error occurred: " + e.getMessage(), null);
	    }
		
	}
	
	@Override
	public GenericResponse1<AllotedQueuesResponseDto> getQueueDetails(String empId, String queueCode) {
		
		try {
			Map<String, Object> variables = new HashMap<>();
			variables.put("queueCode", queueCode);
			ResponseEntity<String> response = makeApiCall(QueueApiUtil.GET_QUEUE_DETAILS,empId,variables);
			return getFinalData1(response,"getQueueDetails",AllotedQueuesResponseDto.class);
		} catch (HttpClientErrorException e) { 
	        return new GenericResponse1<>(false, "Client Error: " + e.getStatusText(), null);
	    } catch (HttpServerErrorException e) {
	        return new GenericResponse1<>(false, "Server Error: " + e.getStatusText(), null);
	    } catch (Exception e) {
	    	e.printStackTrace();
	        return new GenericResponse1<>(false, "An unexpected error occurred: " + e.getMessage(), null);
	    }
		
	}
	
	@Override
	public GenericResponse1<PatientQueueDto> getPatientQueue(String empId, String uhId) {
		
		try {
			Map<String, Object> variables = new HashMap<>();
			variables.put("uhid", uhId);
			ResponseEntity<String> response = makeApiCall(QueueApiUtil.GET_PATIENT_QUEUE,empId,variables);
			return getFinalData1(response,"getPatientQueue",PatientQueueDto.class);
		} catch (HttpClientErrorException e) { 
	        return new GenericResponse1<>(false, "Client Error: " + e.getStatusText(), null);
	    } catch (HttpServerErrorException e) {
	        return new GenericResponse1<>(false, "Server Error: " + e.getStatusText(), null);
	    } catch (Exception e) {
	    	e.printStackTrace();
	        return new GenericResponse1<>(false, "An unexpected error occurred: " + e.getMessage(), null);
	    }
		
	}
	
	@Override
	public GenericResponse<QueueWeightAgeDto> fetchQueueWeightage(String empId) {

		try {
			ResponseEntity<String> response = makeApiCall(QueueApiUtil.QUEUE_WEIGHT_AGE,empId,null);
			return getFinalData(response,"getQueueWeightage",QueueWeightAgeDto.class);
		} catch (HttpClientErrorException e) { 
	        return new GenericResponse<>(false, "Client Error: " + e.getStatusText(), null);
	    } catch (HttpServerErrorException e) {
	        return new GenericResponse<>(false, "Server Error: " + e.getStatusText(), null);
	    } catch (Exception e) {
	    	e.printStackTrace();
	        return new GenericResponse<>(false, "An unexpected error occurred: " + e.getMessage(), null);
	    }
		
	}
	
	@Override
	public GenericResponse<AddUserToQueueResponseDto> getUserQueue(String empId,String preCheckStatus,Long queueId, List<Long> queueIds, String assignmentOption, String queueCode) {
		try {
			Map<String, Object> variables = new HashMap<>();
			variables.put("preCheckStatus", preCheckStatus);
			variables.put("queueId", queueId);
			variables.put("queueIds", queueIds);
			variables.put("queueCode", queueCode);
			variables.put("assignmentOption", assignmentOption);

			ResponseEntity<String> response = makeApiCall(QueueApiUtil.GET_USER_QUEUE,empId,variables);
			return getFinalData(response,"getUserQueue",AddUserToQueueResponseDto.class);
	            
		} catch (HttpClientErrorException e) { 
	        return new GenericResponse<>(false, "Client Error: " + e.getStatusText(), null);
	    } catch (HttpServerErrorException e) {
	        return new GenericResponse<>(false, "Server Error: " + e.getStatusText(), null);
	    } catch (Exception e) {
	    	e.printStackTrace();
	        return new GenericResponse<>(false, "An unexpected error occurred: " + e.getMessage(), null);
	    }
		
	}


	@Override
	public GenericResponse<AddUserToQueueResponseDto> updateUserQueueWeightage(String empId,String queueWeightageAction,
			Long queueWeightageActionId, Long userQueueId,String remarks) {
		try {
			Map<String, Object> variables = new HashMap<>();
			variables.put("queueWeightageAction", queueWeightageAction);
			variables.put("queueWeightageActionId", queueWeightageActionId);
			variables.put("userQueueId", userQueueId);
			variables.put("remarks",remarks);

			ResponseEntity<String> response = makeApiCall(QueueApiUtil.UPDATE_USER_QUEUE,empId,variables);
			return getFinalData(response,"updateUserQueue",AddUserToQueueResponseDto.class);

	            
		} catch (HttpClientErrorException e) { 
	        return new GenericResponse<>(false, "Client Error: " + e.getStatusText(), null);
	    } catch (HttpServerErrorException e) {
	        return new GenericResponse<>(false, "Server Error: " + e.getStatusText(), null);
	    } catch (Exception e) {
	    	e.printStackTrace();
	        return new GenericResponse<>(false, "An unexpected error occurred: " + e.getMessage(), null);
	    }
	}
		

	@Override
	public GenericResponse<UserServicesDto> fetchUserServices(String empId,Long userQueueId) {
		try {
			Map<String, Object> variables = new HashMap<>();
			variables.put("userQueueId", userQueueId);

			ResponseEntity<String> response = makeApiCall(QueueApiUtil.GET_USER_SERVICES,empId,variables);
			return getFinalData(response,"getUserServices",UserServicesDto.class);
	            
		} catch (HttpClientErrorException e) { 
	        return new GenericResponse<>(false, "Client Error: " + e.getStatusText(), null);
	    } catch (HttpServerErrorException e) {
	        return new GenericResponse<>(false, "Server Error: " + e.getStatusText(), null);
	    } catch (Exception e) {
	    	e.printStackTrace();
	        return new GenericResponse<>(false, "An unexpected error occurred: " + e.getMessage(), null);
	    }
		
	}
	
	@Override
	public GenericResponse<AddUserToQueueResponseDto> addUserToQueue(String empId,Long queueId,QRCodeDetailsRequestDto qrDetails) {
		try {
			Map<String, Object> variables = new HashMap<>();
			variables.put("queueId", queueId);
			variables.put("tokenId", qrDetails.getTokenId());
			variables.put("uhid", qrDetails.getUhid());
			variables.put("prerequisitesConditions", qrDetails.getPrerequisitesConditions());

			ResponseEntity<String> response = makeApiCall(QueueApiUtil.ADD_USER_QUEUE,empId,variables);
			return getFinalData(response,"addUserQueue",AddUserToQueueResponseDto.class);

		} catch (HttpClientErrorException e) { 
	        return new GenericResponse<>(false, "Client Error: " + e.getStatusText(), null);
	    } catch (HttpServerErrorException e) {
	        return new GenericResponse<>(false, "Server Error: " + e.getStatusText(), null);
	    } catch (Exception e) {
	    	e.printStackTrace();
	        return new GenericResponse<>(false, "An unexpected error occurred: " + e.getMessage(), null);
	    }
		
	}
	
	@Override
	public GenericResponse<CommonResponseDto> callNextSemiAuto(String empId,Long queueId,QRCodeDetailsRequestDto qrDetails) {
		try {
			Map<String, Object> variables = new HashMap<>();
			variables.put("queueId", queueId);
			variables.put("prerequisitesConditions", qrDetails.getPrerequisitesConditions());
			variables.put("uhid", qrDetails.getUhid());
			variables.put("userServiceIds", qrDetails.getUserServiceIds());
			variables.put("tokenId", qrDetails.getTokenId());
			variables.put("userQueueId", qrDetails.getUserQueueId());
			ResponseEntity<String> response = makeApiCall(QueueApiUtil.EXIT_USER_QUEUE,empId,variables);
			return getFinalData(response,"exitUserQueue",CommonResponseDto.class);

		} catch (HttpClientErrorException e) { 
	        return new GenericResponse<>(false, "Client Error: " + e.getStatusText(), null);
	    } catch (HttpServerErrorException e) {
	        return new GenericResponse<>(false, "Server Error: " + e.getStatusText(), null);
	    } catch (Exception e) {
	    	e.printStackTrace();
	        return new GenericResponse<>(false, "An unexpected error occurred: " + e.getMessage(), null);
	    }
		
	}
		
	@Override
	public GenericResponse1<TokenDetailsResponseDto> fetchTokenDetails(String uhId) {
		try {
			Map<String, Object> variables = new HashMap<>();
			variables.put("uhid", uhId);
			ResponseEntity<String> response = makeApiCall(QueueApiUtil.FETCH_TOKEN_DETAILS_BY_UHID,queueUserName,variables);
			return getFinalData1(response,"getUserQueueMsg",TokenDetailsResponseDto.class);
		} catch (HttpClientErrorException e) { 
	        return new GenericResponse1<>(false, "Client Error: " + e.getStatusText(), null);
	    } catch (HttpServerErrorException e) {
	        return new GenericResponse1<>(false, "Server Error: " + e.getStatusText(), null);
	    } catch (Exception e) {
	    	e.printStackTrace();
	        return new GenericResponse1<>(false, "An unexpected error occurred: " + e.getMessage(), null);
	    }
		
	}

	@Override
	public GenericResponse<CommonResponseDto> updateUserQueueAssesment(String empId,
			UserQueueAssesmentRequestDto details) {
		try {
			Map<String, Object> variables = new HashMap<>();
			variables.put("userQueueId", details.getUserQueueId());
			variables.put("updateType", details.getUpdateType());
			ResponseEntity<String> response = makeApiCall(QueueApiUtil.UPDATE_USER_QUEUE_ASSESMENT,empId,variables);
			return getFinalData(response,"updateUserQueueTimestamp",CommonResponseDto.class);
		} catch (HttpClientErrorException e) { 
	        return new GenericResponse<>(false, "Client Error: " + e.getStatusText(), null);
	    } catch (HttpServerErrorException e) {
	        return new GenericResponse<>(false, "Server Error: " + e.getStatusText(), null);
	    } catch (Exception e) {
	    	e.printStackTrace();
	        return new GenericResponse<>(false, "An unexpected error occurred: " + e.getMessage(), null);
	    }
		
	}
	
	@Override
	public GenericResponse<String> getCommonRemarks(String empId,String type) {
		try {
			Map<String, Object> variables = new HashMap<>();
			variables.put("type", type);
			ResponseEntity<String> response = makeApiCall(QueueApiUtil.GET_COMMON_REMARKS,empId,variables);
			return getFinalData(response,"getCommonRemarks",String.class);
		} catch (HttpClientErrorException e) { 
	        return new GenericResponse<>(false, "Client Error: " + e.getStatusText(), null);
	    } catch (HttpServerErrorException e) {
	        return new GenericResponse<>(false, "Server Error: " + e.getStatusText(), null);
	    } catch (Exception e) {
	    	e.printStackTrace();
	        return new GenericResponse<>(false, "An unexpected error occurred: " + e.getMessage(), null);
	    }
		
	}
	
	@Override
	public GenericResponse1<AddUserToQueueResponseDto> callNextPreCheck(String empId,Long queueId) {
		try {
			Map<String, Object> variables = new HashMap<>();
			variables.put("queueId", queueId);
			ResponseEntity<String> response = makeApiCall(QueueApiUtil.PRE_CHECK_CALL_NEXT,empId,variables);
			return getFinalData1(response,"callNextPreCheck",AddUserToQueueResponseDto.class);
		} catch (HttpClientErrorException e) { 
	        return new GenericResponse1<>(false, "Client Error: " + e.getStatusText(), null);
	    } catch (HttpServerErrorException e) {
	        return new GenericResponse1<>(false, "Server Error: " + e.getStatusText(), null);
	    } catch (Exception e) {
	    	e.printStackTrace();
	        return new GenericResponse1<>(false, "An unexpected error occurred: " + e.getMessage(), null);
	    }
	}
	
	@Override
	public GenericResponse1<CounterDetailsResponseDto> getCounterDetails(String empId,Long queueCounterId) {
		try {
			Map<String, Object> variables = new HashMap<>();
			variables.put("queueCounterId", queueCounterId);
			ResponseEntity<String> response = makeApiCall(QueueApiUtil.GET_COUNTER_DETAILS,empId,variables);
			return getFinalData1(response,"getCounterDetails",CounterDetailsResponseDto.class);
		} catch (HttpClientErrorException e) { 
	        return new GenericResponse1<>(false, "Client Error: " + e.getStatusText(), null);
	    } catch (HttpServerErrorException e) {
	        return new GenericResponse1<>(false, "Server Error: " + e.getStatusText(), null);
	    } catch (Exception e) {
	    	e.printStackTrace();
	        return new GenericResponse1<>(false, "An unexpected error occurred: " + e.getMessage(), null);
	    }
	}
	
	@Override
	public GenericResponse<CounterDetailsResponseDto> getUserQueueCounters(String empId,Long queueId) {
		try {
			Map<String, Object> variables = new HashMap<>();
			variables.put("queueId", queueId);
			ResponseEntity<String> response = makeApiCall(QueueApiUtil.GET_USER_COUNTERS,empId,variables);
			return getFinalData(response,"getUserQueueCounters",CounterDetailsResponseDto.class);
		} catch (HttpClientErrorException e) { 
	        return new GenericResponse<>(false, "Client Error: " + e.getStatusText(), null);
	    } catch (HttpServerErrorException e) {
	        return new GenericResponse<>(false, "Server Error: " + e.getStatusText(), null);
	    } catch (Exception e) {
	    	e.printStackTrace();
	        return new GenericResponse<>(false, "An unexpected error occurred: " + e.getMessage(), null);
	    }
	}
	
	@Override
	public GenericResponse1<String> updateCounter(String empId,Long queueCounterId, String status) {
		try {
			Map<String, Object> variables = new HashMap<>();
			variables.put("counterId", queueCounterId);
			variables.put("status", status);
			ResponseEntity<String> response = makeApiCall(QueueApiUtil.UPDATE_COUNTER,empId,variables);
			return getFinalData1(response,"updateCounter",String.class);
		} catch (HttpClientErrorException e) { 
	        return new GenericResponse1<>(false, "Client Error: " + e.getStatusText(), null);
	    } catch (HttpServerErrorException e) {
	        return new GenericResponse1<>(false, "Server Error: " + e.getStatusText(), null);
	    } catch (Exception e) {
	    	e.printStackTrace();
	        return new GenericResponse1<>(false, "An unexpected error occurred: " + e.getMessage(), null);
	    }
	}
	
	
	@Override
	public GenericResponse<UserQueueDto> getUserQueueAll(String empId,String queueCode) {
		try {
			Map<String, Object> variables = new HashMap<>();
			variables.put("queueCode", queueCode);
			ResponseEntity<String> response = makeApiCall(QueueApiUtil.GET_USER_QUEUE_ALL,empId,variables);
			return getFinalData(response,"getUserQueueAll",UserQueueDto.class);
		} catch (HttpClientErrorException e) { 
	        return new GenericResponse<>(false, "Client Error: " + e.getStatusText(), null);
	    } catch (HttpServerErrorException e) {
	        return new GenericResponse<>(false, "Server Error: " + e.getStatusText(), null);
	    } catch (Exception e) {
	    	e.printStackTrace();
	        return new GenericResponse<>(false, "An unexpected error occurred: " + e.getMessage(), null);
	    }
	}
	
	
	@Override
	public GenericResponse1<String> updatePreCheckStatus(String empId, Long userQueueId, List<Long> userServiceIds) {
		try {
			Map<String, Object> variables = new HashMap<>();
			variables.put("userQueueId", userQueueId);
			variables.put("userServiceIds", userServiceIds);
			ResponseEntity<String> response = makeApiCall(QueueApiUtil.UPDATE_PRE_CHECK,empId,variables);
			return getFinalData1(response,"updatePreCheckStatus",String.class);
		} catch (HttpClientErrorException e) { 
	        return new GenericResponse1<>(false, "Client Error: " + e.getStatusText(), null);
	    } catch (HttpServerErrorException e) {
	        return new GenericResponse1<>(false, "Server Error: " + e.getStatusText(), null);
	    } catch (Exception e) {
	    	e.printStackTrace();
	        return new GenericResponse1<>(false, "An unexpected error occurred: " + e.getMessage(), null);
	    }
	}
	
	@Override
	public GenericResponse1<TokenResponseDto> getTokenDetails(String empId,Long tokenId) {
		try {
			Map<String, Object> variables = new HashMap<>();
			variables.put("tokenId", tokenId);
			ResponseEntity<String> response = makeApiCall(QueueApiUtil.GET_TOKEN_DETAILS,empId,variables);
			return getFinalData1(response,"getTokenDetails",TokenResponseDto.class);
		} catch (HttpClientErrorException e) { 
	        return new GenericResponse1<>(false, "Client Error: " + e.getStatusText(), null);
	    } catch (HttpServerErrorException e) {
	        return new GenericResponse1<>(false, "Server Error: " + e.getStatusText(), null);
	    } catch (Exception e) {
	    	e.printStackTrace();
	        return new GenericResponse1<>(false, "An unexpected error occurred: " + e.getMessage(), null);
	    }
	}
	
	@Override
	public GenericResponse<String> getStaffUserAllocatedRescouce(String employeeId, List<String> resources) {
		try {
			Map<String, Object> variables = new HashMap<>();
			variables.put("resourceCodes", resources);
			ResponseEntity<String> response = makeApiCall(QueueApiUtil.GET_STAFF_USER_ALLOCATED_RESOURCES,employeeId,variables);
			return getFinalData(response,"getStaffUserAllocatedRescouce",String.class);
		} catch (HttpClientErrorException e) {
	        return new GenericResponse<>(false, "Client Error: " + e.getStatusText(), null);
	    } catch (HttpServerErrorException e) {
	        return new GenericResponse<>(false, "Server Error: " + e.getStatusText(), null);
	    } catch (Exception e) {
	    	e.printStackTrace();
	        return new GenericResponse<>(false, "An unexpected error occurred: " + e.getMessage(), null);
	    }
	}

	@Override
	public GenericResponse<List<GenerateBarCodeResponseDto>> generateBarCode(String employeeId, List<Long> userServiceIds) {
		try {
			Map<String, Object> variables = new HashMap<>();
			variables.put("userServiceIds", userServiceIds);
			ResponseEntity<String> response = makeApiCall(QueueApiUtil.GENERATE_BAR_CODE, employeeId, variables);
			return getFinalDataList(response, "generateBarCode", GenerateBarCodeResponseDto.class);
		} catch (HttpClientErrorException e) {
	        return new GenericResponse<>(false, "Client Error: " + e.getStatusText(), null);
	    } catch (HttpServerErrorException e) {
	        return new GenericResponse<>(false, "Server Error: " + e.getStatusText(), null);
	    } catch (Exception e) {
	    	e.printStackTrace();
	        return new GenericResponse<>(false, "An unexpected error occurred: " + e.getMessage(), null);
	    }
	}
}
