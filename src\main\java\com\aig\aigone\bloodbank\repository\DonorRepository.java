package com.aig.aigone.bloodbank.repository;

import com.aig.aigone.bloodbank.model.dto.DonorBasicInfoDTO;
import com.aig.aigone.bloodbank.model.entity.DonorEntity;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.ListCrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface DonorRepository extends ListCrudRepository<DonorEntity, Long> {
	Optional<DonorEntity> findByEmployeeId(String employeeId);

	List<DonorEntity> findAll();
    @Query("SELECT d FROM DonorEntity d WHERE d.lastDonationDate <= :cutoffDate OR d.lastDonationDate IS NULL")
    List<DonorEntity> findEligibleDonors(@Param("cutoffDate") LocalDate cutoffDate);
    
    @Modifying(clearAutomatically = true)
    @Query(value = "DELETE FROM donors WHERE employee_id = :empId", nativeQuery = true)
    void deleteByEmployeeId(@Param("empId") String empId);


}


