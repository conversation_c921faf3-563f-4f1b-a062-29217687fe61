package com.aig.aigone.config;

import java.util.HashMap;
import java.util.Map;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;

import com.zaxxer.hikari.HikariDataSource;

@Configuration
@EnableJpaRepositories(basePackages = {"com.aig.aigone.repository.hrms"},entityManagerFactoryRef = "hrmsEntityManager", transactionManagerRef = "hrmsTransactionManager")
public class HrmsDbConfig {


    @Bean(name = "hrmsDataSourceProperties")
    @ConfigurationProperties(prefix = "hrms.datasource")
    DataSourceProperties hrmsDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean(name = "hrmsDataSource")
    DataSource hrmsDataSource(@Qualifier("hrmsDataSourceProperties") DataSourceProperties hrmsDataSourceProperties) {
        return hrmsDataSourceProperties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
    }
    
    @Bean
    LocalContainerEntityManagerFactoryBean hrmsEntityManager(EntityManagerFactoryBuilder builder,@Qualifier("hrmsDataSource") DataSource hrmsDataSource) {
    	
    	Map<String, Object> properties = new HashMap<>();
        properties.put("hibernate.hbm2ddl.auto", "none");
//        properties.put("hibernate.dialect", "org.hibernate.dialect.PostgreSQLDialect");
        properties.put("hibernate.properties.hibernate.query.plan_cache_max_size", "1");
        properties.put("hibernate.properties.hibernate.plan_parameter_metadata_max_size", "1");
//        properties.put("hibernate.naming.physical-strategy", "org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy");
//        properties.put("hibernate.naming.implicit-strategy", "org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy");
        
        return builder.dataSource(hrmsDataSource)
                .packages("com.aig.aigone.repository.hrms")
                .persistenceUnit("hrmsDb")
                .properties(properties)
                .build();
    }
 
    @Bean
    PlatformTransactionManager hrmsTransactionManager(LocalContainerEntityManagerFactoryBean hrmsEntityManager) {
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(hrmsEntityManager.getObject());
        return transactionManager;
    }

}