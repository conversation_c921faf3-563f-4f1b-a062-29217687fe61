package com.aig.aigone.controller;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.aig.aigone.model.dto.DoctorsRankingAndVisibilityResponseDto;
import com.aig.aigone.model.dto.MitrPateintAppointmentsDto;
import com.aig.aigone.model.dto.MitrRankingRequestDto;
import com.aig.aigone.model.dto.MitrVisibilityRequestDto;
import com.aig.aigone.model.dto.PresciptionsViewsRequestDto;
import com.aig.aigone.model.dto.ViewOPbillListFromUHIDRequestDto;
import com.aig.aigone.model.dto.his.HisResponseListDto;
import com.aig.aigone.model.dto.his.OrderDto;
import com.aig.aigone.model.dto.his.OrganizationDto;
import com.aig.aigone.model.dto.his.PateintPreviousAppointmentsMitrDto;
import com.aig.aigone.model.dto.his.PatientPrescriptionsResponseDto;
import com.aig.aigone.model.dto.his.PatientReportsResponseDto;
import com.aig.aigone.model.dto.hismaster.DoctorResponseImagesDto;
import com.aig.aigone.model.entity.aigone.MitrDoctorDetails;
import com.aig.aigone.model.entity.aigone.MitrPaymentOrders;
import com.aig.aigone.model.entity.aigone.MitrSpecialityDetails;
import com.aig.aigone.model.entity.aigone.MitraPaymentDetail;
import com.aig.aigone.service.DepartmentService;
import com.razorpay.RazorpayException;

import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
@Slf4j
@RestController
@CrossOrigin("*")
@RequestMapping("/department")
public class DepartmentController {

	@Autowired
	private DepartmentService departmentService;
	
	@Operation(summary = "fetch master Organization ")
	@GetMapping("/mitr/fetchAllOrganization")
	public List<OrganizationDto> fetchAllOrganization() {
		return departmentService.fetchAllOrganization();
	}

	@Operation(summary = "fetch master specilaites By Location ")
	@GetMapping("/mitr/specialitiesIcons/{hosLocation}")
	public List<MitrSpecialityDetails> fetchMasterSpecialitiesIcons( @PathVariable(name = "hosLocation",required = true) String hosLocation) {
		return departmentService.fetchMasterSpecialitiesIcons(hosLocation);
	}

	@Operation(summary = "fetch doctor details consultants ")
	@GetMapping("/mitr/doctorById/{doctorCode}")
	public DoctorResponseImagesDto fetchByDoctorsId(
			@PathVariable(name = "doctorCode", required = true) String doctorCode) {
		return departmentService.fetchByDoctorsId(doctorCode);
	}

	@Operation(summary = "fetch speciality consultants ")
	@GetMapping("/mitr/doctor/{speciality}")
	public List<DoctorResponseImagesDto> fetchSpecialityDoctorsImages(
			@PathVariable(name = "speciality", required = true) String speciality) {
		return departmentService.fetchSpecialityDoctorsImages(speciality);
	}

	@PostMapping("/create-order")
	public ResponseEntity<MitrPaymentOrders> createOrders(@RequestBody MitrPaymentOrders mitrPaymentOrders)
			throws RazorpayException {
		MitrPaymentOrders createOrders = departmentService.createOrders(mitrPaymentOrders);
		return new ResponseEntity<>(createOrders, HttpStatus.CREATED);
	}

	@PostMapping("/verify-payment")
	public ResponseEntity<String> verifyPayment(@RequestBody MitraPaymentDetail paymentDetails) {
		boolean isVerified = departmentService.verifyPaymentSignature(paymentDetails);
		if (isVerified) {
			return ResponseEntity.ok("Payment Verified");
		} else {
			return ResponseEntity.status(400).body("Payment Verification Failed");
		}
	}

//	@PostMapping("/razorpay")
//	public ResponseEntity<String> logRequest(@RequestBody Map<String, Object> payload, HttpServletRequest request)
//			throws RazorpayException {
//		Enumeration<String> headerNames = request.getHeaderNames();
//
////	    while (headerNames.hasMoreElements()) {
//		String headerName = headerNames.nextElement();
//		departmentService.processWebhook(payload, headerNames.toString());
//
////	    }System.out.println("Payload: " + payload);
//		return ResponseEntity.ok("Logged successfully");
//	}

	@Operation(summary = "fetch patient prescriptions")
	@GetMapping("/presciptions/{hospCode}/{uhId}")
	public HisResponseListDto<PatientPrescriptionsResponseDto> fetchPatientPrescriptions(
			@PathVariable(name = "hospCode", required = true) String hospCode,
			@PathVariable(name = "uhId", required = true) String uhId) {
		return departmentService.fetchPatientPrescriptions(hospCode, uhId.toUpperCase());
	}

	@Operation(summary = "fetch patient prescriptions in Base64")
	@PostMapping("/presciptions/views")
	public String fetchPatientPrescriptionsBase64(
			@RequestBody PresciptionsViewsRequestDto presciptionsViewsRequestDto) {
		return departmentService.fetchPatientPrescriptionsBase64(presciptionsViewsRequestDto.getHospCode(),
				presciptionsViewsRequestDto.getUhId().toUpperCase(), presciptionsViewsRequestDto.getVisitID());

	}

	@Operation(summary = "fetch opbills in Base64")
	@PostMapping("/viewOPbillListFromUHID")
	public List<String> ViewOPbillListFromUHIDN(
			@RequestBody ViewOPbillListFromUHIDRequestDto viewOPbillListFromUHIDRequestDto) {
		return departmentService.ViewOPbillListFromUHIDN(viewOPbillListFromUHIDRequestDto.getHospCode(),
				viewOPbillListFromUHIDRequestDto.getUhId().toUpperCase(), viewOPbillListFromUHIDRequestDto.getBillNo());

	}

//	@PostMapping("/generatehashdata")
//	public PayUHashResponseDto generateHashDataPayU(@RequestBody Map<String, String> requestBody) {
//		PayUHashResponseDto generateHashDataPayU = departmentService.generateHashDataPayU(requestBody);
//		return generateHashDataPayU;
//	}

//	@PostMapping("/payu/status")
//	public String payUPaystatus(@RequestBody Map<String, Object> paymentData) throws IOException {
//		String payUPaystatus = departmentService.payUPaystatus(paymentData);
//		return payUPaystatus;
//	}

	@Operation(summary = "fetch patient reports")
	@GetMapping("/reports/{uhId}")
	public HisResponseListDto<PatientReportsResponseDto> fetchPatientReports(
			@PathVariable(name = "uhId", required = true) String uhId) {
		return departmentService.fetchPatientReports(uhId.toUpperCase());
	}

	@Operation(summary = "fetch Lab Orders ")
	@GetMapping("/lab/orders/{uhId}")
	public List<OrderDto> labOrders(@PathVariable(name = "uhId", required = true) String uhId) {
		return departmentService.fetchLabOrdersByUhId(uhId);
	}

	@Operation(summary = "Upload Doctor Image and details In Admin console")
	@PostMapping("/uploadDocImage")
	public ResponseEntity<String> uploadDocImage(@RequestParam("file") MultipartFile file,
			@RequestParam("doctorCode") String doctorCode) {
		try {
			String fileUrl = departmentService.uploadDocImage(file, doctorCode);
			return ResponseEntity.ok(fileUrl);
		} catch (Exception e) {
			return ResponseEntity.internalServerError().body("Error uploading file: " + e.getMessage());
		}
	}

	
	@Operation(summary = "fetch Patient Previous Appointments")
	@GetMapping("/previous/appointments/{uhId}")
	public List<PateintPreviousAppointmentsMitrDto> previousAppointments(@PathVariable(name = "uhId", required = true) String uhId) {
		return departmentService.fetchPreviousAppointmentsByUhId(uhId);
	}
	
	@Operation(summary = "Updating the Doctor  Ranking by location and speciality")
	@PostMapping("/UpdateRanking")
	public String UpdateRanking(@RequestBody MitrRankingRequestDto mitrRankingRequestDto) {
		 String updateVisibility = departmentService.updateRanking(mitrRankingRequestDto);
		return updateVisibility;
	}

	@Operation(summary = "Getting All doctors Records")
	@GetMapping("/getDoctorsList")
	public ResponseEntity<List<DoctorsRankingAndVisibilityResponseDto>> getDoctorsList(){
		return ResponseEntity.ok(departmentService.getDoctorsList());
	}
	
	
	@Operation(summary = "Updating the Visibility by locations",description = "Updating the Visibility by locations for Doctor in different locations in Admin Console.")
	@PutMapping("/updateVisibility")
	public ResponseEntity<String> updateDoctorVisibility(@RequestBody MitrVisibilityRequestDto visibilityRequestDto) {
		return ResponseEntity.ok(departmentService.updateDoctorVisibility(visibilityRequestDto));
	}
	
//	@Operation(summary = "Upload Doctor Image and details")
//	@PostMapping("/uploadDocImage")
//	public ResponseEntity<String> getDoctorsByLocationAndSpecialiy(
//			@PathVariable(name = "specialityCode", required = true)String specialityCode,@PathVariable(name = "hosLocation", required = true) String hosLocation) {
//		try {
//			String fileUrl = departmentService.getDoctorsByLocationAndSpecialiy(specialityCode, hosLocation);
//			return ResponseEntity.ok(fileUrl);
//		} catch (Exception e) {
//			return ResponseEntity.internalServerError().body("Error uploading file: " + e.getMessage());
//		}
//	}

	 @Operation(summary = "Updating the speciality ranking", description = "Updating the speciality ranking for Doctors in different locations in Admin Console.")
	 @PutMapping("/updateSpecialityRank")
	 public ResponseEntity<String> updateRankingForSpeciality(@RequestParam("specialityCode") String specialityCode, @RequestParam("locationCode") String locationCode,@RequestParam("rank") Integer rank) {
	        try {
	            String result = departmentService.updateRankingForSpeciality(specialityCode,locationCode, rank);
	            return ResponseEntity.ok(result);
	        } catch (Exception e) {
	            log.error("Error updating ranking for specialityCode {}: {}", specialityCode, e.getMessage(), e);
	            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Failed to update ranking: " + e.getMessage());
	        }
	    }
	 
	 @Operation(summary = "Updating the experience", description = "Updating the experience for Doctors in Admin Console.")
	 @PutMapping("/updateExperience")
	 public ResponseEntity<String> updateExperience(
	         @RequestParam("doctorCode") String doctorCode,
	         @RequestParam(value = "experience", required = false) String experience) {

	     if ((experience == null || experience.isEmpty())) {
	         return ResponseEntity.badRequest().body("Either language or experience must be provided for an update.");
	     }

	     log.info("Updating experience or language for doctorCode: {}", doctorCode);

	     try {
	         String result = departmentService.updateExperience(doctorCode, experience);
	         return ResponseEntity.ok(result);
	     } catch (Exception e) {
	         log.error("Failed to update experience for doctorCode {}: {}", doctorCode, e.getMessage());
	         return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
	                 .body("Failed to update experience: " + e.getMessage());
	     }
	 }

	 @Operation(summary = "Updating the languages", description = "Updating the languages for Doctors in Admin Console.")
	 @PutMapping("/updateLanguage")
	 public ResponseEntity<String> updateLanguage(
	         @RequestParam("doctorCode") String doctorCode,
	         @RequestParam(value = "language", required = false) String language) {

	     if ((language == null || language.isEmpty())) {
	         return ResponseEntity.badRequest().body("Either language must be provided for an update.");
	     }

	     log.info("Updating language for doctorCode: {}", doctorCode);

	     try {
	         String result = departmentService.updateLanguage(doctorCode, language);
	         return ResponseEntity.ok(result);
	     } catch (Exception e) {
	         log.error("Failed to update language for doctorCode {}: {}", doctorCode, e.getMessage());
	         return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
	                 .body("Failed to update  language: " + e.getMessage());
	     }
	 }
	 
	 @Operation(summary = "Updating the appointment type", description = "Updating the appointment type for Doctors in Admin Console.")
     @PutMapping("/updateAppointmentType")
	 public ResponseEntity<String> updateAppointmentType(
	         @RequestParam("doctorCode") String doctorCode,
	         @RequestParam("appointmentType") String appointmentType) {
 
	     if (doctorCode == null || doctorCode.isEmpty() || appointmentType == null || appointmentType.isEmpty()) {
	         return ResponseEntity.badRequest().body("Doctor code and appointment type must be provided.");
	     }

	     log.info("Updating appointment type for doctorCode: {}", doctorCode);

	     try {
	         String result = departmentService.updateAppointmentType(doctorCode, appointmentType);
	         return ResponseEntity.ok(result);
	     } catch (Exception e) {
	         log.error("Failed to update appointment type for doctorCode {}: {}", doctorCode, e.getMessage(), e);
	         return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
	                 .body("Failed to update appointment type: " + e.getMessage());
	     }
	 }

    @Operation(summary = "Fetch Patient Appointments", description = "Retrieve patient appointments based on UHID for the current date.")
    @GetMapping("/appointments/{uhId}")
    public ResponseEntity<List<MitrPateintAppointmentsDto>> getAppointments(
            @PathVariable(name = "uhId", required = true) String uhId) {
         try {
              List<MitrPateintAppointmentsDto> appointments = departmentService.fetchAppointmentsByUhIdAndDate(uhId, new Date());
              return ResponseEntity.ok(appointments); 
        } catch (Exception e) {
            log.error("Error fetching appointments for UHID: {}", uhId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Collections.emptyList());
        }
    }


    @Operation(summary = "Updating the Carousel Ranking For Doctors", description = "Updating the carousel ranking for Doctors in Admin Console.")
    @PutMapping("/updateCarouselRankingForDoctor")
	 public ResponseEntity<String> updateCarouselRankingForDoctor(@RequestParam("doctorCode") String doctorCode, @RequestParam("locationCode") String locationCode,@RequestParam("rank") Integer rank) {
	        try {
	            String result = departmentService.updateCarouselRankingForDoctor(doctorCode,locationCode, rank);
	            return ResponseEntity.ok(result);
	        } catch (Exception e) {
	            log.error("Error updating ranking for specialityCode {}: {}", doctorCode, e.getMessage(), e);
	            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Failed to update ranking: " + e.getMessage());
	        }
	    }
    
    @Operation(summary = "Fetching the top five Doctors", description = "Fetching the top five Doctors for Carousel Ranking.")
    @GetMapping("/mitr/getTop5Doctors")
    public ResponseEntity<List<MitrDoctorDetails>> getTop5Doctors(@RequestParam("locationCode") String locationCode) {
        try {
            log.info("Received request to fetch top 5 doctors for location: {}", locationCode);
            List<MitrDoctorDetails> doctors = departmentService.getTop5Doctors(locationCode);
            return ResponseEntity.ok(doctors);
        } catch (Exception e) {
            log.error("Error occurred while fetching doctors: ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                 .body(null);
        }
    }

}
