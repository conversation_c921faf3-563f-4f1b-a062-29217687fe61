package com.aig.aigone.daycare.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.aig.aigone.daycare.model.Booking;

import java.time.LocalDateTime;
import java.util.List;
@Repository
public interface BookingRepository extends JpaRepository<Booking, Integer> {

    @Query("SELECT b FROM Booking b WHERE b.id IN :bookingIds ORDER BY b.id DESC")
    List<Booking> findByBookingIds(@Param("bookingIds") List<Integer> bookingIds);
    
    @Query("SELECT b FROM Booking b WHERE b.bookedOn >= :startDate ORDER BY b.id DESC")
    List<Booking> findByBookingIdByDate(@Param("startDate") LocalDateTime startDate);
    
    @Query("SELECT COUNT(b) > 0 FROM Booking b WHERE b.uhid = :uhid")
    boolean existsByUhid(@Param("uhid") String uhid);
    
    @Query("SELECT b.id FROM Booking b WHERE b.uhid = :uhid and b.isActive=true ORDER BY b.id DESC")
    List<Integer> findBookingIdsByUhid(@Param("uhid") String uhid);
    
    @Query("SELECT b FROM Booking b WHERE b.id = :bookingId AND b.isActive=true")
    Booking findByBookingId(@Param("bookingId") Integer bookingId);
    
    @Query("SELECT b FROM Booking b WHERE b.uhid = :uhid")
    List<Booking> findByUhid(@Param("uhid") String uhid);

    @Query("SELECT b FROM Booking b WHERE b.isActive = false")
	List<Booking> findCancelledBooking();
    
   


    





}
