package com.aig.aigone.controller;

import com.aig.aigone.model.dto.SurveyDTO;
import com.aig.aigone.model.dto.SurveyResultSummaryDTO;
import com.aig.aigone.model.dto.SurveyStatusDTO;
import com.aig.aigone.model.dto.UserSessionVO;
import com.aig.aigone.security.SecurityUtil;
import com.aig.aigone.service.SurveyService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/surveys")
@RequiredArgsConstructor
@Tag(name = "Survey Management", description = "APIs for managing surveys")
public class SurveyController {

    private final SurveyService surveyService;

    @PostMapping
    @Operation(summary = "Create a new survey")
    public ResponseEntity<SurveyDTO> createSurvey(@RequestBody SurveyDTO surveyDTO) {
        return ResponseEntity.ok(surveyService.createSurvey(surveyDTO));
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update an existing survey")
    public ResponseEntity<SurveyDTO> updateSurvey(@PathVariable Long id, @RequestBody SurveyDTO surveyDTO) {
        return ResponseEntity.ok(surveyService.updateSurvey(id, surveyDTO));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete a survey")
    public ResponseEntity<Void> deleteSurvey(@PathVariable Long id) {
        surveyService.deleteSurvey(id);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get a survey by ID")
    public ResponseEntity<SurveyDTO> getSurvey(@PathVariable Long id) {
        return ResponseEntity.ok(surveyService.getSurvey(id));
    }

    @GetMapping
    @Operation(summary = "Get all surveys")
    public ResponseEntity<List<SurveyDTO>> getAllSurveys() {
        return ResponseEntity.ok(surveyService.getAllSurveys());
    }

    @GetMapping("/active")
    @Operation(summary = "Get all active surveys")
    public ResponseEntity<List<SurveyDTO>> getActiveSurveys() {
        return ResponseEntity.ok(surveyService.getActiveSurveys());
    }

//    @GetMapping("/creator/{createdBy}")
//    @Operation(summary = "Get surveys by creator")
//    public ResponseEntity<List<SurveyDTO>> getSurveysByCreator(@PathVariable String createdBy) {
//        return ResponseEntity.ok(surveyService.getSurveysByCreator(createdBy));
//    }

    @GetMapping("/status")
    @Operation(summary = "Check if user has any pending surveys")
    public ResponseEntity<?> getSurveyStatus(
            @RequestParam(value = "isMandatory", required = false) Boolean isMandatory) {
        UserSessionVO currentUser = SecurityUtil.getCurrentUser();
        String employeeId = currentUser.getEmployeeId();

        if (Boolean.TRUE.equals(isMandatory)) {
            // Only mandatory surveys, show one by one (first index)
            List<SurveyStatusDTO> mandatorySurveys = surveyService.getMandatorySurveysForUser(employeeId);
            if (!mandatorySurveys.isEmpty()) {
                return ResponseEntity.ok(mandatorySurveys.get(0)); // Only the first mandatory survey
            } else {
                return ResponseEntity.ok().build(); // Or return a message/empty object
            }
        } else {
            // All active surveys for the user
            List<SurveyStatusDTO> activeSurveys = surveyService.getSurveyStatusListForUser(employeeId);
            return ResponseEntity.ok(activeSurveys);
        }
    }
    

} 