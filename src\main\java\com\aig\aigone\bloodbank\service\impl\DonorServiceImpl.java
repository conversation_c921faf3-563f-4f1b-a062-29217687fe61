package com.aig.aigone.bloodbank.service.impl;

//import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.log;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import com.aig.aigone.bloodbank.model.dto.BloodRequestDTO;
import com.aig.aigone.bloodbank.model.dto.DonorBasicInfoDTO;
import com.aig.aigone.bloodbank.model.dto.DonorHistoryRequest;
import com.aig.aigone.bloodbank.model.dto.WhatsAppSendRequest;
import com.aig.aigone.bloodbank.model.entity.DonorEntity;
import com.aig.aigone.bloodbank.model.entity.DonorHistory;
import com.aig.aigone.bloodbank.repository.DonorHistoryRepository;
import com.aig.aigone.bloodbank.repository.DonorRepository;
import com.aig.aigone.bloodbank.service.DonorService;
import com.aig.aigone.mapper.AigOneMapper;
import com.fasterxml.jackson.core.JsonProcessingException;

import jakarta.transaction.Transactional;

import com.aig.aigone.exception.AigOneException;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@Scope(proxyMode = ScopedProxyMode.TARGET_CLASS)
public class DonorServiceImpl implements DonorService {
	@Autowired
	private DonorHistoryRepository donorhistoryrepository;
    @Autowired
  
	private AigOneMapper aigOneMapper;
    @Autowired
    private DonorRepository donorRepository;
    
    @Autowired
    private BloodbankWhatsAppMsg bloodbankWhatsAppMsg;

    
    public ResponseEntity<Map<String, Object>> registerDonorWithResponse(DonorBasicInfoDTO dto) {
        try {
            Optional<DonorEntity> existingDonor = donorRepository.findByEmployeeId(dto.getEmployeeId());

            if (existingDonor.isPresent()) {
                return ResponseEntity.status(HttpStatus.CONFLICT)
                        .body(Map.of("message", "Donor already registered."));
            }

            System.out.println("Before mapping, lastDonationDate: " + dto.getLastDonationDate());

            DonorEntity entity = aigOneMapper.toDonor(dto);

            // Set lastDonationDate and eligibleForDonation
            if (dto.getLastDonationDate() != null && !dto.getLastDonationDate().isBlank()) {
                String input = dto.getLastDonationDate().trim();
                try {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
                    LocalDate parsedDate = LocalDate.parse(input, formatter);
                    entity.setLastDonationDate(parsedDate);
                    entity.setLastDonationText(null);
                    
                    // ✅ Set eligibility based on donation date
                    boolean eligible = parsedDate.isBefore(LocalDate.now().minusDays(90));
                    entity.setEligibleForDonation(eligible);
                } catch (DateTimeParseException e) {
                    entity.setLastDonationDate(null);
                    entity.setLastDonationText(input);
                    
                    // ✅ Default to eligible when date format is unknown
                    entity.setEligibleForDonation(true);
                }
            } else {
                entity.setLastDonationDate(null);
                entity.setLastDonationText(null);
                
                // ✅ Default to eligible if no date provided
                entity.setEligibleForDonation(true);
            }

            DonorEntity savedEntity = donorRepository.save(entity);

            return ResponseEntity.ok(Map.of(
                    "message", "Donor registered successfully.",
                    "donorId", savedEntity.getId()
            ));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("message", "Internal error", "error", e.getMessage()));
        }
    }


    @Override
    public List<DonorBasicInfoDTO> getAllDonors() {
        List<DonorEntity> entities = donorRepository.findAll();

        return entities.stream().map(entity -> {
            DonorBasicInfoDTO dto = aigOneMapper.toDonorBasicInfoDTO(entity);
            boolean eligible = entity.getLastDonationDate() == null ||
                    entity.getLastDonationDate().isBefore(LocalDate.now().minusDays(90));
 dto.setEligibleForDonation(eligible);

            return dto;
        }).collect(Collectors.toList());
    }
 
    @Override
    public DonorBasicInfoDTO getDonorByEmployeeId(String employeeId) {
        DonorEntity entity = donorRepository.findByEmployeeId(employeeId)
                .orElseThrow(() -> new RuntimeException("Donor not found for employee ID: " + employeeId));
        return aigOneMapper.toDonorBasicInfoDTO(entity);
    }




 public void savedonorhistory(List<Long> donorids,String res) {
	List<DonorEntity> list= donorRepository.findAllById(donorids);
	
	Long maxRequestId = donorhistoryrepository.findMaxRequestId(); 
	if (maxRequestId == null || maxRequestId < 1000) {
	    maxRequestId = 999L;
	}

	AtomicLong counter = new AtomicLong(maxRequestId + 1);
	List<DonorHistory> donorhistory=list.stream().map(
	    		   dto->{
	    			   DonorHistory history=new DonorHistory();
	    			      history.setDonorname(dto.getName());
	    			      history.setAge(dto.getAge());
	    			      history.setBloodGroup(dto.getBloodGroup());
	    			      history.setCoMorbities(dto.getCoMorbities());
	    			      history.setDonorType(dto.getDonorType());
	    			      history.setEligibleForDonation(dto.getEligibleForDonation());
	    			      history.setEmployeeId(dto.getEmployeeId());
	    			      history.setFk_donor_id(dto.getId());
	    			      history.setGender(dto.getGender());
	    			      history.setLastDonationDate(dto.getLastDonationDate());
	    			      history.setMedications(dto.getMedications());
	    			      history.setMobile(dto.getMobile());
	    			      history.setRegistrationDate(dto.getRegistrationDate());
	    			      if (StringUtils.containsIgnoreCase(res, "i can donate")) {
	    			    	  history.setRequest_id(counter.getAndIncrement()); 
	    			        } else {
	    			            history.setRequest_id(0L);
	    			        }
	    			      history.setDonrResponse("ok");
	    			      
	    			   return history; 
	    		   }
	    		   ).collect(Collectors.toList());
	
	  donorhistoryrepository.saveAll(donorhistory); 
 }

   
    public Mono<ResponseEntity<String>> sendWhatsAppToDonors(WhatsAppSendRequest request) {        
    	List<BloodRequestDTO> requests = request.getRequests();

        if (requests == null || requests.isEmpty()) {
            return Mono.just(ResponseEntity.badRequest().body("Request list is empty"));
        }

        return Flux.fromIterable(requests)
                .flatMap(req -> {
                    List<String> variables = List.of(
                    		
                            req.getBloodGroup(), 
                            req.getFacility(), 
                           req.getRequiredDate()
                    );

                    List<String> phoneNumbers = req.getPhone().stream()
                            .flatMap(p -> Arrays.stream(p.split(",")))
                            .map(String::trim)
                            .collect(Collectors.toList());

                    return Flux.fromIterable(phoneNumbers)
                        .flatMap(phone -> {
                            try {
                                return bloodbankWhatsAppMsg.sendWhatsAppMessage(phone, variables)
                                        .map(response -> "Sent to " + phone + ": " + response)
                                        .onErrorResume(e -> Mono.just("Error sending to " + phone + ": " + e.getMessage()));
                            } catch (JsonProcessingException e) {
                               return Mono.just("JSON processing error for " + phone + ": " + e.getMessage());
                            }
                       });
                })
                .collectList().map(responses -> ResponseEntity.ok(String.join("\n", responses)));
   }


	

	@Override
	public boolean updateLastDonationDate(Long donorId, String donationDate, String coMorbities,
			String medications) {
		
		return false;
	}


	public ResponseEntity<Map<String, String>> updateLastDonationDateWithResponse(Long donorId, LocalDate donationDate,
	        String coMorbities, String medications) {
	    Optional<DonorEntity> donorOpt = donorRepository.findById(donorId);
	    if (donorOpt.isEmpty()) {
	        return ResponseEntity.status(HttpStatus.NOT_FOUND)
	                .body(Map.of("message", "Donor not found."));
	    }

	    DonorEntity donor = donorOpt.get();

	    donor.setLastDonationDate(donationDate);
	    if (donationDate == null) {
	        
	        donor.setEligibleForDonation(true);
	    } else {
	    
	        boolean eligible = donationDate.isBefore(LocalDate.now().minusDays(90));
	        donor.setEligibleForDonation(eligible);
	    }

	    if (coMorbities != null && !coMorbities.isBlank()) {
	        donor.setCoMorbities(coMorbities);
	    }

	    if (medications != null && !medications.isBlank()) {
	        donor.setMedications(medications);
	    }

	    donorRepository.save(donor);

	    return ResponseEntity.ok(Map.of("message", "Donation date updated successfully."));
	}


	@Transactional
	public String deleteDonorByEmployeeId(String employeeId) {
	    donorRepository.deleteByEmployeeId(employeeId);
	    return "Deleted Successfully";
	}




}

	






