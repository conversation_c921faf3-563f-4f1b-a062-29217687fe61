package com.aig.aigone.model.entity.aigone;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Entity(name = "t_user")
@ToString
public class UserEntity extends Auditable implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@Column(name = "first_name")
	private String firstName;
	
	@Column(name = "last_name")
	private String lastName;
	
	@Column(name = "name")
	private String name;
	
	private boolean active;
	
	@Column(name = "phone_no",unique = true)
	private Long phoneNo;
	
	private String gender;
	
	@Column(name = "employee_id",unique = true)
	private String employeeId;
		
	private String title;
	
	private Date dob;
	
//	@ManyToOne
//	@JoinColumn(name = "employee_type_id", referencedColumnName = "id")
//	private EmployeeTypeEntity employeeType;
//	
	private boolean consultant;
	
	@Column(name = "db_id")
	private Integer dbId;
	
	@Column(name = "icare_user")
	private boolean iCareUser;
	
	@Column(name = "device_token")
	private String deviceToken;
	
	@ManyToMany
    @JoinTable(
        name = "t_rel_user_role",
        joinColumns = @JoinColumn(name = "user_id"),
        inverseJoinColumns = @JoinColumn(name = "role_id")
    )
    private Set<RoleEntity> roles;
	
	@Column(name="secret_key")
	private String secretKey;
	
	private String department;
	
	 private String designation;

	    private Date doj;

	    private Date dow;

	    @Column(name = "resigned_date")
	    private Date resignedDate;

	    @Column(name = "separation_date")
	    private Date separationDate;

	    @Column(name = "paygroup_name")
	    private String payGroupName;

	    @Column(name = "company_name")
	    private String companyName;

	    @Column(name = "confirmation_period") 
	    private String confirmationPeriod;

	    @Column(name = "associate_grade")
	    private String associateGrade;

	    @Column(name = "per_mail_id")
	    private String perMailId;

	    @Column(name = "off_mail_id")
	    private String offMailId;

	    @Column(name = "np_days")
	    private Double npDays;
	    
	    @Column(name = "associate_id", nullable = false, unique = true)
	    private Integer associateId;

	    private String division;
	    
	
	    @Column(name = "manager_id")
	    private String managerId;

	    @Column(name = "manager_name")
	    private String managerName;
	    
	    @Column(name = "hos_location")
	    private String hosLocation;
	    
	    @Column(name = "blood_group")
	    private String bloodGroup;
	    
	    @Column(name = "icare_tabs")
	    private List<String> iCareTabs;
	   // List<Map<String,String>> iCareTabs;

	   @Column(name = "restricted_coupon")
	   private Boolean restrictedCoupon;

	    
	
}
