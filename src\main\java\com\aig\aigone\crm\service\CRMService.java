package com.aig.aigone.crm.service;

import java.util.List;
import java.util.Map;

import com.aig.aigone.crm.model.dto.DoctorConsultationFeeDetailsDto;
import com.aig.aigone.crm.model.dto.PatientOpBillsListRequestDto;
import com.aig.aigone.model.dto.emr.EmrVisitDetailsDto;
import com.aig.aigone.model.dto.his.PateintPreviousAppointmentsDto;
import com.aig.aigone.ref.dto.ReferenceValueResponseDTO;

public interface CRMService {

	List<ReferenceValueResponseDTO> getValuesByReferenceCode(String referenceCode);

	List<PateintPreviousAppointmentsDto> fetchAllPreviousAppointmentsByUhId(String uhId);

	Map<String, Object> getOPbillListFromUHID(PatientOpBillsListRequestDto requestDto);

	EmrVisitDetailsDto fetchPatientAllVisits(String uhId);

	List<DoctorConsultationFeeDetailsDto> getConsultationFeeDetails(Integer doctorId);

	
}
