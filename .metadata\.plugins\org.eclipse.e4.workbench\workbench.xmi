<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_rHW4cEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_rHW4cUzQEfCUbqAxRfw0FQ" bindingContexts="_rHW4ekzQEfCUbqAxRfw0FQ">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList/>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_rHW4cUzQEfCUbqAxRfw0FQ" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_raR0cUzQEfCUbqAxRfw0FQ" label="%trimmedwindow.label.eclipseSDK" x="182" y="182" width="1024" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1750311962282"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <tags>topLevel</tags>
    <tags>shellMaximized</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_raR0cUzQEfCUbqAxRfw0FQ" selectedElement="_raR0ckzQEfCUbqAxRfw0FQ" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_raR0ckzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_rlwEokzQEfCUbqAxRfw0FQ">
        <children xsi:type="advanced:Perspective" xmi:id="_rlwEokzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.JavaPerspective" selectedElement="_rlwEo0zQEfCUbqAxRfw0FQ" label="Java" iconURI="platform:/plugin/org.eclipse.jdt.ui/$nl$/icons/full/eview16/jperspective.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,"/>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.springsource.ide.eclipse.commons.launch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.TypeHierarchy</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.SourceView</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.JavadocView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.JavaProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewPackageCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewInterfaceCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewEnumCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewRecordCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewAnnotationCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewSnippetFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.ui.wizards.NewJavaWorkingSetWizard</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.folder</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.file</tags>
          <tags>persp.newWizSC:org.eclipse.ui.editors.wizards.UntitledTextFileWizard</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaBrowsingPerspective</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.editorOnboardingImageUri:platform:/plugin/org.eclipse.jdt.ui/$nl$/icons/full/onboarding_jperspective.png</tags>
          <tags>persp.editorOnboardingText:Open a file or drop files here to open them.</tags>
          <tags>persp.showIn:org.eclipse.jdt.ui.PackageExplorer</tags>
          <tags>persp.showIn:org.eclipse.team.ui.GenericHistoryView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$Ctrl+3</tags>
          <tags>persp.editorOnboardingCommand:Show Key Assist$$$Ctrl+Shift+L</tags>
          <tags>persp.editorOnboardingCommand:New$$$Ctrl+N</tags>
          <tags>persp.editorOnboardingCommand:Open Type$$$Ctrl+Shift+T</tags>
          <tags>persp.viewSC:org.eclipse.mylyn.tasks.ui.views.tasks</tags>
          <tags>persp.newWizSC:org.eclipse.mylyn.tasks.ui.wizards.new.repository.task</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.debug.ui.JDTDebugActionSet</tags>
          <tags>persp.newWizSC:org.eclipse.jdt.junit.wizards.NewTestCaseCreationWizard</tags>
          <tags>persp.actionSet:org.eclipse.jdt.junit.JUnitActionSet</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <tags>persp.newWizSC:org.eclipse.m2e.core.wizards.Maven2ProjectWizard</tags>
          <tags>persp.newWizSC:org.springsource.ide.eclipse.commons.gettingstarted.wizard.boot.NewSpringBootWizard</tags>
          <tags>persp.newWizSC:org.springsource.ide.eclipse.gettingstarted.wizards.import.generic.newalias</tags>
          <tags>persp.viewSC:org.eclipse.jdt.bcoview.views.BytecodeOutlineView</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.ant.ui.views.AntView</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_rlwEo0zQEfCUbqAxRfw0FQ" selectedElement="_rlwEpEzQEfCUbqAxRfw0FQ" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_rlwEpEzQEfCUbqAxRfw0FQ" containerData="2500" selectedElement="_rlwEpUzQEfCUbqAxRfw0FQ">
              <children xsi:type="basic:PartSashContainer" xmi:id="_rlwEpUzQEfCUbqAxRfw0FQ" containerData="6000" selectedElement="_rlwEpkzQEfCUbqAxRfw0FQ">
                <children xsi:type="basic:PartStack" xmi:id="_rlwEpkzQEfCUbqAxRfw0FQ" elementId="left" containerData="6600" selectedElement="_rlwEp0zQEfCUbqAxRfw0FQ">
                  <tags>org.eclipse.e4.primaryNavigationStack</tags>
                  <tags>active</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_rlwEp0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.PackageExplorer" ref="_rlnhwEzQEfCUbqAxRfw0FQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_rlwEqEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" toBeRendered="false" ref="_rlov4EzQEfCUbqAxRfw0FQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_rlwEqUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" toBeRendered="false" ref="_rlov4UzQEfCUbqAxRfw0FQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_rlwEqkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.junit.ResultView" toBeRendered="false" ref="_rlu2gUzQEfCUbqAxRfw0FQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Java</tags>
                  </children>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="_rlwEq0zQEfCUbqAxRfw0FQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashViewMStack" containerData="3400" selectedElement="_rlwErEzQEfCUbqAxRfw0FQ">
                  <children xsi:type="advanced:Placeholder" xmi:id="_rlwErEzQEfCUbqAxRfw0FQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" ref="_rlvdkkzQEfCUbqAxRfw0FQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Other</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_rlwErUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesViewMStack" toBeRendered="false" containerData="4000">
                <children xsi:type="advanced:Placeholder" xmi:id="_rlwErkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesView" toBeRendered="false" ref="_rlvdkEzQEfCUbqAxRfw0FQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Git</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_rlwEr0zQEfCUbqAxRfw0FQ" containerData="7500">
              <children xsi:type="basic:PartSashContainer" xmi:id="_rlwEsEzQEfCUbqAxRfw0FQ" containerData="7500" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_rlwEsUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_rliCMEzQEfCUbqAxRfw0FQ"/>
                <children xsi:type="basic:PartSashContainer" xmi:id="_rlwEskzQEfCUbqAxRfw0FQ" containerData="2500">
                  <children xsi:type="basic:PartStack" xmi:id="_rlwEs0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasksMStack" containerData="5000" selectedElement="_rlwEtEzQEfCUbqAxRfw0FQ">
                    <children xsi:type="advanced:Placeholder" xmi:id="_rlwEtEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" ref="_rlu2gEzQEfCUbqAxRfw0FQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Mylyn</tags>
                    </children>
                  </children>
                  <children xsi:type="basic:PartStack" xmi:id="_rlwEtUzQEfCUbqAxRfw0FQ" elementId="right" containerData="5000" selectedElement="_rlwEtkzQEfCUbqAxRfw0FQ">
                    <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                    <children xsi:type="advanced:Placeholder" xmi:id="_rlwEtkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.ContentOutline" ref="_rlpW9UzQEfCUbqAxRfw0FQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_rlwEt0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.texteditor.TemplatesView" toBeRendered="false" ref="_rlp-AEzQEfCUbqAxRfw0FQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_rlwEuEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_rlrzMEzQEfCUbqAxRfw0FQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_rlwEuUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" toBeRendered="false" ref="_rlvdkUzQEfCUbqAxRfw0FQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Java</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_rlwEukzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ant.ui.views.AntView" toBeRendered="false" ref="_rlwEoUzQEfCUbqAxRfw0FQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Ant</tags>
                    </children>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_rlwEu0zQEfCUbqAxRfw0FQ" elementId="bottom" containerData="2500" selectedElement="_rlwEvEzQEfCUbqAxRfw0FQ">
                <tags>org.eclipse.e4.secondaryDataStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_rlwEvEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.ProblemView" ref="_rlov4kzQEfCUbqAxRfw0FQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_rlwEvUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.JavadocView" ref="_rlov40zQEfCUbqAxRfw0FQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_rlwEvkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.SourceView" ref="_rlpW8EzQEfCUbqAxRfw0FQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_rlwEv0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.search.ui.views.SearchView" toBeRendered="false" ref="_rlpW8UzQEfCUbqAxRfw0FQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_rlwEwEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.console.ConsoleView" toBeRendered="false" ref="_rlpW8kzQEfCUbqAxRfw0FQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_rlwEwUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_rlpW80zQEfCUbqAxRfw0FQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_rlwEwkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_rlpW9EzQEfCUbqAxRfw0FQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_rlwEw0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_rlwEoEzQEfCUbqAxRfw0FQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Terminal</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_raR0c0zQEfCUbqAxRfw0FQ" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_raR0dEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_raRNYEzQEfCUbqAxRfw0FQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_raR0dUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_raRNYUzQEfCUbqAxRfw0FQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_raR0dkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_raR0cEzQEfCUbqAxRfw0FQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_raRNYEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_raRNYUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_raR0cEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_rliCMEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.editorss">
      <children xsi:type="basic:PartStack" xmi:id="_rliCMUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.e4.primaryDataStack">
        <tags>EditorStack</tags>
        <tags>org.eclipse.e4.primaryDataStack</tags>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rlnhwEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.PackageExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view group_libraries=&quot;1&quot; layout=&quot;2&quot; linkWithEditor=&quot;0&quot; rootMode=&quot;1&quot; workingSetName=&quot;&quot;>&#xD;&#xA;&lt;customFilters userDefinedPatternsEnabled=&quot;false&quot;>&#xD;&#xA;&lt;xmlDefinedFilters>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.StaticsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.buildfolder&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonSharedProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;bndtools.jareditor.tempfiles.packageexplorer.filter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyInnerPackageFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.m2e.MavenModuleFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.subProject&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ClosedProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.EmptyLibraryContainerFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.PackageDeclarationFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.pde.ui.BinaryProjectFilter1&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LocalTypesFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.pde.ui.ExternalPluginLibrariesFilter1&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.FieldsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaProjectsFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer_patternFilterId_.*&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.SyntheticMembersFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ContainedLibraryFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.HideInnerClassFilesFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.DeprecatedMembersFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ImportDeclarationFilter&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaElementFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LibraryFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.CuAndClassFileFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyPackageFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonPublicFilter&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;/xmlDefinedFilters>&#xD;&#xA;&lt;/customFilters>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
      <tags>active</tags>
      <tags>activeOnClose</tags>
      <menus xmi:id="_ro6GUEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.PackageExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_ro6GUUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.PackageExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rlov4EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rlov4UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rlov4kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.resourceField=&quot;90&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;300&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_rzF9EEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_rzF9EUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rlov40zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.JavadocView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rlpW8EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.SourceView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rlpW8UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.search.ui.views.SearchView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rlpW8kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rlpW80zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rlpW9EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rlpW9UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_ryXkUEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_ryXkUUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rlp-AEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.texteditor.TemplatesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rlrzMEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rlu2gEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view linkWithEditor=&quot;true&quot; presentation=&quot;org.eclipse.mylyn.tasks.ui.categorized&quot;>&#xD;&#xA;&lt;sorter groupBy=&quot;CATEGORY_QUERY&quot;>&#xD;&#xA;&lt;sorter>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled0 sortDirection=&quot;1&quot; sortKey=&quot;DUE_DATE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled1 sortDirection=&quot;1&quot; sortKey=&quot;SCHEDULED_DATE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled2 sortDirection=&quot;1&quot; sortKey=&quot;PRIORITY&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled3 sortDirection=&quot;1&quot; sortKey=&quot;RANK&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled4 sortDirection=&quot;1&quot; sortKey=&quot;DATE_CREATED&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled5 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled6 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled7 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.scheduled8 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized0 sortDirection=&quot;1&quot; sortKey=&quot;PRIORITY&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized1 sortDirection=&quot;1&quot; sortKey=&quot;RANK&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized2 sortDirection=&quot;1&quot; sortKey=&quot;DATE_CREATED&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized3 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized4 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized5 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized6 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized7 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;sortorg.eclipse.mylyn.tasks.ui.categorized8 sortDirection=&quot;1&quot; sortKey=&quot;NONE&quot;/>&#xD;&#xA;&lt;/sorter>&#xD;&#xA;&lt;/sorter>&#xD;&#xA;&lt;filteredTreeFindHistory/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Mylyn</tags>
      <menus xmi:id="_rwAz4EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_rwAz4UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rlu2gUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.junit.ResultView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rlvdkEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Git</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rlvdkUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bytecode" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/bytecodeview.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeOutlineView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rlvdkkzQEfCUbqAxRfw0FQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Boot Dashboard" iconURI="platform:/plugin/org.springframework.ide.eclipse.boot.dash/icons/boot-icon.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.springframework.ide.eclipse.boot.dash.views.BootDashTreeView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.springframework.ide.eclipse.boot.dash"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Other</tags>
      <menus xmi:id="_rt1poEzQEfCUbqAxRfw0FQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_rt1poUzQEfCUbqAxRfw0FQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rlwEoEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Terminal</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rlwEoUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ant.ui.views.AntView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Ant</tags>
    </sharedElements>
    <trimBars xmi:id="_rHW4ckzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.platform">
      <children xsi:type="menu:ToolBar" xmi:id="_rdeSYEzQEfCUbqAxRfw0FQ" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_rdeSYUzQEfCUbqAxRfw0FQ" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_rdhVsEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_rdqfoEzQEfCUbqAxRfw0FQ" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" enabled="false" command="_rIErjEzQEfCUbqAxRfw0FQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_rdhVsUzQEfCUbqAxRfw0FQ" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_rdhVskzQEfCUbqAxRfw0FQ" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_rdhVs0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.workbench.edit">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_rdrGsEzQEfCUbqAxRfw0FQ" elementId="undo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/undo_edit.png" tooltip="Undo" enabled="false" command="_rIAZwEzQEfCUbqAxRfw0FQ"/>
        <children xsi:type="menu:HandledToolItem" xmi:id="_rdrtwEzQEfCUbqAxRfw0FQ" elementId="redo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/redo_edit.png" tooltip="Redo" enabled="false" command="_rIEqP0zQEfCUbqAxRfw0FQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_rdhVtEzQEfCUbqAxRfw0FQ" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_rdhVtUzQEfCUbqAxRfw0FQ" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_rnETQEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_rmoOYEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_rm6iQEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_rdhVtkzQEfCUbqAxRfw0FQ" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_rdhVt0zQEfCUbqAxRfw0FQ" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_rdij0EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_rd2F0EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" enabled="false" type="Check" command="_rIErRUzQEfCUbqAxRfw0FQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_rdjx8EzQEfCUbqAxRfw0FQ" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_rdjx8UzQEfCUbqAxRfw0FQ" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_rdlAEEzQEfCUbqAxRfw0FQ" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_rdlAEUzQEfCUbqAxRfw0FQ" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_rdlAEkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_rfYW4EzQEfCUbqAxRfw0FQ" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_rfZlAEzQEfCUbqAxRfw0FQ" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_rHW4c0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.platform" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_rHW4dEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_rHW4dUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_rHW4dkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_rHW4d0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.platform" side="Left"/>
    <trimBars xmi:id="_rHW4eEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.platform" side="Right"/>
  </children>
  <bindingTables xmi:id="_rHW4eUzQEfCUbqAxRfw0FQ" contributorURI="platform:/plugin/org.eclipse.platform" bindingContext="_rHW4ekzQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIp4_kzQEfCUbqAxRfw0FQ" keySequence="CTRL+1" command="_rIAZgUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIqgEEzQEfCUbqAxRfw0FQ" keySequence="CTRL+6" command="_rIAZqUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIqgGUzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+I" command="_rIAZYEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIrHF0zQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+L" command="_rIErxEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIrHJkzQEfCUbqAxRfw0FQ" keySequence="CTRL+SPACE" command="_rIErZEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIruLEzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+D" command="_rIEr7kzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIsVTEzQEfCUbqAxRfw0FQ" keySequence="CTRL+V" command="_rIAY40zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIs8V0zQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+SPACE" command="_rIAZnkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIs8WEzQEfCUbqAxRfw0FQ" keySequence="CTRL+A" command="_rIAaFkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rItjVkzQEfCUbqAxRfw0FQ" keySequence="CTRL+C" command="_rIEqdEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuKa0zQEfCUbqAxRfw0FQ" keySequence="CTRL+X" command="_rIAZxkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuKbEzQEfCUbqAxRfw0FQ" keySequence="CTRL+Y" command="_rIEqP0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuKbkzQEfCUbqAxRfw0FQ" keySequence="CTRL+Z" command="_rIAZwEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuxekzQEfCUbqAxRfw0FQ" keySequence="ALT+PAGE_UP" command="_rIEqTkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuxe0zQEfCUbqAxRfw0FQ" keySequence="ALT+PAGE_DOWN" command="_rIErDkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuxf0zQEfCUbqAxRfw0FQ" keySequence="SHIFT+INSERT" command="_rIAY40zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIvYgEzQEfCUbqAxRfw0FQ" keySequence="ALT+F11" command="_rIAZJUzQEfCUbqAxRfw0FQ">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_rIxNtEzQEfCUbqAxRfw0FQ" keySequence="CTRL+F10" command="_rIAZA0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIxNvkzQEfCUbqAxRfw0FQ" keySequence="CTRL+INSERT" command="_rIEqdEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx0ykzQEfCUbqAxRfw0FQ" keySequence="CTRL+PAGE_UP" command="_rIErpUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx0y0zQEfCUbqAxRfw0FQ" keySequence="CTRL+PAGE_DOWN" command="_rIAZikzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx0zUzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+F1" command="_rIAZPEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx0zkzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+F2" command="_rIEq9kzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx0z0zQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+F3" command="_rIErmUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx01EzQEfCUbqAxRfw0FQ" keySequence="SHIFT+DEL" command="_rIAZxkzQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIikMEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.textEditorScope" bindingContext="_rIKJskzQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIoq0EzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+CR" command="_rIErmEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIpR4kzQEfCUbqAxRfw0FQ" keySequence="CTRL+BS" command="_rIAYuEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIp4-0zQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+Q" command="_rIAZZkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIqgHEzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+J" command="_rIAZW0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIrHE0zQEfCUbqAxRfw0FQ" keySequence="CTRL++" command="_rIEq60zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIrHHUzQEfCUbqAxRfw0FQ" keySequence="CTRL+-" command="_rIAZ-EzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIrHIkzQEfCUbqAxRfw0FQ" keySequence="CTRL+/" command="_rIAYw0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIruKEzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+C" command="_rIAYykzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIruKkzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+C" command="_rIAYw0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIruM0zQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+F" command="_rIAZqEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIsVM0zQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+J" command="_rIAZd0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIsVO0zQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+A" command="_rIEqlUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIsVSUzQEfCUbqAxRfw0FQ" keySequence="CTRL+T" command="_rIEsBEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIs8QUzQEfCUbqAxRfw0FQ" keySequence="CTRL+J" command="_rIAZCkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIs8RUzQEfCUbqAxRfw0FQ" keySequence="CTRL+L" command="_rIEreEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIs8TUzQEfCUbqAxRfw0FQ" keySequence="CTRL+O" command="_rIEr4kzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIs8VEzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+/" command="_rIAZskzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rItjXEzQEfCUbqAxRfw0FQ" keySequence="CTRL+D" command="_rIAZFkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuKZUzQEfCUbqAxRfw0FQ" keySequence="CTRL+=" command="_rIEq60zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuKaUzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+Y" command="_rH9VYkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuKcUzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+DEL" command="_rIEraUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuKckzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+X" command="_rIEqfUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuKc0zQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+Y" command="_rIAZ9UzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuKd0zQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+\" command="_rIEqrUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuxcUzQEfCUbqAxRfw0FQ" keySequence="CTRL+DEL" command="_rIAZuEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuxdEzQEfCUbqAxRfw0FQ" keySequence="ALT+ARROW_UP" command="_rIHtikzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuxdkzQEfCUbqAxRfw0FQ" keySequence="ALT+ARROW_DOWN" command="_rIErGkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuxfEzQEfCUbqAxRfw0FQ" keySequence="SHIFT+END" command="_rIAaAUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIvYgUzQEfCUbqAxRfw0FQ" keySequence="SHIFT+HOME" command="_rIAZ6EzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIv_kUzQEfCUbqAxRfw0FQ" keySequence="END" command="_rIErskzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIv_k0zQEfCUbqAxRfw0FQ" keySequence="INSERT" command="_rIEquEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIv_mUzQEfCUbqAxRfw0FQ" keySequence="F2" command="_rIAZjEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIv_pkzQEfCUbqAxRfw0FQ" keySequence="HOME" command="_rIEr0EzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIv_qUzQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+ARROW_UP" command="_rIEsBkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIwmoEzQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+ARROW_DOWN" command="_rIEqIEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIwmo0zQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+INSERT" command="_rIAZTEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIwmqEzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_rIAaBEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIwmqkzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_rIAZU0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIxNtUzQEfCUbqAxRfw0FQ" keySequence="CTRL+F10" command="_rIErkkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIxNvEzQEfCUbqAxRfw0FQ" keySequence="CTRL+END" command="_rIErHkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx0xUzQEfCUbqAxRfw0FQ" keySequence="CTRL+ARROW_UP" command="_rIAZOkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx0xkzQEfCUbqAxRfw0FQ" keySequence="CTRL+ARROW_DOWN" command="_rIHtmUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx0yEzQEfCUbqAxRfw0FQ" keySequence="CTRL+ARROW_LEFT" command="_rIEqbkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx0yUzQEfCUbqAxRfw0FQ" keySequence="CTRL+ARROW_RIGHT" command="_rIAZZUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx0zEzQEfCUbqAxRfw0FQ" keySequence="CTRL+HOME" command="_rIAY4kzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx00EzQEfCUbqAxRfw0FQ" keySequence="CTRL+NUMPAD_MULTIPLY" command="_rIErMkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx00UzQEfCUbqAxRfw0FQ" keySequence="CTRL+NUMPAD_ADD" command="_rIEr9EzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx00kzQEfCUbqAxRfw0FQ" keySequence="CTRL+NUMPAD_SUBTRACT" command="_rIErlkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx000zQEfCUbqAxRfw0FQ" keySequence="CTRL+NUMPAD_DIVIDE" command="_rIAZPkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIyb0EzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_rIErOEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIyb1UzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_rIEqvkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIzC7EzQEfCUbqAxRfw0FQ" keySequence="ALT+/" command="_rIEr00zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIzC7UzQEfCUbqAxRfw0FQ" keySequence="SHIFT+CR" command="_rIErz0zQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIpR4EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.contexts.window" bindingContext="_rHW4e0zQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIpR4UzQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+SHIFT+A" command="_rIErHEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIpR5UzQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+SHIFT+T" command="_rIAZAkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIpR5kzQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+SHIFT+L" command="_rIEqrEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIpR50zQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+SHIFT+M" command="_rIEr5EzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIp490zQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+Q O" command="_rIEq_0zQEfCUbqAxRfw0FQ">
      <parameters xmi:id="_rIp4-EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_rIp4-UzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+Q P" command="_rIEq_0zQEfCUbqAxRfw0FQ">
      <parameters xmi:id="_rIp4-kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.PackageExplorer"/>
    </bindings>
    <bindings xmi:id="_rIp4_0zQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+B" command="_rIErB0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIp5AEzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+R" command="_rIHtnUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIqgAEzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+Q Q" command="_rIEq_0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIqgAUzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+S" command="_rIEq30zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIqgAkzQEfCUbqAxRfw0FQ" keySequence="CTRL+3" command="_rIAZi0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIqgA0zQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+T" command="_rIAZxUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIqgBUzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+T" command="_rIAZzkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIqgBkzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+Q S" command="_rIEq_0zQEfCUbqAxRfw0FQ">
      <parameters xmi:id="_rIqgB0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_rIqgCkzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+U" command="_rIAZS0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIqgC0zQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+Q T" command="_rIEq_0zQEfCUbqAxRfw0FQ">
      <parameters xmi:id="_rIqgDEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.TypeHierarchy"/>
    </bindings>
    <bindings xmi:id="_rIqgDUzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+V" command="_rIErtUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIqgEUzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+Q V" command="_rIEq_0zQEfCUbqAxRfw0FQ">
      <parameters xmi:id="_rIqgEkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_rIqgFkzQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+G" command="_rIEq9EzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIqgF0zQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+W" command="_rIAZxEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIqgGEzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+H" command="_rIEqaEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIqgGkzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+Q H" command="_rIEq_0zQEfCUbqAxRfw0FQ">
      <parameters xmi:id="_rIqgG0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_rIrHEEzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+Q J" command="_rIEq_0zQEfCUbqAxRfw0FQ">
      <parameters xmi:id="_rIrHEUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.JavadocView"/>
    </bindings>
    <bindings xmi:id="_rIrHEkzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+K" command="_rIAZN0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIrHFEzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+Q K" command="_rIEq_0zQEfCUbqAxRfw0FQ">
      <parameters xmi:id="_rIrHFUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.mylyn.tasks.ui.views.tasks"/>
    </bindings>
    <bindings xmi:id="_rIrHFkzQEfCUbqAxRfw0FQ" keySequence="CTRL+," command="_rIAY5kzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIrHG0zQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+Q L" command="_rIEq_0zQEfCUbqAxRfw0FQ">
      <parameters xmi:id="_rIrHHEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_rIrHHkzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+N" command="_rIEqiEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIrHH0zQEfCUbqAxRfw0FQ" keySequence="CTRL+." command="_rIEsCkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIrHI0zQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+O" command="_rIEr6EzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIrHJ0zQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+P" command="_rIAZKUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIruIEzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+A" command="_rIErDEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIruIkzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+B" command="_rIAZOEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIruJEzQEfCUbqAxRfw0FQ" keySequence="CTRL+#" command="_rIAZBEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIruLkzQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+T" command="_rIEqb0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIruL0zQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+E" command="_rIAZRkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIruN0zQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+G" command="_rIHteEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIruOUzQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+H" command="_rIAY-0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIruO0zQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+Q X" command="_rIEq_0zQEfCUbqAxRfw0FQ">
      <parameters xmi:id="_rIsVMEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_rIsVMUzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+Q Y" command="_rIEq_0zQEfCUbqAxRfw0FQ">
      <parameters xmi:id="_rIsVMkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_rIsVNEzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+Q Z" command="_rIEq_0zQEfCUbqAxRfw0FQ">
      <parameters xmi:id="_rIsVNUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_rIsVOUzQEfCUbqAxRfw0FQ" keySequence="CTRL+P" command="_rIErjEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIsVOkzQEfCUbqAxRfw0FQ" keySequence="CTRL+Q" command="_rIErn0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIsVQ0zQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+C" command="_rIEraEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIsVRkzQEfCUbqAxRfw0FQ" keySequence="CTRL+S" command="_rIAZ-kzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIsVSkzQEfCUbqAxRfw0FQ" keySequence="CTRL+U" command="_rIEqOkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIsVS0zQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+F" command="_rIEro0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIsVTkzQEfCUbqAxRfw0FQ" keySequence="CTRL+W" command="_rIEqSEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIsVT0zQEfCUbqAxRfw0FQ" keySequence="CTRL+H" command="_rIErY0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIs8QkzQEfCUbqAxRfw0FQ" keySequence="CTRL+K" command="_rIErCUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIs8RkzQEfCUbqAxRfw0FQ" keySequence="CTRL+M" command="_rIErX0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIs8SUzQEfCUbqAxRfw0FQ" keySequence="CTRL+N" command="_rIHtfUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIs8VkzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+P" command="_rIEq-EzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIs8WkzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+R" command="_rIEqREzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIs8X0zQEfCUbqAxRfw0FQ" keySequence="CTRL+B" command="_rIAY6UzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rItjUEzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+Q B" command="_rIEq_0zQEfCUbqAxRfw0FQ">
      <parameters xmi:id="_rItjUUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_rItjV0zQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+S" command="_rIEqXUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rItjWUzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+T" command="_rIEqmEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rItjWkzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+Q C" command="_rIEq_0zQEfCUbqAxRfw0FQ">
      <parameters xmi:id="_rItjW0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_rItjXUzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+Q D" command="_rIEq_0zQEfCUbqAxRfw0FQ">
      <parameters xmi:id="_rItjXkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.SourceView"/>
    </bindings>
    <bindings xmi:id="_rItjYkzQEfCUbqAxRfw0FQ" keySequence="CTRL+E" command="_rIAZtUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rItjY0zQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+V" command="_rIAaA0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rItjZEzQEfCUbqAxRfw0FQ" keySequence="CTRL+F" command="_rIAZHkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rItjZ0zQEfCUbqAxRfw0FQ" keySequence="CTRL+G" command="_rIAYuUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rItjaEzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+W" command="_rIEsBUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rItjaUzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+H" command="_rIAZr0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rItjakzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+I" command="_rIAZBUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuKYUzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+J" command="_rIAZs0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuKYkzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+K" command="_rIAZ_UzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuKY0zQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+L" command="_rIAZfUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuKZEzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+M" command="_rIEr9UzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuKZkzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+N" command="_rIAZwUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuKakzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+Z" command="_rIEqYkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuKb0zQEfCUbqAxRfw0FQ" keySequence="CTRL+_" command="_rIAZoUzQEfCUbqAxRfw0FQ">
      <parameters xmi:id="_rIuKcEzQEfCUbqAxRfw0FQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_rIuKdEzQEfCUbqAxRfw0FQ" keySequence="CTRL+{" command="_rIAZoUzQEfCUbqAxRfw0FQ">
      <parameters xmi:id="_rIuKdUzQEfCUbqAxRfw0FQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_rIuxd0zQEfCUbqAxRfw0FQ" keySequence="ALT+ARROW_LEFT" command="_rIAZB0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuxeUzQEfCUbqAxRfw0FQ" keySequence="ALT+ARROW_RIGHT" command="_rIAZ2EzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuxgUzQEfCUbqAxRfw0FQ" keySequence="SHIFT+F2" command="_rIEqz0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuxgkzQEfCUbqAxRfw0FQ" keySequence="SHIFT+F5" command="_rIEqJkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuxg0zQEfCUbqAxRfw0FQ" keySequence="ALT+F7" command="_rIEqn0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIvYgkzQEfCUbqAxRfw0FQ" keySequence="ALT+F5" command="_rIAaD0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIvYhUzQEfCUbqAxRfw0FQ" keySequence="F11" command="_rIEr4EzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIv_kEzQEfCUbqAxRfw0FQ" keySequence="F12" command="_rIErZkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIv_mEzQEfCUbqAxRfw0FQ" keySequence="F2" command="_rIAY6EzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIv_m0zQEfCUbqAxRfw0FQ" keySequence="F3" command="_rIAZekzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIv_nkzQEfCUbqAxRfw0FQ" keySequence="F4" command="_rIAY8UzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIv_pEzQEfCUbqAxRfw0FQ" keySequence="F5" command="_rIAZ4UzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIv_p0zQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+F7" command="_rIEr5UzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIv_qEzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+F8" command="_rIAZoEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIv_qkzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+F9" command="_rIAaDEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIwmoUzQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+ARROW_LEFT" command="_rIErn0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIwmokzQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+ARROW_RIGHT" command="_rIAZQEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIwmpEzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+F4" command="_rIAZxEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIwmpUzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+F6" command="_rIEq6EzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIwmqUzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+X J" command="_rIErEkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIwmq0zQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+X M" command="_rIAaHUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIwmrEzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+X A" command="_rIAY5UzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIwmrUzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+X B" command="_rIHtmEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIwmrkzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+X E" command="_rIEq80zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIwmr0zQEfCUbqAxRfw0FQ" keySequence="CTRL+F7" command="_rIEqdUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIwmtEzQEfCUbqAxRfw0FQ" keySequence="CTRL+F8" command="_rIAZhEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIxNs0zQEfCUbqAxRfw0FQ" keySequence="CTRL+F9" command="_rIAZSkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIxNuEzQEfCUbqAxRfw0FQ" keySequence="CTRL+F11" command="_rIErs0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIxNu0zQEfCUbqAxRfw0FQ" keySequence="CTRL+F12" command="_rIAZOUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx0wEzQEfCUbqAxRfw0FQ" keySequence="CTRL+F4" command="_rIEqSEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx0wkzQEfCUbqAxRfw0FQ" keySequence="CTRL+F6" command="_rIAZKkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx0w0zQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+F7" command="_rIErIEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx0xEzQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+X G" command="_rIEr3EzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx01UzQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+SHIFT+ARROW_UP" command="_rIEqykzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx01kzQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+SHIFT+ARROW_DOWN" command="_rIHthkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx010zQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+SHIFT+ARROW_RIGHT" command="_rIEqtEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx02EzQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+SHIFT+B D" command="_rIEr8UzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx02UzQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+SHIFT+B F" command="_rIAZZEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx02kzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+X O" command="_rIEq0EzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx03UzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+X P" command="_rIEr-0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx03kzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_rIEq3kzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIyb0UzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+X Q" command="_rIAZXkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIyb0kzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+X R" command="_rIEqPkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIyb00zQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+X T" command="_rIAaFEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIyb1EzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_rIAZo0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIyb1kzQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+SHIFT+B R" command="_rIEquUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIyb10zQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+SHIFT+B S" command="_rIErT0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIyb2EzQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+SHIFT+F12" command="_rIEr9kzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIyb2UzQEfCUbqAxRfw0FQ" keySequence="DEL" command="_rIAZL0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIzC60zQEfCUbqAxRfw0FQ" keySequence="ALT+-" command="_rIEqhkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIzC8EzQEfCUbqAxRfw0FQ" keySequence="ALT+CR" command="_rIErUkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIzC8UzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+D B" command="_rIEr60zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIzC8kzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+D E" command="_rIHtj0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIzC80zQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+D A" command="_rIErekzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIzp8EzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+D R" command="_rIEqQEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIzp8UzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+D T" command="_rIAYxUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIzp8kzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+D J" command="_rIErPUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIzp80zQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+D O" command="_rIEqeUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIzp9EzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+D P" command="_rIEriEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIzp9UzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+D Q" command="_rIEqLUzQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIpR40zQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" bindingContext="_rIKJxUzQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIpR5EzQEfCUbqAxRfw0FQ" keySequence="CTRL+CR" command="_rIAZrUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIsVREzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+C" command="_rIEqW0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIs8XUzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+R" command="_rIAaAkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rItjYEzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+U" command="_rIErKUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rItja0zQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+I" command="_rIAZ-UzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuxckzQEfCUbqAxRfw0FQ" keySequence="ALT+ARROW_UP" command="_rIEq_EzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuxdUzQEfCUbqAxRfw0FQ" keySequence="ALT+ARROW_DOWN" command="_rIAZsUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuxfUzQEfCUbqAxRfw0FQ" keySequence="SHIFT+INSERT" command="_rIAZLEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIv_kkzQEfCUbqAxRfw0FQ" keySequence="INSERT" command="_rIAZ8kzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIv_oEzQEfCUbqAxRfw0FQ" keySequence="F4" command="_rIAZAEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIwmskzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+ARROW_UP" command="_rIErVkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIxNsUzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_rIAZ_kzQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIpR6EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.javaEditorScope" bindingContext="_rIKJw0zQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIpR6UzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+P" command="_rIErH0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIqgBEzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+T" command="_rIAZxUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIqgE0zQEfCUbqAxRfw0FQ" keySequence="CTRL+7" command="_rIEqi0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIrHGEzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+M" command="_rIAZjUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIrHIEzQEfCUbqAxRfw0FQ" keySequence="CTRL+/" command="_rIEqi0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIruJUzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+C" command="_rIEqi0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIruMkzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+F" command="_rIErzkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIsVPEzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+B" command="_rIHtjkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIsVSEzQEfCUbqAxRfw0FQ" keySequence="CTRL+T" command="_rIEqlEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIsVUEzQEfCUbqAxRfw0FQ" keySequence="CTRL+I" command="_rIAZ1kzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIs8TEzQEfCUbqAxRfw0FQ" keySequence="CTRL+O" command="_rIAaF0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIs8U0zQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+/" command="_rIEqO0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIs8W0zQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+R" command="_rIEqREzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rItjX0zQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+U" command="_rIErREzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rItjZUzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+'" command="_rIEqpkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuKaEzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+O" command="_rIAZu0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuKdkzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+\" command="_rIAZF0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIwmpkzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+ARROW_UP" command="_rIEqS0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIwmp0zQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_rIAaG0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIwmsEzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+ARROW_UP" command="_rIEqLEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIwmtUzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_rIAZO0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIxNtkzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+ARROW_LEFT" command="_rIAZ30zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIxNuUzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_rIAZEkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIxNwkzQEfCUbqAxRfw0FQ" keySequence="CTRL+F3" command="_rIEsCEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIyb30zQEfCUbqAxRfw0FQ" keySequence="CTRL+2 F" command="_rIEr80zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIzC50zQEfCUbqAxRfw0FQ" keySequence="CTRL+2 R" command="_rIErVEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIzC6EzQEfCUbqAxRfw0FQ" keySequence="CTRL+2 T" command="_rIEqikzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIzC6UzQEfCUbqAxRfw0FQ" keySequence="CTRL+2 L" command="_rIAZC0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIzC6kzQEfCUbqAxRfw0FQ" keySequence="CTRL+2 M" command="_rIAZ5EzQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIp48UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.core.runtime.xml" bindingContext="_rIp48EzQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIp48kzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+P" command="_rIErZ0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIruLUzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+D" command="_rIEqOUzQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIp480zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_rIKJvUzQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIp49EzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+P" command="_rIAZ70zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIruOEzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+G" command="_rIErsUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIruOkzQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+H" command="_rIErb0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIs8XEzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+R" command="_rIAY6EzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIv_nEzQEfCUbqAxRfw0FQ" keySequence="F3" command="_rIEroEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIv_n0zQEfCUbqAxRfw0FQ" keySequence="F4" command="_rIAYtUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIwmsUzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+ARROW_UP" command="_rIEqmUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIxNsEzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_rIEqnUzQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIp49UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" bindingContext="_rIKJyUzQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIp49kzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+P" command="_rIEqlkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIruIUzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+A" command="_rIHtmkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIruKUzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+C" command="_rIErzEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIruNkzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+F" command="_rIHteUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIs8QEzQEfCUbqAxRfw0FQ" keySequence="CTRL+I" command="_rIErkUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIs8UkzQEfCUbqAxRfw0FQ" keySequence="CTRL+O" command="_rIEqpUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIs8VUzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+/" command="_rIEqhUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuxcEzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+\" command="_rIEq9UzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIv_nUzQEfCUbqAxRfw0FQ" keySequence="F3" command="_rIEqk0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIwms0zQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+ARROW_UP" command="_rIErFUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIxNskzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_rIErK0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIxNt0zQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+ARROW_LEFT" command="_rIAY-UzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIxNukzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_rIEq0kzQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIp4_EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.classFileEditorScope" bindingContext="_rIKJvkzQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIp4_UzQEfCUbqAxRfw0FQ" keySequence="CTRL+1" command="_rIHtdUzQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIqgCEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.serverViewScope" bindingContext="_rIKJvEzQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIqgCUzQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+D" command="_rIErDUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIrHKEzQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+P" command="_rIErJUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIruI0zQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+R" command="_rIEsC0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIruK0zQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+S" command="_rIAZckzQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIqgDkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_rIKJsEzQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIqgD0zQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+V" command="_rIAZ7UzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIruJ0zQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+C" command="_rIErSUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuxc0zQEfCUbqAxRfw0FQ" keySequence="ALT+ARROW_UP" command="_rIAYt0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuxeEzQEfCUbqAxRfw0FQ" keySequence="ALT+ARROW_RIGHT" command="_rIEr30zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuxfkzQEfCUbqAxRfw0FQ" keySequence="SHIFT+INSERT" command="_rIAZ7UzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIxNvUzQEfCUbqAxRfw0FQ" keySequence="CTRL+INSERT" command="_rIErSUzQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIqgFEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.propertiesEditorScope" bindingContext="_rIKJ0EzQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIqgFUzQEfCUbqAxRfw0FQ" keySequence="CTRL+7" command="_rIEqi0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIrHIUzQEfCUbqAxRfw0FQ" keySequence="CTRL+/" command="_rIEqi0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIruJkzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+C" command="_rIEqi0zQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIrHGUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.editors.task" bindingContext="_rIKJxkzQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIrHGkzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+M" command="_rIAY1EzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIsVRUzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+C" command="_rIEqW0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIs8TkzQEfCUbqAxRfw0FQ" keySequence="CTRL+O" command="_rIEr_kzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIs8XkzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+R" command="_rIAaAkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rItjWEzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+S" command="_rIAZy0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rItjYUzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+U" command="_rIErKUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuKYEzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+I" command="_rIAZ-UzQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIrHJEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" bindingContext="_rIKJzUzQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIrHJUzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+O" command="_rIAY3UzQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIruMEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ant.ui.AntEditorScope" bindingContext="_rIKJzEzQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIruMUzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+F" command="_rIErzkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIs8WUzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+R" command="_rIAY8EzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuKZ0zQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+O" command="_rIAYwUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuxgEzQEfCUbqAxRfw0FQ" keySequence="SHIFT+F2" command="_rIEqqEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIv_mkzQEfCUbqAxRfw0FQ" keySequence="F3" command="_rIAYyEzQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIruNEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.pdeEditorContext" bindingContext="_rIKJt0zQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIruNUzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+F" command="_rIAZEEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIs8UUzQEfCUbqAxRfw0FQ" keySequence="CTRL+O" command="_rIAZJEzQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIsVNkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_rIKJu0zQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIsVN0zQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+M" command="_rIAaEUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIsVOEzQEfCUbqAxRfw0FQ" keySequence="ALT+CTRL+N" command="_rIEr90zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIsVR0zQEfCUbqAxRfw0FQ" keySequence="CTRL+T" command="_rIAZkEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIsVTUzQEfCUbqAxRfw0FQ" keySequence="CTRL+W" command="_rIEqw0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIs8SEzQEfCUbqAxRfw0FQ" keySequence="CTRL+N" command="_rIEq7kzQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIsVPUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" bindingContext="_rIKJwkzQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIsVPkzQEfCUbqAxRfw0FQ" keySequence="ALT+SHIFT+B" command="_rIHtjkzQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIsVP0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.debugging" bindingContext="_rIKJv0zQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIsVQEzQEfCUbqAxRfw0FQ" keySequence="CTRL+R" command="_rIEqeEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIvYg0zQEfCUbqAxRfw0FQ" keySequence="F7" command="_rIEsDkzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIvYhEzQEfCUbqAxRfw0FQ" keySequence="F8" command="_rIEqskzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIv_o0zQEfCUbqAxRfw0FQ" keySequence="F5" command="_rIAY9kzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIv_pUzQEfCUbqAxRfw0FQ" keySequence="F6" command="_rIAaBUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIxNwUzQEfCUbqAxRfw0FQ" keySequence="CTRL+F2" command="_rIErbEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx0wUzQEfCUbqAxRfw0FQ" keySequence="CTRL+F5" command="_rIEr20zQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIsVQUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.console" bindingContext="_rIKJtUzQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIsVQkzQEfCUbqAxRfw0FQ" keySequence="CTRL+R" command="_rIEqoUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIuKbUzQEfCUbqAxRfw0FQ" keySequence="CTRL+Z" command="_rIEsAUzQEfCUbqAxRfw0FQ">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_rIs8Q0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_rIKJwEzQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIs8REzQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+," command="_rIErokzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIs8R0zQEfCUbqAxRfw0FQ" keySequence="CTRL+SHIFT+." command="_rIErXEzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rItjZkzQEfCUbqAxRfw0FQ" keySequence="CTRL+G" command="_rIErXUzQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIs8SkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.DiffViewer" bindingContext="_rIKJs0zQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIs8S0zQEfCUbqAxRfw0FQ" keySequence="CTRL+O" command="_rIEqK0zQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIs8T0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" bindingContext="_rIKJyEzQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIs8UEzQEfCUbqAxRfw0FQ" keySequence="CTRL+O" command="_rIAY3UzQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rItjUkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_rIKJ0UzQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rItjU0zQEfCUbqAxRfw0FQ" keySequence="CTRL+C" command="_rIAZlUzQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIx0x0zQEfCUbqAxRfw0FQ" keySequence="CTRL+ARROW_LEFT" command="_rIAZE0zQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rItjVEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_rIKJwUzQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rItjVUzQEfCUbqAxRfw0FQ" keySequence="CTRL+C" command="_rIAZK0zQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIv_lEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" bindingContext="_rIKJx0zQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIv_lUzQEfCUbqAxRfw0FQ" keySequence="F1" command="_rIAYxkzQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIv_lkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" bindingContext="_rIKJ0kzQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIv_l0zQEfCUbqAxRfw0FQ" keySequence="F2" command="_rIAZMkzQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIv_oUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.buildship.ui.contexts.taskview" bindingContext="_rIKJy0zQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIv_okzQEfCUbqAxRfw0FQ" keySequence="F5" command="_rIEry0zQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIxNv0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.console.ConsoleView" bindingContext="_rIKJukzQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIxNwEzQEfCUbqAxRfw0FQ" keySequence="CTRL+INSERT" command="_rIEq2UzQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIx020zQEfCUbqAxRfw0FQ" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_rIKJxEzQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIx03EzQEfCUbqAxRfw0FQ" keySequence="ALT+Y" command="_rIEqg0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIyb2kzQEfCUbqAxRfw0FQ" keySequence="ALT+A" command="_rIEqg0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIyb20zQEfCUbqAxRfw0FQ" keySequence="ALT+B" command="_rIEqg0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIyb3EzQEfCUbqAxRfw0FQ" keySequence="ALT+C" command="_rIEqg0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIyb3UzQEfCUbqAxRfw0FQ" keySequence="ALT+D" command="_rIEqg0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIyb3kzQEfCUbqAxRfw0FQ" keySequence="ALT+E" command="_rIEqg0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIyb4EzQEfCUbqAxRfw0FQ" keySequence="ALT+F" command="_rIEqg0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIyb4UzQEfCUbqAxRfw0FQ" keySequence="ALT+G" command="_rIEqg0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIyb4kzQEfCUbqAxRfw0FQ" keySequence="ALT+P" command="_rIEqg0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIyb40zQEfCUbqAxRfw0FQ" keySequence="ALT+R" command="_rIEqg0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIzC4EzQEfCUbqAxRfw0FQ" keySequence="ALT+S" command="_rIEqg0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIzC4UzQEfCUbqAxRfw0FQ" keySequence="ALT+T" command="_rIEqg0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIzC4kzQEfCUbqAxRfw0FQ" keySequence="ALT+V" command="_rIEqg0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIzC40zQEfCUbqAxRfw0FQ" keySequence="ALT+W" command="_rIEqg0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIzC5EzQEfCUbqAxRfw0FQ" keySequence="ALT+H" command="_rIEqg0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIzC5UzQEfCUbqAxRfw0FQ" keySequence="ALT+L" command="_rIEqg0zQEfCUbqAxRfw0FQ"/>
    <bindings xmi:id="_rIzC5kzQEfCUbqAxRfw0FQ" keySequence="ALT+N" command="_rIEqg0zQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rIzC7kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.BreakpointView" bindingContext="_rIKJsUzQEfCUbqAxRfw0FQ">
    <bindings xmi:id="_rIzC70zQEfCUbqAxRfw0FQ" keySequence="ALT+CR" command="_rIEqZ0zQEfCUbqAxRfw0FQ"/>
  </bindingTables>
  <bindingTables xmi:id="_rljQUUzQEfCUbqAxRfw0FQ" bindingContext="_rljQUEzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rlj3YUzQEfCUbqAxRfw0FQ" bindingContext="_rlj3YEzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rlj3Y0zQEfCUbqAxRfw0FQ" bindingContext="_rlj3YkzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rlj3ZUzQEfCUbqAxRfw0FQ" bindingContext="_rlj3ZEzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rlj3Z0zQEfCUbqAxRfw0FQ" bindingContext="_rlj3ZkzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rlj3aUzQEfCUbqAxRfw0FQ" bindingContext="_rlj3aEzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rlj3a0zQEfCUbqAxRfw0FQ" bindingContext="_rlj3akzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rlj3bUzQEfCUbqAxRfw0FQ" bindingContext="_rlj3bEzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rlkecUzQEfCUbqAxRfw0FQ" bindingContext="_rlkecEzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rlkec0zQEfCUbqAxRfw0FQ" bindingContext="_rlkeckzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rlkedUzQEfCUbqAxRfw0FQ" bindingContext="_rlkedEzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rlked0zQEfCUbqAxRfw0FQ" bindingContext="_rlkedkzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rlkeeUzQEfCUbqAxRfw0FQ" bindingContext="_rlkeeEzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rlkee0zQEfCUbqAxRfw0FQ" bindingContext="_rlkeekzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rlkefUzQEfCUbqAxRfw0FQ" bindingContext="_rlkefEzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rlkef0zQEfCUbqAxRfw0FQ" bindingContext="_rlkefkzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rlkegUzQEfCUbqAxRfw0FQ" bindingContext="_rlkegEzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rllFgUzQEfCUbqAxRfw0FQ" bindingContext="_rllFgEzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rllFg0zQEfCUbqAxRfw0FQ" bindingContext="_rllFgkzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rllFhUzQEfCUbqAxRfw0FQ" bindingContext="_rllFhEzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rllFh0zQEfCUbqAxRfw0FQ" bindingContext="_rllFhkzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rllFiUzQEfCUbqAxRfw0FQ" bindingContext="_rllFiEzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rllFi0zQEfCUbqAxRfw0FQ" bindingContext="_rllFikzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rllFjUzQEfCUbqAxRfw0FQ" bindingContext="_rllFjEzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rllFj0zQEfCUbqAxRfw0FQ" bindingContext="_rllFjkzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rllFkUzQEfCUbqAxRfw0FQ" bindingContext="_rllFkEzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rllskEzQEfCUbqAxRfw0FQ" bindingContext="_rllFkkzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rllskkzQEfCUbqAxRfw0FQ" bindingContext="_rllskUzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rllslEzQEfCUbqAxRfw0FQ" bindingContext="_rllsk0zQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rllslkzQEfCUbqAxRfw0FQ" bindingContext="_rllslUzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rllsmEzQEfCUbqAxRfw0FQ" bindingContext="_rllsl0zQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rllsmkzQEfCUbqAxRfw0FQ" bindingContext="_rllsmUzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rllsnEzQEfCUbqAxRfw0FQ" bindingContext="_rllsm0zQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rllsnkzQEfCUbqAxRfw0FQ" bindingContext="_rllsnUzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rlmToUzQEfCUbqAxRfw0FQ" bindingContext="_rlmToEzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rlmTo0zQEfCUbqAxRfw0FQ" bindingContext="_rlmTokzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rlmTpUzQEfCUbqAxRfw0FQ" bindingContext="_rlmTpEzQEfCUbqAxRfw0FQ"/>
  <bindingTables xmi:id="_rlmTp0zQEfCUbqAxRfw0FQ" bindingContext="_rlmTpkzQEfCUbqAxRfw0FQ"/>
  <rootContext xmi:id="_rHW4ekzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_rHW4e0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.platform" name="In Windows" description="A window is open">
      <children xmi:id="_rHW4fEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.platform" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_rIKJsEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_rIKJsUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_rIKJskzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_rIKJs0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.DiffViewer" name="In Diff Viewer"/>
        <children xmi:id="_rIKJt0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.pdeEditorContext" name="PDE editor" description="The context used by PDE editors"/>
        <children xmi:id="_rIKJvUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_rIKJvkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.classFileEditorScope" name="Browsing attached Java Source" description="Browsing attached Java Source Context"/>
        <children xmi:id="_rIKJw0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.javaEditorScope" name="Editing Java Source" description="Editing Java Source Context"/>
        <children xmi:id="_rIKJxkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.editors.task" name="In Tasks Editor"/>
        <children xmi:id="_rIKJx0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context">
          <children xmi:id="_rIKJyEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context"/>
          <children xmi:id="_rIKJzUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" name="Task Markup Editor Source Context"/>
        </children>
        <children xmi:id="_rIKJyUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors">
          <children xmi:id="_rIKJykzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.sse.comments" name="Source Comments in Structured Text Editors" description="Source Comments in Structured Text Editors"/>
          <children xmi:id="_rIKJzkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.sse.hideFormat" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors"/>
        </children>
        <children xmi:id="_rIKJzEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ant.ui.AntEditorScope" name="Editing Ant Buildfiles" description="Editing Ant Buildfiles Context"/>
        <children xmi:id="_rIKJ0EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.propertiesEditorScope" name="Editing Properties Files" description="Editing Properties Files Context"/>
      </children>
      <children xmi:id="_rIKJtUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_rIKJtkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" name="In Terminal View" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_rIKJuEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_rIKJukzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_rIKJu0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_rIKJvEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.serverViewScope" name="In Servers View" description="In Servers View"/>
      <children xmi:id="_rIKJv0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_rIKJwEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_rIKJz0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.debug.ui.debugging" name="Debugging Java" description="Debugging Java programs"/>
      </children>
      <children xmi:id="_rIKJwUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_rIKJxEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_rIKJxUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" name="In Tasks View"/>
      <children xmi:id="_rIKJy0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.buildship.ui.contexts.taskview" name="In Gradle Tasks View" description="This context is activated when the Gradle Tasks view is in focus"/>
      <children xmi:id="_rIKJ0UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View">
        <children xmi:id="_rIKJ0kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" name="In Git Repositories View"/>
      </children>
    </children>
    <children xmi:id="_rHW4fUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs" description="A dialog is open"/>
  </rootContext>
  <rootContext xmi:id="_rIKJtEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_rIKJuUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_rIKJwkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" name="Editor Breadcrumb Navigation" description="Editor Breadcrumb Navigation Context"/>
  <rootContext xmi:id="_rIp48EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.core.runtime.xml" name="Auto::org.eclipse.core.runtime.xml"/>
  <rootContext xmi:id="_rljQUEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ant.ui.actionSet.presentation" name="Auto::org.eclipse.ant.ui.actionSet.presentation"/>
  <rootContext xmi:id="_rlj3YEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_rlj3YkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_rlj3ZEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_rlj3ZkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_rlj3aEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_rlj3akzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_rlj3bEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.SearchActionSet" name="Auto::org.eclipse.egit.ui.SearchActionSet"/>
  <rootContext xmi:id="_rlkecEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.debug.ui.JDTDebugActionSet" name="Auto::org.eclipse.jdt.debug.ui.JDTDebugActionSet"/>
  <rootContext xmi:id="_rlkeckzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.junit.JUnitActionSet" name="Auto::org.eclipse.jdt.junit.JUnitActionSet"/>
  <rootContext xmi:id="_rlkedEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.text.java.actionSet.presentation" name="Auto::org.eclipse.jdt.ui.text.java.actionSet.presentation"/>
  <rootContext xmi:id="_rlkedkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" name="Auto::org.eclipse.jdt.ui.JavaElementCreationActionSet"/>
  <rootContext xmi:id="_rlkeeEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.JavaActionSet" name="Auto::org.eclipse.jdt.ui.JavaActionSet"/>
  <rootContext xmi:id="_rlkeekzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.A_OpenActionSet" name="Auto::org.eclipse.jdt.ui.A_OpenActionSet"/>
  <rootContext xmi:id="_rlkefEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.CodingActionSet" name="Auto::org.eclipse.jdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_rlkefkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.SearchActionSet" name="Auto::org.eclipse.jdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_rlkegEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.launchActionSet" name="Auto::org.eclipse.linuxtools.docker.launchActionSet"/>
  <rootContext xmi:id="_rllFgEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.context.ui.actionSet" name="Auto::org.eclipse.mylyn.context.ui.actionSet"/>
  <rootContext xmi:id="_rllFgkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.navigation" name="Auto::org.eclipse.mylyn.tasks.ui.navigation"/>
  <rootContext xmi:id="_rllFhEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.navigation.additions" name="Auto::org.eclipse.mylyn.tasks.ui.navigation.additions"/>
  <rootContext xmi:id="_rllFhkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.SearchActionSet" name="Auto::org.eclipse.pde.ui.SearchActionSet"/>
  <rootContext xmi:id="_rllFiEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_rllFikzQEfCUbqAxRfw0FQ" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_rllFjEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_rllFjkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_rllFkEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_rllFkkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_rllskUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_rllsk0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_rllslUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_rllsl0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_rllsmUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_rllsm0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_rllsnUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_rlmToEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_rlmTokzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.server.ui.new.actionSet" name="Auto::org.eclipse.wst.server.ui.new.actionSet"/>
  <rootContext xmi:id="_rlmTpEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.server.ui.internal.webbrowser.actionSet" name="Auto::org.eclipse.wst.server.ui.internal.webbrowser.actionSet"/>
  <rootContext xmi:id="_rlmTpkzQEfCUbqAxRfw0FQ" elementId="org.springsource.ide.eclipse.commons.launch.actionSet" name="Auto::org.springsource.ide.eclipse.commons.launch.actionSet"/>
  <descriptors xmi:id="_rLoUcEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_rZysQEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ant.ui.views.AntView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" category="Ant" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Ant</tags>
  </descriptors>
  <descriptors xmi:id="_rZz6YEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.buildship.ui.views.taskview" label="Gradle Tasks" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/tasks_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.task.TaskView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_rZ1IgEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.buildship.ui.views.executionview" label="Gradle Executions" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/executions_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.execution.ExecutionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_rZ1vkEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rZ2WoEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rZ2WoUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rZ29sEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rZ29sUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rZ29skzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rZ29s0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rZ29tEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rZ29tUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_rZ29tkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_rZ29t0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.rebase.RebaseInteractiveView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_rZ29uEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.dialogs.CompareTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_rZ29uUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.reflog.ReflogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_rZ29ukzQEfCUbqAxRfw0FQ" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_rZ29u0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" label="Bytecode" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/bytecodeview.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeOutlineView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_rZ29vEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.bcoview.views.BytecodeReferenceView" label="Bytecode Reference" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/reference.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeReferenceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_rZ29vUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.debug.ui.DisplayView" label="Debug Shell" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.debug.ui.display.DisplayView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rZ29vkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.junit.ResultView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_rZ72MEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.PackageExplorer" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_rZ72MUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_rZ9rYEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.ProjectsView" label="Projects" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/projects.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.ProjectsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_rZ-ScEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.PackagesView" label="Packages" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.PackagesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_rZ-ScUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.TypesView" label="Types" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/types.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.TypesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_rZ-5gEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.MembersView" label="Members" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/members.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.MembersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_rZ-5gUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.png" tooltip="" allowMultiple="true" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.callhierarchy.CallHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_rZ-5gkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_rZ_gkEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_rZ_gkUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.JavadocView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_rZ_gkkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.dockerContainersView" label="Docker Containers" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/mock-repository.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerContainersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_raAHoEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.dockerImagesView" label="Docker Images" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/dbgroup_obj.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerImagesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_raAHoUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.dockerExplorerView" label="Docker Explorer" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/repositories-blue.gif" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerExplorerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_raAusEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.dockerImageHierarchyView" label="Docker Image Hierarchy" iconURI="platform:/plugin/org.eclipse.linuxtools.docker.ui/icons/class_hi.png" tooltip="" category="Docker" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.docker.ui.views.DockerImageHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.docker.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Docker</tags>
  </descriptors>
  <descriptors xmi:id="_raAusUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.lsp4e.callHierarchy.callHierarchyView" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/call_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.callhierarchy.CallHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_raBVwEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/type_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_raB80EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.lsp4e.ui.languageServersView" label="Language Servers" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.ui.LanguageServersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_raB80UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.m2e.core.views.MavenRepositoryView" label="Maven Repositories" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/maven_indexes.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenRepositoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_raCj4EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.m2e.core.views.MavenLifecycleMappingsView" label="Maven Lifecycle Mappings" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/main_tab.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenLifecycleMappingsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_raCj4UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.m2e.core.views.MavenBuild" label="Maven Workspace Build" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.build.BuildDebugView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_raCj4kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.commons.repositories.ui.navigator.Repositories" label="Team Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.commons.repositories.ui/icons/eview16/repositories.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.repositories.ui.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.repositories.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_raDK8EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" allowMultiple="true" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_raDyAEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.views.repositories" label="Task Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/repositories.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskRepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_raEZEEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.api.tools.ui.views.apitooling.views.apitoolingview" label="API Tools" iconURI="platform:/plugin/org.eclipse.pde.api.tools.ui/icons/full/obj16/api_tools.png" tooltip="" category="API Tools" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.api.tools.ui.internal.views.APIToolingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.api.tools.ui"/>
    <tags>View</tags>
    <tags>categoryTag:API Tools</tags>
  </descriptors>
  <descriptors xmi:id="_raEZEUzQEfCUbqAxRfw0FQ" elementId="pde.bnd.ui.repositoriesView" label="Bundle Repositories" iconURI="platform:/plugin/org.eclipse.pde.bnd.ui/icons/database.png" tooltip="" category="OSGi" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.bnd.ui.views.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.bnd.ui"/>
    <tags>View</tags>
    <tags>categoryTag:OSGi</tags>
  </descriptors>
  <descriptors xmi:id="_raFAIEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.runtime.RegistryBrowser" label="Plug-in Registry" iconURI="platform:/plugin/org.eclipse.pde.runtime/icons/eview16/registry.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.runtime.registry.RegistryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.runtime"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_raFAIUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.PluginsView" label="Plug-ins" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/eview16/plugin_depend.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.plugins.PluginsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_raFnMEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.FeaturesView" label="Features" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/feature_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.features.FeaturesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_raFnMUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.DependenciesView" label="Plug-in Dependencies" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/req_plugins_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.dependencies.DependenciesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_raFnMkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.TargetPlatformState" label="Target Platform State" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/target_profile_xml_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.target.TargetStateView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_raGOQEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.ImageBrowserView" label="Plug-in Image Browser" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/psearch_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.imagebrowser.ImageBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_raGOQUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_raG1UEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_raG1UUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_raHcYEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.tips.ide.tipPart" label="Tip of the Day" iconURI="platform:/plugin/org.eclipse.tips.ui/icons/lightbulb.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.tips.ide/org.eclipse.tips.ide.internal.TipPart">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_raIDcEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_raIDcUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.tcf.te.ui.terminals.TerminalsView" label="Terminals (Old)" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.OldTerminalsViewHandler"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_raIDckzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_raIDc0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_raIqgEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_raJ4oEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_raKfsEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_raLGwEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_raLt0EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_raLt0UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_raLt0kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_raMU4EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_raMU4UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_raM78EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_raM78UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_raNjAEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_raNjAUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.internet.monitor.view" label="TCP/IP Monitor" iconURI="platform:/plugin/org.eclipse.wst.internet.monitor.ui/icons/cview16/monitorView.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.internet.monitor.ui.internal.view.MonitorView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.internet.monitor.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_raOKEEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.server.ui.ServersView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" tooltip="" category="Server" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.server.ui.internal.cnf.ServersView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.server.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Server</tags>
  </descriptors>
  <descriptors xmi:id="_raOKEUzQEfCUbqAxRfw0FQ" elementId="org.springframework.ide.eclipse.boot.dash.views.BootDashView" label="Boot Dashboard" iconURI="platform:/plugin/org.springframework.ide.eclipse.boot.dash/icons/boot-icon.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.springframework.ide.eclipse.boot.dash.views.BootDashTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.springframework.ide.eclipse.boot.dash"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_raOxIEzQEfCUbqAxRfw0FQ" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.view.SpringSymbolsView" label="Spring Symbols" iconURI="platform:/plugin/org.springframework.tooling.ls.eclipse.gotosymbol/icons/boot-icon.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.springframework.tooling.ls.eclipse.gotosymbol.view.SpringSymbolsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.springframework.tooling.ls.eclipse.gotosymbol"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <trimContributions xmi:id="_2r10UF9tEeO-yojH_y4TJA" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_76uUAF9tEeO-yojH_y4TJA" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_8tJPcF9tEeO-yojH_y4TJA" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_9LgmcF9tEeO-yojH_y4TJA" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_rH9VYEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rH9VYUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rH9VYkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYsEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_rH7gWEzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIAYsUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_rIAYskzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to field" description="Invokes quick assist and selects 'Convert local variable to field'" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYs0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYtEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.FetchGitLabMergeRequest" commandName="Fetch GitLab Merge Request" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYtUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.lsp4e.openTypeHierarchy" commandName="Open Type Hierarchy" description="Open Type Hierarchy for the selected item" category="_rH7gQUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYtkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.startContainers" commandName="&amp;Start" description="Start the selected containers" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYt0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_rH7gTUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYuEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYuUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYukzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYu0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_rH7gQkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYvEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYvUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYvkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_rH7gWEzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIAYv0zQEfCUbqAxRfw0FQ" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="_rIAYwEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.gitflow.ui.command.hotfixPublish" commandName="Publish Hotfix" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYwUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ant.ui.toggleMarkOccurrences" commandName="Toggle Ant Mark Occurrences" description="Toggles mark occurrences in Ant editors" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYwkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYw0zQEfCUbqAxRfw0FQ" elementId="org.springframework.tooling.boot.ls.ToggleComment" commandName="Toggle Comment" category="_rH7gSUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYxEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.Revert" commandName="Revert Commit" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYxUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.junit.junitShortcut.debug" commandName="Debug JUnit Test" description="Debug JUnit Test" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYxkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.showCheatSheetCommand" commandName="Show Markup Cheat Sheet" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYx0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_rH7gUkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYyEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ant.ui.open.declaration.command" commandName="Open Declaration" description="Opens the Ant editor on the referenced element" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYyUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.editConnection" commandName="Edit..." category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYykzQEfCUbqAxRfw0FQ" elementId="org.eclipse.tm4e.languageconfiguration.toggleLineCommentCommand" commandName="Toggle Line Comment" category="_rH7gM0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYy0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYzEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.create.delegate.methods" commandName="Generate Delegate Methods" description="Add delegate methods for a type's fields" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYzUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.filediff.OpenWorkingTree" commandName="Open Working Tree Version" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYzkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.context.ui.commands.task.clearContext" commandName="Clear Context" category="_rH7gO0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAYz0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY0EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY0UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.commit.UnifiedDiffCommand" commandName="Show Unified Diff" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY0kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_rH7gQkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY00zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.refactor.migrate.jar" commandName="Migrate JAR File" description="Migrate a JAR File to a new version" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY1EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.maximizePart" commandName="Maximize Part" description="Maximize Part" category="_rH7gN0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY1UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_rH7gREzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY1kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Fields" description="Choose fields to initialize and constructor from superclass to call " category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY10zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.gitflow.ui.command.featurePublish" commandName="Publish Feature" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY2EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.commands.showElementInTypeHierarchyView" commandName="Show Java Element Type Hierarchy" description="Show a Java element in the Type Hierarchy view" category="_rH7gPUzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIAY2UzQEfCUbqAxRfw0FQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_rIAY2kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY20zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY3EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY3UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.wikitext.ui.quickOutlineCommand" commandName="Quick Outline" description="Open a popup dialog with a quick outline of the current document" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY3kzQEfCUbqAxRfw0FQ" elementId="AnsiConsole.command.enable_disable" commandName="Enable / Disable ANSI Support" description="Enable / disable ANSI Support" category="_rH7gPkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY30zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_rH7gSkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY4EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_rH7gSkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY4UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_rH7gREzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY4kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY40zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY5EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.gitflow.ui.command.featureCheckout" commandName="Check Out Feature" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY5UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.run" commandName="Run Java Applet" description="Run Java Applet" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY5kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY50zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY6EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_rH7gQkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY6UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_rH7gSkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY6kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY60zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.debug.ui.commands.ToggleLambdaEntryBreakpoint" commandName="Toggle Lambda Entry Breakpoint" description="Creates or removes a lambda entry breakpoint" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY7EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY7UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.buildship.ui.commands.runtasks" commandName="Run Gradle Tasks" description="Runs all the selected Gradle tasks" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY7kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggleBreadcrumb" commandName="Toggle Java Editor Breadcrumb" description="Toggle the Java editor breadcrumb" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY70zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY8EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ant.ui.renameInFile" commandName="Rename In File" description="Renames all references within the same buildfile" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY8UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY8kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.interface" commandName="Extract Interface" description="Extract a set of members into a new interface and try to use the new interface" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY80zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_rH7gU0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY9EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.m2e.actions.LifeCycleGenerateSources.run" commandName="Run Maven Generate Sources" description="Run Maven Generate Sources" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY9UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.copytocontainer" commandName="Copy to Container" description="Copy local files to a running Container" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY9kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY90zQEfCUbqAxRfw0FQ" elementId="org.eclipse.lsp4e.toggleHideFieldsOutline" commandName="Hide Fields" category="_rH7gQUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY-EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY-UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.sse.ui.structure.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY-kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.debug.ui.commands.AddExceptionBreakpoint" commandName="Add Java Exception Breakpoint" description="Add a Java exception breakpoint" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY-0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY_EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY_UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.debug.ui.commands.ToggleTracepoint" commandName="Toggle Tracepoint" description="Creates or removes a tracepoint" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAY_kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToMarkupCommand" commandName="Generate Markup" category="_rH7gWEzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIAY_0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.wikitext.ui.targetLanguage" name="TargetLanguage" optional="false"/>
  </commands>
  <commands xmi:id="_rIAZAEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.showToolTip" commandName="Show Tooltip Description" category="_rH7gPEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZAUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.context.ui.commands.task.copyContext" commandName="Copy Context" category="_rH7gO0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZAkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.tm.terminal.view.ui.command.launchToolbar" commandName="Open Local Terminal on Selection" category="_rH7gQ0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZA0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZBEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZBUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.inline" commandName="Inline" description="Inline a constant, local variable or method" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZBkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZB0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZCEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.lsp4e.collapseAllOutline" commandName="Collapse All" category="_rH7gQUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZCUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_rH7gQkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZCkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZC0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZDEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZDUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.return.continue.targets" commandName="Search break/continue Target Occurrences in File" description="Search for break/continue target occurrences of a selected target name" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZDkzQEfCUbqAxRfw0FQ" elementId="org.springframework.ide.eclipse.boot.dash.ToggleLineNumbers" commandName="Toggle Line Numbers" description="Toggle Line Numbers" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZD0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.create.getter.setter" commandName="Generate Getters and Setters" description="Generate Getter and Setter methods for type's fields" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZEEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.edit.text.format" commandName="Format Source" description="Format a PDE Source Page" category="_rH7gV0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZEUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.pauseContainers" commandName="&amp;Pause" description="Pause the selected containers" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZEkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZE0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesViewCollapseWorkingTree" commandName="Collapse Working Tree" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZFEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZFUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZFkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZF0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZGEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.implementation" commandName="Open Implementation" description="Opens the Implementations of a method or a type in its hierarchy" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZGUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.junit.gotoTest" commandName="Referring Tests" description="Referring Tests" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZGkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZG0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.commands.showElementInPackageView" commandName="Show Java Element in Package Explorer" description="Select Java element in the Package Explorer view" category="_rH7gPUzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIAZHEzQEfCUbqAxRfw0FQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_rIAZHUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.debug.ui.commands.InstanceCount" commandName="Instance Count" description="View the instance count of the selected type loaded in the target VM" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZHkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZH0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZIEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.addAllPluginsToJavaSearch" commandName="Add All Plug-ins to Java Workspace Scope" description="Adds all plug-ins in the target platform to Java workspace scope" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZIUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_rH7gWEzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIAZIkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_rIAZI0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.convert.anonymous.to.nested" commandName="Convert Anonymous Class to Nested" description="Convert an anonymous class to a nested class" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZJEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.quickOutline" commandName="Quick Outline" description="Open a quick outline popup dialog for a given editor input" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZJUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZJkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZJ0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_rH7gMEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZKEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.pullImage" commandName="&amp;Pull..." description="Pull Image from registry" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZKUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.m2e.profiles.ui.commands.selectMavenProfileCommand" commandName="Select Maven Profiles" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZKkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZK0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy Commit Id" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZLEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.new.subtask" commandName="New Subtask" category="_rH7gPEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZLUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_rH7gSEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZLkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZL0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZMEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.configureLabels" commandName="&amp;Configure Labels Filter..." description="Configure container labels to match with for filter." category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZMUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZMkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch..." category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZM0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZNEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZNUzQEfCUbqAxRfw0FQ" elementId="org.springframework.tooling.ls.eclipse.commons.commands.OpenResourceInEditor" commandName="Open File in Editor" category="_rH7gWEzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIAZNkzQEfCUbqAxRfw0FQ" elementId="path" name="path" optional="false"/>
  </commands>
  <commands xmi:id="_rIAZN0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZOEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZOUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.openTask" commandName="Open Task" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZOkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZO0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZPEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.runtime.spy.commands.spyCommand" commandName="Plug-in Selection Spy" description="Show the Plug-in Spy" category="_rH7gVkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZPUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.buildImage" commandName="&amp;Build Image" description="Build Image from Dockerfile" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZPkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZP0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZQEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZQUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_rH7gQkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZQkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_rH7gSkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZQ0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_rH7gQkzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIAZREzQEfCUbqAxRfw0FQ" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_rIAZRUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZRkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZR0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_rH7gSEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZSEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZSUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into Java comments" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZSkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.activateTask" commandName="Activate Task" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZS0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZTEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZTUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZTkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZT0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZUEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_rH7gSkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZUUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZUkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZU0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZVEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.JavaBrowsingPerspective" commandName="Java Browsing" description="Show the Java Browsing perspective" category="_rH7gUkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZVUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZVkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZV0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Tree" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZWEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" description="Check out, rename, create, or delete a branch in a git repository" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZWUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.openInHierarchyView" commandName="Open Image Hierarchy" description="Open the Docker image Hierarchy view" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZWkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZW0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZXEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="_rH7gWEzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIAZXUzQEfCUbqAxRfw0FQ" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="_rIAZXkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ant.ui.antShortcut.run" commandName="Run Ant Build" description="Run Ant Build" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZX0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZYEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.debug.ui.commands.Inspect" commandName="Inspect" description="Inspect result of evaluating selected text" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZYUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZYkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZY0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZZEzQEfCUbqAxRfw0FQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.OpenLaunchConfigAction" commandName="Open Config" description="Open Launch Configuration" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZZUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZZkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZZ0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZaEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZaUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.ReplaceWithTheirs" commandName="Replace Conflicting Files with Their Revision" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZakzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.filediff.CheckoutNew" commandName="Check Out This Version" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZa0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeContainers" commandName="&amp;Remove" description="Remove the selected containers" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZbEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZbUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.SynchronizeAll" commandName="Synchronize Changed" category="_rH7gPEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZbkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.debug.ui.commands.Watch" commandName="Watch" description="Create new watch expression" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZb0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZcEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.wikitext.context.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_rH7gQEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZcUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZckzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.server.stop" commandName="Stop" description="Stop the server" category="_rH7gP0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZc0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_rH7gSEzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIAZdEzQEfCUbqAxRfw0FQ" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_rIAZdUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZdkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script" description="Create a refactoring script from refactorings on the local workspace" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZd0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZeEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.refreshConnection" commandName="Refresh" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZeUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.pushImage" commandName="P&amp;ush..." description="Push Image tag to registry" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZekzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZe0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.lsp4e.showKindInOutline" commandName="Show Kind in Outline" category="_rH7gQUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZfEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZfUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.local.variable" commandName="Extract Local Variable" description="Extracts an expression into a new local variable and uses the new local variable" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZfkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.InstallLfsLocal" commandName="Enable LFS locally" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZf0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="Open Refactoring History " description="Opens the refactoring history" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZgEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZgUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZgkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.log.jdt.showinconsole" commandName="&amp;Show In Console" description="Show Stack Trace in Console View" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZg0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.push.down" commandName="Push Down" description="Move members to subclasses" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZhEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZhUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.UpdateRepositoryConfiguration" commandName="Update Repository Configuration" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZhkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZh0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZiEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open this Version" category="_rH7gUUzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIAZiUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_rIAZikzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZi0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZjEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZjUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.import" commandName="Add Import" description="Create import statement on selection" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZjkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.gitflow.ui.command.releasePublish" commandName="Publish Release" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZj0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.attachment.open" commandName="Open Attachment" category="_rH7gN0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZkEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZkUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZkkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZk0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.lsp4e.formatfile" commandName="Format" category="_rH7gQUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZlEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZlUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZlkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZl0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_rH7gRkzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIAZmEzQEfCUbqAxRfw0FQ" elementId="url" name="URL"/>
    <parameters xmi:id="_rIAZmUzQEfCUbqAxRfw0FQ" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_rIAZmkzQEfCUbqAxRfw0FQ" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_rIAZm0zQEfCUbqAxRfw0FQ" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_rIAZnEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZnUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZnkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZn0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_rH7gQkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZoEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZoUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_rH7gRkzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIAZokzQEfCUbqAxRfw0FQ" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_rIAZo0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZpEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_rH7gREzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZpUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateSelectedTask" commandName="Deactivate Selected Task" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZpkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZp0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZqEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.lsp4e.format" commandName="Format" category="_rH7gQUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZqUzQEfCUbqAxRfw0FQ" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.command" commandName="Goto Symbol" category="_rH7gMkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZqkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZq0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZrEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.createAntBuildFile" commandName="Create Ant Build File" description="Creates an Ant build file for the current project" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZrUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.openSelectedTask" commandName="Open Selected Task" category="_rH7gPEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZrkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Toggle &quot;Link with Editor and Selection&quot; (Git Repositories View)" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZr0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.context.ui.commands.toggle.focus.active.view" commandName="Focus on Active Task" description="Toggle the focus on active task for the active view" category="_rH7gO0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZsEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZsUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.goToNextUnread" commandName="Go To Next Unread Task" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZskzQEfCUbqAxRfw0FQ" elementId="org.eclipse.tm4e.languageconfiguration.addBlockCommentCommand" commandName="Add Block Comment" category="_rH7gM0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZs0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.javadoc.comment" commandName="Add Javadoc Comment" description="Add a Javadoc comment stub to the member element" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZtEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="Interactive Rebase" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZtUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZtkzQEfCUbqAxRfw0FQ" elementId="AnsiConsole.command.copy_with_escapes" commandName="Copy Text With ANSI Escapes" description="Copy the console content to clipboard, including the escape sequences" category="_rH7gPkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZt0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.runImage" commandName="Run" description="Run an Image" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZuEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZuUzQEfCUbqAxRfw0FQ" elementId="org.springframework.ide.eclipse.boot.properties.editor.convertPropertiesToYaml" commandName="Convert .properties to .yaml" category="_rH7gNUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZukzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.openDependencies" commandName="Open Plug-in Dependencies" description="Opens the plug-in dependencies view for the current plug-in" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZu0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in Java editors" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZvEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZvUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.replace.invocations" commandName="Replace Invocations" description="Replace invocations of the selected method" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZvkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZv0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZwEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZwUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_rH7gQkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZwkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZw0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_rH7gQkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZxEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_rH7gQkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZxUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a Java editor" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZxkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZx0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.indirection" commandName="Introduce Indirection" description="Introduce an indirection to encapsulate invocations of a selected method" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZyEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZyUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script" description="Perform refactorings from a refactoring script on the local workspace" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZykzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZy0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.submitTask" commandName="Submit Task" description="Submits the currently open task" category="_rH7gN0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZzEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.superclass" commandName="Extract Superclass" description="Extract a set of members into a new superclass and try to use the new superclass" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZzUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZzkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.lsp4e.symbolInWorkspace" commandName="Go to Symbol in Workspace" category="_rH7gQUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZz0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.internal.merge.ToggleCurrentChangesCommand" commandName="Ignore Changes from Ancestor to Current Version" description="Toggle ignoring changes only between the ancestor and the current version in a three-way merge comparison" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ0EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.junit.junitShortcut.rerunFailedFirst" commandName="Rerun JUnit Test - Failures First" description="Rerun JUnit Test - Failures First" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ0UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_rH7gQkzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIAZ0kzQEfCUbqAxRfw0FQ" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_rIAZ00zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ1EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ1UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.JavaPerspective" commandName="Java" description="Show the Java perspective" category="_rH7gUkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ1kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.indent" commandName="Correct Indentation" description="Corrects the indentation of the selected lines" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ10zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ2EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ2UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ2kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_rH7gSkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ20zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.with.resources" commandName="Surround with try-with-resources Block" description="Surround the selected text with a try-with-resources block" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ3EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ3UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_rH7gQkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ3kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ30zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ4EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.externalizeStrings" commandName="Externalize Strings in Plug-ins" description="Extract translatable strings from plug-in files" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ4UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_rH7gQkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ4kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.ReplaceWithOurs" commandName="Replace Conflicting Files with Our Revision" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ40zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ5EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.correction.extractMethodInplace.assist" commandName="Quick Assist - Extract method" description="Invokes quick assist and selects 'Extract to method'" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ5UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ5kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ50zQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.disconnected" commandName="Disconnected" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ6EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ6UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ6kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ60zQEfCUbqAxRfw0FQ" elementId="org.springframework.tooling.boot.ls.rewrite.refactor" commandName="Refactor Spring Boot Project..." description="Rewrite Refactorings for Spring Boot projects" category="_rH7gSUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ7EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ7UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_rH7gTUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ7kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ70zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.genericeditor.gotoMatchingBracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ8EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_rH7gPUzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIAZ8UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_rIAZ8kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.new.local.task" commandName="New Local Task" category="_rH7gPEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ80zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ9EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.sort.members" commandName="Sort Members" description="Sort all members using the member order preference" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ9UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ9kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ90zQEfCUbqAxRfw0FQ" elementId="org.eclipse.m2e.discovery.ui" commandName="m2e Marketplace" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ-EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ-UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskIncomplete" commandName="Mark Task Incomplete" category="_rH7gPEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ-kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_rH7gQkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ-0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.correction.assignAllParamsToNewFields.assist" commandName="Quick Assist - Assign all parameters to new fields" description="Invokes quick assist and selects 'Assign all parameters to new fields'" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ_EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ_UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.make.static" commandName="Make Static" description="Make Static" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ_kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToNextUnread" commandName="Mark Task Read and Go To Next Unread Task" category="_rH7gPEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAZ_0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_rH7gMEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaAEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.buildship.ui.commands.rundefaulttasks" commandName="Run Gradle Default Tasks" description="Runs the default tasks of the selected Gradle project" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaAUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaAkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskRead" commandName="Mark Task Read" category="_rH7gPEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaA0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.move.element" commandName="Move - Refactoring " description="Move the selected element to a new location" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaBEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaBUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaBkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_rH7gREzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaB0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_rH7gQkzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIAaCEzQEfCUbqAxRfw0FQ" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_rIAaCUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaCkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.CompareWithCommit" commandName="Compare with Commit..." category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaC0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaDEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateAllTasks" commandName="Deactivate Task" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaDUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaDkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.codemining" commandName="Toggle Code Mining" description="Toggle Code Mining Annotations" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaD0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.m2e.core.ui.command.updateProject" commandName="Update Maven Project" description="Update Maven project configuration and dependencies" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaEEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaEUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaEkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.commitContainer" commandName="Commit" description="Commit the selected container into a new image" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaE0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.organizeManifest" commandName="Organize Manifests" description="Cleans up plug-in manifest files" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaFEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.junit.junitShortcut.run" commandName="Run JUnit Test" description="Run JUnit Test" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaFUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaFkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaF0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaGEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject" commandName="Copy Project" category="_rH7gU0zQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIAaGUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject.newName.parameter.key" name="The name of the new project." optional="false"/>
    <parameters xmi:id="_rIAaGkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ltk.ui.refactoring.commands.copyProject.newLocation.parameter.key" name="The location of the new project." optional="false"/>
  </commands>
  <commands xmi:id="_rIAaG0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the compilation unit" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaHEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_rH7gSkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaHUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.m2e.core.pomFileAction.run" commandName="Run Maven Build" description="Run Maven Build" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaHkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.gitflow.ui.command.init" commandName="Init Gitflow..." category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaH0zQEfCUbqAxRfw0FQ" elementId="org.springframework.tooling.boot.ls.properties.convert-yaml-to-props" commandName="Convert .yaml to .properties" description="Converts Spring Boot properties file from .properties format to .yaml format" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaIEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.gitflow.ui.command.releaseStart" commandName="Start Release" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIAaIUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.m2e.actions.LifeCycleInstall.run" commandName="Run Maven Install" description="Run Maven Install" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqIEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqIUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.gitflow.ui.command.compareWithDevelop" commandName="Compare with develop branch" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqIkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.tagImage" commandName="Add &amp;Tag" description="Add a tag to an Image" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqI0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqJEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.raw.paste" commandName="Raw Paste" description="Paste and ignore smart insert setting" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqJUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_rH7gSEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqJkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqJ0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqKEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqKUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqKkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.commons.ui.command.AddRepository" commandName="Add Repository" category="_rH7gVUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqK0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.commit.DiffEditorQuickOutlineCommand" commandName="Quick Outline" description="Show the quick outline for a unified diff" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqLEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqLUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ant.ui.antShortcut.debug" commandName="Debug Ant Build" description="Debug Ant Build" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqLkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqL0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_rH7gRkzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIEqMEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_rIEqMUzQEfCUbqAxRfw0FQ" elementId="org.springframework.ide.eclipse.boot.dash.ShowBootDashboard" commandName="Boot Dashboard" description="Show Boot Dashboard view" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqMkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.execContainer" commandName="Execute Shell" description="Get an interactive shell into this container" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqM0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_rH7gSEzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIEqNEzQEfCUbqAxRfw0FQ" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_rIEqNUzQEfCUbqAxRfw0FQ" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_rIEqNkzQEfCUbqAxRfw0FQ" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_rIEqN0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.addConnection" commandName="&amp;Add Connection" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqOEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqOUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.m2e.core.ui.command.addDependency" commandName="Add Maven Dependency" description="Add Maven dependency" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqOkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.debug.ui.commands.Execute" commandName="Execute" description="Evaluate selected text" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqO0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqPEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqPUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_rH7gQkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqPkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.server.launchShortcut.run" commandName="Run on Server" description="Run the current selection on a server" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqP0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqQEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.server.launchShortcut.debug" commandName="Debug on Server" description="Debug the current selection on a server" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqQUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.m2e.editor.RenameArtifactAction" commandName="Rename Maven Artifact..." category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqQkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqQ0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.displayContainerLog" commandName="Display Log" description="Display the log for the selected container in the Console" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqREzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqRUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.api.tools.ui.remove.filters" commandName="Remove API Problem Filters..." description="Remove API problem filters for this project" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqRkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.tm.terminal.view.ui.command.newview" commandName="New Terminal View" category="_rH7gQ0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqR0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqSEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_rH7gQkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqSUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqSkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.gitflow.ui.command.featureStart" commandName="Start Feature" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqS0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the compilation unit" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqTEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_rH7gWEzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIEqTUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_rIEqTkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqT0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.hashcode.equals" commandName="Generate hashCode() and equals()" description="Generates hashCode() and equals() methods for the type" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqUEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_rH7gPUzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIEqUUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_rIEqUkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqU0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showInWebBrowser" commandName="Web Browser" description="Show in Web Browser" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqVEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_rH7gUEzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIEqVUzQEfCUbqAxRfw0FQ" elementId="title" name="Title"/>
    <parameters xmi:id="_rIEqVkzQEfCUbqAxRfw0FQ" elementId="message" name="Message"/>
    <parameters xmi:id="_rIEqV0zQEfCUbqAxRfw0FQ" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_rIEqWEzQEfCUbqAxRfw0FQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_rIEqWUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqWkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqW0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskComplete" commandName="Mark Task Complete" category="_rH7gPEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqXEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqXUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqXkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqX0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqYEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showAllContainers" commandName="&amp;Show all Containers" description="Show all Containers, including non-running ones." category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqYUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_rH7gREzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqYkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqY0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqZEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqZUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeImages" commandName="Re&amp;move " description="Remove the selected images" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqZkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository..." description="Adds an existing Git repository to the Git Repositories view" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqZ0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.debug.ui.breakpoint.properties" commandName="Java Breakpoint Properties" description="View and edit the properties for a given Java breakpoint" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqaEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqaUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearActiveTime" commandName="Clear Active Time" category="_rH7gPEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqakzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqa0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.buildship.ui.commands.refreshproject" commandName="Refresh Gradle Project" description="Synchronizes the Gradle builds of the selected projects with the workspace" category="_rH7gOEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqbEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqbUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.context.ui.commands.task.attachContext" commandName="Attach Context" category="_rH7gO0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqbkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqb0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.tm.terminal.connector.local.command.launch" commandName="Open Local Terminal on Selection" category="_rH7gQ0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqcEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_rH7gRkzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIEqcUzQEfCUbqAxRfw0FQ" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_rIEqckzQEfCUbqAxRfw0FQ" elementId="org.eclipse.m2e.sourcelookup.ui.openSourceLookupInfoDialog" commandName="Source Lookup Info" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqc0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqdEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqdUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqdkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqd0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqeEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqeUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.debug" commandName="Debug OSGi Framework" description="Debug OSGi Framework" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqekzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqe0zQEfCUbqAxRfw0FQ" elementId="org.springframework.tooling.boot.ls.rewrite.boot-upgrade" commandName="Upgrade Spring Boot Version..." description="Upgrade Spring Boot Version for a Spring Boot project" category="_rH7gSUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqfEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeTag" commandName="&amp;Remove Tag" description="Remove a tag from an Image with multiple tags" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqfUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqfkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqf0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqgEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqgUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqgkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqg0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_rH7gTUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqhEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqhUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.sse.ui.add.block.comment" commandName="Add Block Comment" description="Add Block Comment" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqhkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqh0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.gitflow.ui.command.replaceWithDevelop" commandName="Replace with develop branch" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqiEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.debug.ui.commands.AllInstances" commandName="All Instances" description="View all instances of the selected type loaded in the target VM" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqiUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" description="Opens selected commit(s) in Commit Viewer(s)" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqikzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.correction.assignInTryWithResources.assist" commandName="Quick Assist - Assign to variable in new try-with-resources block" description="Invokes quick assist and selects 'Assign to variable in new try-with-resources block'" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqi0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqjEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.m2e.actions.LifeCycleTest.run" commandName="Run Maven Test" description="Run Maven Test" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqjUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqjkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeContainerLog" commandName="Remove Log" description="Remove the console log for the selected container" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqj0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to field" description="Invokes quick assist and selects 'Assign parameter to field'" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqkEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqkUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.move.inner.to.top.level" commandName="Move Type to New File" description="Move Type to New File" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqkkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqk0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.sse.ui.open.file.from.source" commandName="Open Selection" description="Open an editor on the selected link" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqlEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.hierarchy" commandName="Quick Hierarchy" description="Show the quick hierarchy of the selected element" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqlUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqlkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.sse.ui.goto.matching.bracket" commandName="Matching Character" description="Go to Matching Character" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEql0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.FetchGiteaPullRequest" commandName="Fetch Gitea Pull Request" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqmEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.refactor.quickMenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqmUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.lsp4e.selectionRange.up" commandName="Enclosing Element" description="Expand Selection To Enclosing Element" category="_rH7gQUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqmkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqm0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.override.methods" commandName="Override/Implement Methods" description="Override or implement methods from super types" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqnEzQEfCUbqAxRfw0FQ" elementId="org.springframework.ide.eclipse.boot.commands.editStartersCommand2" commandName="Edit Starters 2" category="_rH7gNUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqnUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.lsp4e.selectionRange.down" commandName="Restore To Last Selection" description="Expand Selection To Restore To Last Selection" category="_rH7gQUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqnkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToDocbookCommand" commandName="Generate Docbook" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqn0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqoEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.change.type" commandName="Generalize Declared Type" description="Change the declaration of a selected variable to a more general type consistent with usage" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqoUzQEfCUbqAxRfw0FQ" elementId="org.springframework.ide.eclipse.boot.restart.commands.restart" commandName="Trigger Restart" description="Restart Spring Boot Application" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqokzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository..." description="Clones a Git repository and adds the clone to the Git Repositories view" category="_rH7gUUzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIEqo0zQEfCUbqAxRfw0FQ" elementId="repositoryUri" name="Repository URI"/>
  </commands>
  <commands xmi:id="_rIEqpEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqpUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.sse.ui.quick_outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqpkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.textblock" commandName="Add Text Block" description="Adds Text Block" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqp0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqqEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ant.ui.openExternalDoc" commandName="Open External Documentation" description="Open the External documentation for the current task in the Ant editor" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqqUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Git Repository..." description="Creates a new Git repository and adds it to the Git Repositories view" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqqkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.gitflow.ui.command.featureFinish" commandName="Finish Feature" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqq0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqrEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_rH7gRUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqrUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.tm4e.languageconfiguration.removeBlockCommentCommand" commandName="Remove Block Comment" category="_rH7gM0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqrkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqr0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqsEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_rH7gSEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqsUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch..." category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqskzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqs0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.restartContainers" commandName="Res&amp;tart" description="Restart selected containers" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqtEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.context.ui.commands.open.context.dialog" commandName="Show Context Quick View" description="Show Context Quick View" category="_rH7gO0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqtUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_rH7gQkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqtkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.CompareWithRef" commandName="Compare with Branch, Tag or Reference..." category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqt0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.context.ui.commands.attachment.retrieveContext" commandName="Retrieve Context Attachment" category="_rH7gO0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEquEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEquUzQEfCUbqAxRfw0FQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.RestartAction" commandName="(Re)start" description="(Re)start Boot App" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqukzQEfCUbqAxRfw0FQ" elementId="editor.action.triggerSuggest" commandName="Invoke Content Assist" category="_rH7gWEzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIEqu0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.lsp4e.path.param" name="Resource Path (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.pathParameterType"/>
    <parameters xmi:id="_rIEqvEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.lsp4e.command.param" name="Command id (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.commandParameterType"/>
  </commands>
  <commands xmi:id="_rIEqvUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.pull.up" commandName="Pull Up" description="Move members to a superclass" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqvkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqv0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.RefreshRepositoryTasks" commandName="Synchronize Changed" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqwEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqwUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_rH7gWEzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIEqwkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_rIEqw0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqxEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqxUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqxkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqx0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqyEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqyUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqykzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.context.ui.commands.interest.increment" commandName="Make Landmark" description="Make Landmark" category="_rH7gO0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqy0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.gitflow.ui.command.developCheckout" commandName="Check Out Develop" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqzEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqzUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.api.tools.ui.convert.javadocs" commandName="Convert API Tools Javadoc Tags..." description="Starts a wizard that will allow you to convert existing Javadoc tags to annotations" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqzkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesViewShowInSystemExplorer" commandName="Show In System Explorer" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEqz0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.external.javadoc" commandName="Open Attached Javadoc" description="Open the attached Javadoc of the selected element in a browser" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq0EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.run" commandName="Run OSGi Framework" description="Run OSGi Framework" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq0UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq0kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.sse.ui.structure.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq00zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq1EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.debug.ui.commands.AddClassPrepareBreakpoint" commandName="Add Class Load Breakpoint" description="Add a class load breakpoint" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq1UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesCreateGroup" commandName="Create a Repository Group" description="Create a repository group for structuring repositories in the Git Repositories view" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq1kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq10zQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_rH7gUkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq2EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.tips.ide.command.open" commandName="Tip of the Day" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq2UzQEfCUbqAxRfw0FQ" elementId="AnsiConsole.command.copy_without_escapes" commandName="Copy Text Without ANSI Escapes" description="Copy the console content to clipboard, removing the escape sequences" category="_rH7gPkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq2kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.gitflow.ui.command.hotfixFinish" commandName="Finish Hotfix" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq20zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.specific_content_assist.command" commandName="Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_rH7gNEzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIEq3EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_rIEq3UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq3kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq30zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_rH7gQkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq4EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.method.exits" commandName="Search Method Exit Occurrences in File" description="Search for method exit occurrences of a selected return type" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq4UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq4kzQEfCUbqAxRfw0FQ" elementId="org.springframework.ide.eclipse.boot.properties.editor.convertYamlToProperties" commandName="Convert .yaml to .properties" category="_rH7gNUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq40zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_rH7gQkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq5EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq5UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq5kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq50zQEfCUbqAxRfw0FQ" elementId="org.eclipse.m2e.sourcelookup.ui.importBinaryProject" commandName="Import Binary Project" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq6EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq6UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq6kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.promote.local.variable" commandName="Convert Local Variable to Field" description="Convert a local variable to a field" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq60zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq7EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq7UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq7kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq70zQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.copyfromcontainer" commandName="Copy from Container" description="Copy files from running Container to a local directory" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq8EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_rH7gSkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq8UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.self.encapsulate.field" commandName="Encapsulate Fields" description="Create getting and setting methods for the field and use only those to access the field" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq8kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq80zQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.run" commandName="Run Eclipse Application" description="Run Eclipse Application" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq9EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq9UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.sse.ui.remove.block.comment" commandName="Remove Block Comment" description="Remove Block Comment" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq9kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.runtime.spy.commands.menuSpyCommand" commandName="Plug-in Menu Spy" description="Show the Plug-in Spy" category="_rH7gVkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq90zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq-EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.m2e.core.ui.command.openPom" commandName="Open Maven POM" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq-UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.index.rebuild" commandName="Rebuild Java Index" description="Rebuilds the Java index database" category="_rH7gSkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq-kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq-0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq_EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.goToPreviousUnread" commandName="Go To Previous Unread Task" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq_UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq_kzQEfCUbqAxRfw0FQ" elementId="org.springframework.ide.eclipse.boot.ui.EnableDisableBootDevtools" commandName="Add/Remove Boot Devtools" category="_rH7gNUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEq_0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_rH7gMUzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIErAEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_rIErAUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_rIErAkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_rIErA0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErBEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErBUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_rH7gPUzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIErBkzQEfCUbqAxRfw0FQ" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_rIErB0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErCEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_rH7gQkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErCUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErCkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErC0zQEfCUbqAxRfw0FQ" elementId="org.springframework.ide.eclipse.boot.commands.editStartersCommand" commandName="Edit Starters" category="_rH7gNUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErDEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.openPluginArtifact" commandName="Open Plug-in Artifact" description="Open a plug-in artifact in the manifest editor" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErDUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.server.debug" commandName="Debug" description="Debug server" category="_rH7gP0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErDkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErD0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter" commandName="Introduce Parameter" description="Introduce a new method parameter based on the selected expression" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErEEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.addTaskRepository" commandName="Add Task Repository..." category="_rH7gPEzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIErEUzQEfCUbqAxRfw0FQ" elementId="connectorKind" name="Repository Type"/>
  </commands>
  <commands xmi:id="_rIErEkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.run" commandName="Run Java Application" description="Run Java Application" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErE0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErFEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.updateUnitVersions" commandName="Update IU Versions from Repositories" description="Update to latest IU versions" category="_rH7gV0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErFUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.sse.ui.structure.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErFkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_rH7gSEzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIErF0zQEfCUbqAxRfw0FQ" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_rIErGEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.viewSource.command" commandName="View Unformatted Text" category="_rH7gPEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErGUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showInPropertiesView" commandName="Properties" description="Show in Properties View" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErGkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErG0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErHEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.searchTargetRepositories" commandName="Add Artifact to Target Platform" description="Add an artifact to your target platform" category="_rH7gWEzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIErHUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.searchTargetRepositories.term" name="The initial search pattern for the artifact search dialog"/>
  </commands>
  <commands xmi:id="_rIErHkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErH0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErIEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErIUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_rH7gSkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErIkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.PullWithOptions" commandName="Pull..." category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErI0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.lsp4e.toggleLinkWithEditor" commandName="Toggle Link with Editor" category="_rH7gQUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErJEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErJUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.server.publish" commandName="Publish" description="Publish to server" category="_rH7gP0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErJkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.factory" commandName="Introduce Factory" description="Introduce a factory method to encapsulate invocation of the selected constructor" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErJ0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.updateClasspath" commandName="Update Classpath" description="Updates the plug-in classpath from latest settings" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErKEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.use.supertype" commandName="Use Supertype Where Possible" description="Change occurrences of a type to use a supertype instead" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErKUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskUnread" commandName="Mark Task Unread" category="_rH7gPEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErKkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErK0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.sse.ui.structure.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErLEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErLUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.tm.terminal.view.ui.command.launch" commandName="Open Terminal on Selection" category="_rH7gQ0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErLkzQEfCUbqAxRfw0FQ" elementId="org.springsource.ide.eclipse.commons.ui.stop" commandName="Stop Application" description="Stop last launched application" category="_rH7gS0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErL0zQEfCUbqAxRfw0FQ" elementId="java.execute.workspaceCommand" commandName="Execute Java Command in Workspace" category="_rH7gWEzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIErMEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.lsp4e.path.param" name="Resource Path (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.pathParameterType"/>
    <parameters xmi:id="_rIErMUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.lsp4e.command.param" name="Command id (unnecessary, only to make lsp4e happy)" typeId="org.eclipse.lsp4e.commandParameterType"/>
  </commands>
  <commands xmi:id="_rIErMkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErM0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.correction.encapsulateField.assist" commandName="Quick Assist - Create getter/setter for field" description="Invokes quick assist and selects 'Create getter/setter for field'" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErNEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_rH7gSEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErNUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.ReplaceWithPrevious" commandName="Replace with Previous Revision" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErNkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.m2e.sourcelookup.ui.openPom" commandName="Open Pom" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErN0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErOEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErOUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_rH7gSEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErOkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.removeConnection" commandName="&amp;Remove" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErO0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErPEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.api.tools.ui.setup.projects" commandName="API Tools Setup..." description="Configure projects for API usage and compatibility checks" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErPUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.debug" commandName="Debug Java Application" description="Debug Java Application" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErPkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErP0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.CherryPick" commandName="Cherry Pick" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErQEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.buildship.ui.commands.openbuildscript" commandName="Open Gradle Build Script" description="Opens the Gradle build script for the selected Gradle project" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErQUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.FetchGitHubPR" commandName="Fetch GitHub Pull Request" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErQkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.buildship.ui.commands.openrunconfiguration" commandName="Open Gradle Run Configuration" description="Opens the Run Configuration for the selected Gradle tasks" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErQ0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErREzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErRUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErRkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErR0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErSEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErSUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_rH7gTUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErSkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErS0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_rH7gREzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErTEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_rH7gU0zQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIErTUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_rIErTkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.importFromRepository" commandName="Import from a Source Repository" description="Imports a plug-in from a source repository" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErT0zQEfCUbqAxRfw0FQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.StopAction" commandName="Stop" description="Stop Boot App" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErUEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErUUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.previousTask" commandName="Previous Task Command" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErUkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_rH7gQkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErU0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErVEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErVUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErVkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToPreviousUnread" commandName="Mark Task Read and Go To Previous Unread Task" category="_rH7gPEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErV0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.constant" commandName="Extract Constant" description="Extracts a constant into a new static field and uses the new static field" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErWEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErWUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.sse.ui.cleanup.document" commandName="Cleanup Document..." description="Cleanup document" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErWkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.gitflow.ui.command.releaseFinish" commandName="Finish Release" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErW0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErXEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErXUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErXkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.gitflow.ui.command.hotfixStart" commandName="Start Hotfix" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErX0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErYEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify field access" description="Invokes quick assist and selects 'Qualify field access'" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErYUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErYkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.sse.ui.format" commandName="Format" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErY0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErZEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErZUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErZkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErZ0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.m2e.core.ui.command.addPlugin" commandName="Add Maven Plugin" description="Add Maven plugin" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEraEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.modify.method.parameters" commandName="Change Method Signature" description="Change method signature includes parameter names and parameter order" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEraUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErakzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable (replace all occurrences)" description="Invokes quick assist and selects 'Extract local variable (replace all occurrences)'" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEra0zQEfCUbqAxRfw0FQ" elementId="org.springsource.ide.eclipse.commons.ui.relaunch" commandName="Relaunch Application" description="Relaunch last launched application" category="_rH7gS0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErbEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErbUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErbkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_rH7gSEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErb0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.lsp4e.openCallHierarchy" commandName="Open Call Hierarchy" description="Open Call Hierarchy for the selected item" category="_rH7gQUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErcEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_rH7gREzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErcUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showAllImages" commandName="&amp;Show all Images" description="Show all Images, including intermediate images." category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErckzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch..." category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErc0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErdEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErdUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_rH7gUkzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIErdkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_rIErd0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_rIEreEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEreUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErekzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.debug" commandName="Debug Java Applet" description="Debug Java Applet" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEre0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErfEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErfUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErfkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErf0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_rH7gREzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErgEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_rH7gSkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErgUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.filediff.OpenPrevious" commandName="Open Previous Version" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErgkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErg0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.tips.ide.command.trim.open" commandName="Tip of the Day" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErhEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErhUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_rH7gSkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErhkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErh0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEriEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.debug" commandName="Debug JUnit Plug-in Test" description="Debug JUnit Plug-in Test" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEriUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.correction.extractLocalNotReplaceOccurrences.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErikzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEri0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_rH7gU0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErjEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_rH7gQkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErjUzQEfCUbqAxRfw0FQ" elementId="org.springframework.tooling.boot.ls.modulith.metadata.refresh" commandName="Refresh Modulith Metadata" description="Refresh project's Modulith metadata and re-validate the project" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErjkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.debug.ui.commands.AllReferences" commandName="All References" description="Inspect all references to the selected object" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErj0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.junit.junitShortcut.rerunLast" commandName="Rerun JUnit Test" description="Rerun JUnit Test" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErkEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErkUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.sse.ui.format.active.elements" commandName="Format Active Elements" description="Format active elements" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErkkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErk0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.showInSystemExplorer" commandName="System Explorer" description="%command.showInSystemExplorer.menu.description" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErlEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErlUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearOutgoing" commandName="Clear Outgoing Changes" category="_rH7gPEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErlkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErl0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErmEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErmUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErmkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_rH7gSEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErm0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.infer.type.arguments" commandName="Infer Generic Type Arguments" description="Infer type arguments for references to generic classes and remove unnecessary casts" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErnEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.gitflow.ui.command.featureTrack" commandName="Track Feature" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErnUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErnkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.m2e.actions.LifeCycleClean.run" commandName="Run Maven Clean" description="Run Maven Clean" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErn0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEroEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEroUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToEclipseHelpCommand" commandName="Generate Eclipse Help (*.html and *-toc.xml)" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErokzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEro0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.debug.ui.commands.ForceReturn" commandName="Force Return" description="Forces return from method with value of selected expression" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErpEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErpUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErpkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.lsp4e.toggleSortOutline" commandName="Sort" category="_rH7gQUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErp0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_rH7gUEzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIErqEzQEfCUbqAxRfw0FQ" elementId="title" name="Title"/>
    <parameters xmi:id="_rIErqUzQEfCUbqAxRfw0FQ" elementId="message" name="Message"/>
    <parameters xmi:id="_rIErqkzQEfCUbqAxRfw0FQ" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_rIErq0zQEfCUbqAxRfw0FQ" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_rIErrEzQEfCUbqAxRfw0FQ" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_rIErrUzQEfCUbqAxRfw0FQ" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_rIErrkzQEfCUbqAxRfw0FQ" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_rIErr0zQEfCUbqAxRfw0FQ" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_rIErsEzQEfCUbqAxRfw0FQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_rIErsUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErskzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErs0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErtEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErtUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.debug.ui.command.OpenFromClipboard" commandName="Open from Clipboard" description="Opens a Java element or a Java stack trace from clipboard" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErtkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.internationalize" commandName="Internationalize Plug-ins" description="Sets up internationalization for a plug-in" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErt0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEruEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEruUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErukzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEru0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErvEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.JavaHierarchyPerspective" commandName="Java Type Hierarchy" description="Show the Java Type Hierarchy perspective" category="_rH7gUkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErvUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.multicatch" commandName="Surround with try/multi-catch Block" description="Surround the selected text with a try/multi-catch block" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErvkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.Tag" commandName="Create Tag..." category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErv0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErwEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErwUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_rH7gWEzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIErwkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_rIErw0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_rIErxEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErxUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErxkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter.object" commandName="Introduce Parameter Object" description="Introduce a parameter object to a selected method" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErx0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.stopContainers" commandName="&amp;Stop" description="Stop the selected containers" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEryEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.commands.openElementInEditor" commandName="Open Java Element" description="Open a Java element in its editor" category="_rH7gPUzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIEryUzQEfCUbqAxRfw0FQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_rIErykzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEry0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.buildship.ui.commands.refreshtaskview" commandName="Refresh View (Gradle Tasks)" description="Refreshes the Gradle Tasks view" category="_rH7gMUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErzEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.sse.ui.toggle.comment" commandName="Toggle Comment" description="Toggle Comment" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErzUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_rH7gSEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErzkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIErz0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr0EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr0UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr0kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr00zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr1EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr1UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_rH7gMEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr1kzQEfCUbqAxRfw0FQ" elementId="spring.initializr.addStarters" commandName="Add Spring Boot Starters" description="Adds Spring Boot Starters dependencies">
    <parameters xmi:id="_rIEr10zQEfCUbqAxRfw0FQ" elementId="org.eclipse.lsp4e.command.param" name="command" typeId="org.eclipse.lsp4e.commandParameterType"/>
    <parameters xmi:id="_rIEr2EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.lsp4e.path.param" name="path" typeId="org.eclipse.lsp4e.pathParameterType"/>
  </commands>
  <commands xmi:id="_rIEr2UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.generate.javadoc" commandName="Generate Javadoc" description="Generates Javadoc for a selectable set of Java resources" category="_rH7gSkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr2kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr20zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.debug.ui.commands.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr3EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.buildship.ui.shortcut.test.run" commandName="Run Gradle Test" description="Run Gradle test based on the current selection" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr3UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr3kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr30zQEfCUbqAxRfw0FQ" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_rH7gTUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr4EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr4UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.filterContainersWithLabels" commandName="Filter by &amp;Labels" description="Show containers that have specified labels." category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr4kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.lsp4e.symbolInFile" commandName="Go to Symbol in File" category="_rH7gQUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr40zQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToHtmlCommand" commandName="Generate HTML" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr5EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.openManifest" commandName="Open Manifest" description="Open the plug-in manifest" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr5UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr5kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.enableConnection" commandName="&amp;Enable Connection" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr50zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.tostring" commandName="Generate toString()" description="Generates the toString() method for the type" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr6EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.organize.imports" commandName="Organize Imports" description="Evaluate all required imports and replace the current imports" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr6UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr6kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr60zQEfCUbqAxRfw0FQ" elementId="org.springsource.ide.eclipse.boot.BootLaunchShortcut.debug" commandName="Debug Spring Boot App" description="Debug Spring Boot App" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr7EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.api.tools.ui.compare.to.baseline" commandName="API Baseline..." description="Allows to compare the selected resource with the current baseline" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr7UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.imagebrowser.saveToWorkspace" commandName="Save Image" description="Save the selected image into a project in the workspace" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr7kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.debug.ui.commands.Display" commandName="Display" description="Display result of evaluating selected text" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr70zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr8EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.tm.terminal.view.ui.command.disconnect" commandName="Disconnect Terminal" category="_rH7gQ0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr8UzQEfCUbqAxRfw0FQ" elementId="org.springframework.ide.eclipse.boot.dash.boot.dash.RedebugAction" commandName="(Re)debug" description="(Re)debug Boot App" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr8kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Revision Information" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr80zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to field" description="Invokes quick assist and selects 'Assign to field'" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr9EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr9UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.method" commandName="Extract Method" description="Extract a set of statements or an expression into a new method and use the new method" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr9kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.openRemoteTask" commandName="Open Remote Task" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr90zQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr-EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.CompareWithEachOther" commandName="Compare with Each Other" description="Compare two files selected in the Compare Editor with each other." category="_rH7gREzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr-UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.context.ui.commands.task.retrieveContext" commandName="Retrieve Context" category="_rH7gO0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr-kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr-0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.run" commandName="Run JUnit Plug-in Test" description="Run JUnit Plug-in Test" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr_EzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.navigate.gotopackage" commandName="Go to Package" description="Go to Package" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr_UzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr_kzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.task.ui.editor.QuickOutline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_rH7gPEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEr_0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEsAEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.buildship.ui.commands.addbuildshipnature" commandName="Add Gradle Nature" description="Adds the Gradle nature and synchronizes this project as if the Gradle Import wizard had been run on its location." category="_rH7gOEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEsAUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEsAkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEsA0zQEfCUbqAxRfw0FQ" elementId="org.springframework.tooling.boot.ls.properties.convert-props-to-yaml" commandName="Convert .properties to .yaml" description="Converts Spring Boot properties file from .yaml format to .properties format" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEsBEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.lsp4e.typeHierarchy" commandName="Quick Type Hierarchy" description="Open Quick Call Hierarchy for the selected item" category="_rH7gQUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEsBUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEsBkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEsB0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEsCEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEsCUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.unpauseContainers" commandName="&amp;Unpause" description="Unpause the selected containers" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEsCkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEsC0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.server.run" commandName="Run" description="Run server" category="_rH7gP0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIEsDEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.context.ui.commands.focus.view" commandName="Focus View" category="_rH7gWEzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIEsDUzQEfCUbqAxRfw0FQ" elementId="viewId" name="View ID to Focus" optional="false"/>
  </commands>
  <commands xmi:id="_rIEsDkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtcEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_rH7gRkzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIHtcUzQEfCUbqAxRfw0FQ" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_rIHtckzQEfCUbqAxRfw0FQ" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_rIHtc0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_rH7gSEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtdEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.common.project.facet.ui.ConvertProjectToFacetedForm" commandName="Convert to Faceted Form..." category="_rH7gQkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtdUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.annotate.classFile" commandName="Annotate Class File" description="Externally add Annotations to a Class File." category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtdkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtd0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.command.activateSelectedTask" commandName="Activate Selected Task" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHteEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHteUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.sse.ui.format.document" commandName="Format" description="Format selection" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtekzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHte0zQEfCUbqAxRfw0FQ" elementId="org.sonatype.m2e.egit.CloneAsMavenProjects" commandName="Clone Maven Projects..." category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtfEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtfUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_rH7gQkzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIHtfkzQEfCUbqAxRfw0FQ" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_rIHtf0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtgEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected Java comment lines" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtgUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtgkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with Each Other" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtg0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHthEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHthUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.killContainers" commandName="&amp;Kill" description="Kill the selected containers" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHthkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.context.ui.commands.interest.decrement" commandName="Make Less Interesting" description="Make Less Interesting" category="_rH7gO0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHth0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtiEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Check Out" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtiUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_rH7gTkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtikzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHti0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_rH7gWEzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIHtjEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_rIHtjUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_rIHtjkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.gotoBreadcrumb" commandName="Show In Breadcrumb" description="Shows the Java editor breadcrumb and sets the keyboard focus into it" category="_rH7gPUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtj0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.debug" commandName="Debug Eclipse Application" description="Debug Eclipse Application" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtkEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.class" commandName="Extract Class..." description="Extracts fields into a new class" category="_rH7gR0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtkUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_rH7gVEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtkkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_rH7gREzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtk0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtlEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.gitflow.ui.command.featureRebase" commandName="Rebase Feature" category="_rH7gUUzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtlUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtlkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtl0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.linuxtools.docker.ui.commands.refreshExplorerView" commandName="&amp;Refresh" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtmEzQEfCUbqAxRfw0FQ" elementId="org.springsource.ide.eclipse.boot.BootLaunchShortcut.run" commandName="Run Spring Boot App" description="Run Spring Boot App" category="_rH7gT0zQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtmUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_rH7gOkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtmkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.sse.ui.search.find.occurrences" commandName="Occurrences in File" description="Find occurrences of the selection in the file" category="_rH7gNEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtm0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_rH7gRkzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtnEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.sse.ui.outline.customFilter" commandName="&amp;Filters" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rIHtnUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_rH7gPUzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIHtnkzQEfCUbqAxRfw0FQ" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_rIHtn0zQEfCUbqAxRfw0FQ" elementId="org.springframework.tooling.ls.eclipse.commons.commands.OpenJavaElementInEditor" commandName="Open Java Element in Editor" category="_rH7gWEzQEfCUbqAxRfw0FQ">
    <parameters xmi:id="_rIHtoEzQEfCUbqAxRfw0FQ" elementId="bindingKey" name="bindingKey" optional="false"/>
    <parameters xmi:id="_rIHtoUzQEfCUbqAxRfw0FQ" elementId="projectName" name="projectName" optional="false"/>
  </commands>
  <commands xmi:id="_rM-YQEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.convertAutomaticManifest" commandName="org.eclipse.pde.ui.convertAutomaticManifest"/>
  <commands xmi:id="_rZApIEzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.ant.ui.actionSet.presentation/org.eclipse.ant.ui.toggleAutoReconcile" commandName="Toggle Ant Editor Auto Reconcile" description="Toggle Ant Editor Auto Reconcile" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZApIUzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZApIkzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZApI0zQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZApJEzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZApJUzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZApJkzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZApJ0zQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZApKEzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZApKUzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZApKkzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.egit.ui.SearchActionSet/org.eclipse.egit.ui.actions.OpenCommitSearchPage" commandName="Git..." category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZM2YEzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New Java Class" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZNdcEzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenPackageWizard" commandName="Package..." description="New Java Package" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZNdcUzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenProjectWizard" commandName="Java Project..." description="New Java Project" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZNdckzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.SearchActionSet/org.eclipse.jdt.ui.actions.OpenJavaSearchPage" commandName="Java..." category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZOEgEzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZOEgUzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZOEgkzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.linuxtools.docker.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZOEg0zQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.navigation.additions/org.eclipse.mylyn.tasks.ui.navigate.task.history" commandName="Activate Previous Task" description="Activate Previous Task" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZOrkEzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.pde.ui.SearchActionSet/org.eclipse.pde.ui.actions.OpenPluginSearchPage" commandName="Plug-in..." category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZOrkUzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZOrkkzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZOrk0zQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZOrlEzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZOrlUzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZPSoEzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.new.actionSet/org.eclipse.wst.server.ui.action.new.server" commandName="Create Server" description="Create Server" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZPSoUzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.open" commandName="Open Web Browser" description="Open Web Browser" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZPSokzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.switch" commandName="Web Browser" description="Web Browser" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZPSo0zQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.springsource.ide.eclipse.commons.launch.actionSet/org.springsource.ide.eclipse.commons.launch.relaunch.action" commandName="Relaunch" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZP5sEzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.springsource.ide.eclipse.commons.launch.actionSet/org.springsource.ide.eclipse.commons.launch.stop.action" commandName="Terminate" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZP5sUzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.ant.ui.BreakpointRulerActions/org.eclipse.ant.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZP5skzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZP5s0zQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZP5tEzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZP5tUzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="Run to Line" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZQgwEzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetExecute" commandName="Execute" description="Execute the Selected Text" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZQgwUzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetDisplay" commandName="Display" description="Display Result of Evaluating Selected Text" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZQgwkzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetInspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZQgw0zQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZQgxEzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZRH0EzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZRH0UzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZRH0kzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZRH00zQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.lsp4e.debug.textEditor.rulerActions/org.eclipse.lsp4e.debug.textEditor.doubleClickBreakpointAction" commandName="unusedlabel" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZRH1EzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.lsp4e.debug.genericEditor.rulerActions/org.eclipse.lsp4e.debug.genericEditor.doubleClickBreakpointAction" commandName="unusedlabel" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZRH1UzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution/org.eclipse.m2e.jdt.ui.downloadSourcesAction" commandName="label" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZRu4EzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution_38/org.eclipse.m2e.jdt.ui.downloadSourcesAction_38" commandName="label" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZRu4UzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Text Editor Bookmark Ruler Action" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZRu4kzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZRu40zQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZRu5EzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZSV8EzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZSV8UzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZSV8kzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZSV80zQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZSV9EzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZSV9UzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZSV9kzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZSV90zQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZTkEEzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZTkEUzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZTkEkzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZTkE0zQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZTkFEzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZTkFUzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZULIEzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZULIUzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZULIkzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZULI0zQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZULJEzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZULJUzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variablesViewActions.AllReferencesInView" commandName="Show References" description="Shows references to each object in the variables view as an array of objects." category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZUyMEzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZUyMUzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZUyMkzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZUyM0zQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZVZQEzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZVZQUzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.AllReferencesInView" commandName="Show References" description="Show &amp;References" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZVZQkzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZVZQ0zQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZVZREzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZWAUEzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZWAUUzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.actions.AddException" commandName="Add Java Exception Breakpoint" description="Add Java Exception Breakpoint" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZWAUkzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.breakpointViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZWAU0zQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowThreadGroups" commandName="Show Thread Groups" description="Show Thread Groups" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZWnYEzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZWnYUzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowSystemThreads" commandName="Show System Threads" description="Show System Threads" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZWnYkzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowRunningThreads" commandName="Show Running Threads" description="Show Running Threads" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZWnY0zQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowMonitorThreadInfo" commandName="Show Monitors" description="Show the Thread &amp; Monitor Information" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZWnZEzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Watch" commandName="Watch" description="Create a Watch Expression from the Selected Text" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZWnZUzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Execute" commandName="Execute" description="Execute the Selected Text" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZWnZkzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Display" commandName="Display" description="Display Result of Evaluating Selected Text" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZWnZ0zQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Inspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZXOcEzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.mylyn.context.ui.outline.contribution/org.eclipse.mylyn.context.ui.contentOutline.focus" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZXOcUzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.synchronize.changed" commandName="Synchronize Changed" description="Synchronize Changed" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZXOckzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.tasks.restore" commandName="Restore Tasks from History..." category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZXOc0zQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.open.repositories.view" commandName="Show Task Repositories View" description="Show Task Repositories View" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZXOdEzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.doc.legend.show.action" commandName="Show UI Legend" description="Show Tasks UI Legend" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZXOdUzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.context.ui.actions.tasklist.focus" commandName="Focus on Workweek" description="Focus on Workweek" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <commands xmi:id="_rZXOdkzQEfCUbqAxRfw0FQ" elementId="AUTOGEN:::org.eclipse.pde.ui.logViewActions/org.eclipse.jdt.debug.ui.LogViewActions.showStackTrace" commandName="Show Stack Trace in Console View" description="Show Stack Trace in Console View" category="_rH7gWEzQEfCUbqAxRfw0FQ"/>
  <addons xmi:id="_rHW4fkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_rHW4f0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_rHW4gEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_rHW4gUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_rHW4gkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_rHW4g0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_rHW4hEzQEfCUbqAxRfw0FQ" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_rHW4hUzQEfCUbqAxRfw0FQ" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_rHW4hkzQEfCUbqAxRfw0FQ" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_rHW4h0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_rHgpcEzQEfCUbqAxRfw0FQ" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_IYS0IKimEeS11vbz3f9ezw" elementId="org.eclipse.ui.ide.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_dz0JgGOlEeSMMaPQU2nlzw" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="_rH7gMEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_rH7gMUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_rH7gMkzQEfCUbqAxRfw0FQ" elementId="org.springframework.tooling.ls.eclipse.gotosymbol.commands.category" name="STS4"/>
  <categories xmi:id="_rH7gM0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.tm4e.languageconfiguration.category" name="TM4E Language Configuration"/>
  <categories xmi:id="_rH7gNEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_rH7gNUzQEfCUbqAxRfw0FQ" elementId="org.springframework.ide.eclipse.boot.commands.category" name="Spring Boot"/>
  <categories xmi:id="_rH7gNkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.category" name="WikiText Markup Editing Commands" description="commands for editing lightweight markup"/>
  <categories xmi:id="_rH7gN0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.category.editor" name="Task Editor"/>
  <categories xmi:id="_rH7gOEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.buildship.ui.project" name="Buildship" description="Contains the Buildship specific commands"/>
  <categories xmi:id="_rH7gOUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_rH7gOkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_rH7gO0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.context.ui.commands" name="Focused UI" description="Task-Focused Interface"/>
  <categories xmi:id="_rH7gPEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.tasks.ui.commands" name="Task Repositories"/>
  <categories xmi:id="_rH7gPUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_rH7gPkzQEfCUbqAxRfw0FQ" elementId="AnsiConsole.command.categoryid" name="ANSI Support Commands"/>
  <categories xmi:id="_rH7gP0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.wst.server.ui" name="Server" description="Server"/>
  <categories xmi:id="_rH7gQEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.wikitext.context.ui.commands" name="Mylyn WikiText" description="Commands used for Mylyn WikiText"/>
  <categories xmi:id="_rH7gQUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.lsp4e.category" name="Language Servers"/>
  <categories xmi:id="_rH7gQkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_rH7gQ0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.tm.terminal.view.ui.commands.category" name="Terminal Commands"/>
  <categories xmi:id="_rH7gREzQEfCUbqAxRfw0FQ" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_rH7gRUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_rH7gRkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_rH7gR0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.category.refactoring" name="Refactor - Java" description="Java Refactoring Actions"/>
  <categories xmi:id="_rH7gSEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_rH7gSUzQEfCUbqAxRfw0FQ" elementId="org.springframework.ide.eclipse.commands" name="Spring Generic Text Editor" description="Spring Language Server Commands"/>
  <categories xmi:id="_rH7gSkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_rH7gS0zQEfCUbqAxRfw0FQ" elementId="org.springsource.ide.eclipse.common.ui.commands" name="SpringSource Tools"/>
  <categories xmi:id="_rH7gTEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.genericeditor.extension.category.source" name="Target Definition Source" description="Target Definition Source Page actions"/>
  <categories xmi:id="_rH7gTUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_rH7gTkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_rH7gT0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_rH7gUEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_rH7gUUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_rH7gUkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_rH7gU0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_rH7gVEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.jdt.ui.category.source" name="Source" description="Java Source Actions"/>
  <categories xmi:id="_rH7gVUzQEfCUbqAxRfw0FQ" elementId="org.eclipse.mylyn.commons.repositories.ui.category.Team" name="Team"/>
  <categories xmi:id="_rH7gVkzQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.runtime.spy.commands.category" name="Spy"/>
  <categories xmi:id="_rH7gV0zQEfCUbqAxRfw0FQ" elementId="org.eclipse.pde.ui.category.source" name="Manifest Editor Source" description="PDE Source Page actions"/>
  <categories xmi:id="_rH7gWEzQEfCUbqAxRfw0FQ" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
</application:Application>
