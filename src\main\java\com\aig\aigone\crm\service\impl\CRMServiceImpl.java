package com.aig.aigone.crm.service.impl;


import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.stereotype.Service;

import com.aig.aigone.crm.model.dto.DoctorConsultationFeeDetailsDto;
import com.aig.aigone.crm.model.dto.PatientOpBillsListRequestDto;
import com.aig.aigone.crm.service.CRMService;
import com.aig.aigone.exception.AigOneException;
import com.aig.aigone.mapper.AigOneMapper;
import com.aig.aigone.model.dto.emr.EmrVisitDetailsDto;
import com.aig.aigone.model.dto.his.PateintPreviousAppointmentsDto;
import com.aig.aigone.ref.dto.ReferenceValueResponseDTO;
import com.aig.aigone.ref.entity.Reference;
import com.aig.aigone.ref.repo.ReferenceRepository;
import com.aig.aigone.ref.repo.ReferenceValueRepository;
import com.aig.aigone.repository.his.HisDbRepository;
import com.aig.aigone.service.EmrService;
import com.aig.aigone.service.HisService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Scope(proxyMode = ScopedProxyMode.TARGET_CLASS)
public class CRMServiceImpl implements CRMService{
	
	
	@Autowired
	private  ReferenceValueRepository refValueRepo;
	
	@Autowired
	private  ReferenceRepository refRepo;
	
	@Autowired
	private AigOneMapper aigOneMapper;
	
	@Autowired
	private HisDbRepository hisDbRepo;
	
	@Autowired
	private HisService hisService;
	
	@Autowired
	private EmrService emrService;
	
	@Override
	public List<ReferenceValueResponseDTO> getValuesByReferenceCode(String referenceCode) {
		Reference reference = refRepo.findByCode(referenceCode).orElseThrow(() ->new AigOneException("Reference Code not found"));
	return refValueRepo.findByReferenceId(reference.getId()).stream().map(aigOneMapper::toReferenceValueResponseDTO)
			.collect(Collectors.toList());
	}

	@Override
	public List<PateintPreviousAppointmentsDto> fetchAllPreviousAppointmentsByUhId(String uhId) {
		List<PateintPreviousAppointmentsDto> appointmentDetails = hisDbRepo.fetchAllPreviousAppointmentsByUhId(uhId);
		return appointmentDetails;
	}
	
	public Map<String, Object> getOPbillListFromUHID(PatientOpBillsListRequestDto requestDto) {
		return hisService.getOPbillListFromUHID(requestDto);
	}

	@Override
	public EmrVisitDetailsDto fetchPatientAllVisits(String uhId) {
		return emrService.fetchPatientAllVisits(uhId);
	}

	@Override
	public List<DoctorConsultationFeeDetailsDto> getConsultationFeeDetails(Integer doctorId) {
		return hisDbRepo.getConsultationFeeDetails(doctorId);
	}
	
	

}
