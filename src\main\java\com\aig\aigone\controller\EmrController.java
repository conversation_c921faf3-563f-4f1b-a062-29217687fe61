package com.aig.aigone.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.model.dto.IcdMasterDto;
import com.aig.aigone.model.dto.OpConsultationDto;
import com.aig.aigone.model.dto.VitalsTimeRequestDto;
import com.aig.aigone.model.dto.emr.IPProgressOrderNoteDto;
import com.aig.aigone.model.dto.emr.InPatientConsultationNoteDto;
import com.aig.aigone.model.dto.emr.PatientConsultationNoteDto;
import com.aig.aigone.model.dto.emr.PatientEventDateTimeDto;
import com.aig.aigone.model.dto.emr.PatientVisitDetailsDto;
import com.aig.aigone.model.dto.emr.PatientVitalsDto;
import com.aig.aigone.model.dto.emr.PatientVitalsOpIpDto;
import com.aig.aigone.model.dto.emr.PatientVitalsWithClinicalNoteDto;

import com.aig.aigone.model.dto.emr.PractitionerContentDTO;

import com.aig.aigone.model.dto.emr.PreviousConsultationsDto;
import com.aig.aigone.model.dto.emr.EmrDoctorTemplateDTO;
import com.aig.aigone.model.dto.mdt.MdtMedicationDto;
import com.aig.aigone.prism.services.EmrDoctorsTemplateReplicaInPrismService;
import com.aig.aigone.service.EmrService;
import org.springframework.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@RestController
@RequestMapping("/api/emr")
public class EmrController {
	
	private static final Logger log = LoggerFactory.getLogger(EmrController.class);
	
	@Autowired
	private EmrService emrService;
	
	@Autowired
	private EmrDoctorsTemplateReplicaInPrismService emrDoctorsTemplateReplicaInPrismService;

	@GetMapping("/vitals/{uhId}")
	public List<PatientVitalsDto> fetchVitals(@PathVariable(name = "uhId", required = true) String uhId) {
		return emrService.fetchPatientVitals(uhId);
	}
	
	@GetMapping("/consultationNote/{uhId}")
	public List<PatientConsultationNoteDto> fetchPatientConsultationNote(@PathVariable(name = "uhId", required = true) String uhId) {
		return emrService.fetchPatientConsultationNote(uhId);
	}
	
	@GetMapping("/ipConsultationNote/{uhId}")
	public List<InPatientConsultationNoteDto> fetchInPatientConsultationNote(@PathVariable(name = "uhId", required = true) String uhId) {
		return emrService.fetchInPatientConsultationNote(uhId);
	}
	
	@GetMapping("/ipProgressNote/{uhId}/{ipNumber}")
	public List<IPProgressOrderNoteDto> fetchIPProgressOrderNote(@PathVariable(name = "uhId", required = true) String uhId,@PathVariable(name = "ipNumber", required = true) String ipNumber) {
		return emrService.fetchIPProgressOrderNote(uhId,ipNumber);
	}
	
//	@GetMapping("/visits/{uhId}/{doctorId}/{from}")
//	public PatientVisitDetailsDto fetchPatientVisitDetailsByDoctorId(@PathVariable(name = "uhId", required = true) String uhId,@PathVariable(name = "doctorId", required = true) String doctorId, @PathVariable(name = "from", required = false) String from) {
//		if(from=="TODAY") {
//			return emrService.fetchPatientVisitDetailsByDoctorId(uhId,doctorId);
//		}else if(from=="IP") {
//			return emrService.fetchPatientVisitDetailsByDoctorId(uhId,doctorId);
//		}else {
//			return emrService.fetchPatientVisitDetailsByDoctorId(uhId,doctorId);
//		}
//	}
	
	@GetMapping("/visits/{uhId}/{doctorId}")
	public PatientVisitDetailsDto fetchPatientVisitDetailsByDoctorId(@PathVariable(name = "uhId", required = true) String uhId,@PathVariable(name = "doctorId", required = true) String doctorId) {
		return emrService.fetchPatientVisitDetailsByDoctorId(uhId,doctorId);
	}
	
 
	@PostMapping("/op-consultation")
	public void saveConsultationNote(@RequestBody OpConsultationDto request) {
		emrService.saveOpConsultioan(request);
	}
	
	@GetMapping("/op-consultation/{uhId}/{billNo}")
	public OpConsultationDto getOpConsultation(@PathVariable(name = "uhId", required = true) String uhId, @PathVariable(name="billNo",required= true) Integer billNo) {
		return emrService.getOpConsultation(uhId, billNo);
	}
	
	@GetMapping("/op-consultation/prev/{uhId}/{doctorId}")
	public OpConsultationDto getOpConsultation(@PathVariable(name = "uhId", required = true) String uhId, @PathVariable(name="doctorId",required= true) String doctorId) {
		return emrService.getPrevOpConsultation(uhId, doctorId);
	}
	
	@PostMapping("/op-consultation/updateStatus")
	public void updateStatus(@RequestBody OpConsultationDto request) {
		emrService.updateStatus(request);
	}
	
//	@PostMapping("/op-consultation/takeAction")
//	public void takeAction(@RequestBody OpConsultationDto request) {
//		emrService.takeOpAction(request);
//	}
//	
	@GetMapping("/vitals/{uhId}/{ipNumber}")
	public List<PatientVitalsDto> fetchPatientVitalsByIpNumber(@PathVariable(name = "uhId", required = true) String uhId, @PathVariable(name="ipNumber",required= true) String ipNumber) {
		return emrService.fetchPatientVitalsByIpNumber(uhId,ipNumber);
	}
	
	@GetMapping("/vitals/encounter_id/{encounterId}")
	public List<PatientVitalsWithClinicalNoteDto> fetchPatientVitalsByIpNumber(@PathVariable(name = "encounterId", required = true) String encounterId) {
		return emrService.fetchPatientVitalsByEncounterId(encounterId);
	}
	
	@GetMapping("/op-consultation-note")
	public String getOpConsultationNote(@RequestParam(name="encounterId", required = false) String encounterId, @RequestParam(name="uhId",required = false) String uhId) {
		return emrService.getOpConsultationNote(encounterId,uhId);
	}
	
	@GetMapping("/op/medications")
	public List<MdtMedicationDto> fetchOpMedications(@RequestParam(name="uhId",required=false) String uhId) {
		return emrService.fetchOpMedications(uhId);
	}
	
    @GetMapping("/vitalsIpOp/{patientId}")
    public ResponseEntity<List<PatientVitalsOpIpDto>> getPatientVitalsOpIpByUhid(@PathVariable String patientId) {   
        List<PatientVitalsOpIpDto> vitals = emrService.fetchPatientVitalsOpIpByUhidImpl(patientId);
        return ResponseEntity.ok(vitals);
    }
    
	@GetMapping("/getIcdMaster/{serachString}")
	public List<IcdMasterDto> getIcdMaster(@PathVariable(name = "serachString", required = true) String serachString) {
		return emrService.getIcdMaster(serachString);
	}
	
	@PostMapping("/getVitalStatus")
	public List<PatientEventDateTimeDto> getVitalStatusDetails(@RequestBody VitalsTimeRequestDto request) {
		return emrService.getVitalStatusDetails(request);
	}
	

	@GetMapping("/patientPreviousConsultation/{uhId}")
	public List<PreviousConsultationsDto> getPatientPreviousConsultantData(@PathVariable(name = "uhId", required = true) String uhId){
		return emrService.getPatientPreviousConsultations(uhId);
	}

	
	  @GetMapping("practitionercontent/{practId}")
	    public ResponseEntity<PractitionerContentDTO> getPractitionerContent(@PathVariable(name = "practId", required = true) String practId) {
	        PractitionerContentDTO dto = emrService.getPractitionerContent(practId);
	        return ResponseEntity.ok(dto);
	    }
	  
	    @GetMapping("/doctor_templates/{empid}")
	    public ResponseEntity<List<EmrDoctorTemplateDTO>> getTemplatesByEmpid(@PathVariable(name = "empid", required = true) String empid) {
//	        List<EmrDoctorTemplateDTO> templates = emrService.getDoctorTemplatesById(empid);
	    	List<EmrDoctorTemplateDTO> templates = emrDoctorsTemplateReplicaInPrismService.getTemplatesByEmpid(empid);
	        return ResponseEntity.ok(templates);
	    }
}
