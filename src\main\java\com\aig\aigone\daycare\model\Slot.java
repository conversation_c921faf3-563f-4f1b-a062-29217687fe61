package com.aig.aigone.daycare.model;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonIgnore;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name="dc_slot")
public class Slot {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private LocalDateTime startingTime;
    private LocalDateTime endTime;
    

    private LocalDateTime arrivalTime;
    
    @Column(name = "is_available", nullable=false)
    private boolean isAvailable = false;
    
    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "bed_id")
    @NotNull
    private Bed bed;

    @JsonIgnore
    @NotNull
    @ManyToOne
    private Booking bookingId;
    
    private String comments;
    private LocalDateTime createdAt;
	private LocalDateTime modifiedAt;
	private String createdBy;
	private String modifiedBy;
	private String status;
	@Column(name="dialysis_status")
	private String dialysisStatus;
	@Column(name="is_notification_sent")
	private Boolean isNotificationSent = false;
	@Column(name="is_system_remainder_notification_sent")
	private Boolean isSystemRemainderNotificationSent=false;
}

