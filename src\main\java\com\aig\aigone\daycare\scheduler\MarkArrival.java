package com.aig.aigone.daycare.scheduler;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.aig.aigone.daycare.dto.PatientDTO;
import com.aig.aigone.daycare.emr.model.EventDetails;
import com.aig.aigone.daycare.emr.model.EventHeader;
import com.aig.aigone.daycare.emr.repository.EventDetailsRepository;
import com.aig.aigone.daycare.emr.repository.EventHeaderRepository;
import com.aig.aigone.daycare.enums.DialysisStatus;
import com.aig.aigone.daycare.model.DialysisArrivalStatus;
import com.aig.aigone.daycare.model.Slot;
import com.aig.aigone.daycare.repository.DialysisArrivalStatusRepository;
import com.aig.aigone.daycare.repository.DialysisBookingRepository;
import com.aig.aigone.daycare.repository.SlotRepository;
import com.aig.aigone.daycare.service.HisServiceDaycare;
import com.aig.aigone.daycare.service.WhatsAppService;
import com.aig.aigone.daycare.util.DaycareConstants;

import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class MarkArrival {

 @Autowired
 SlotRepository slotRepo;

 @Autowired
 DialysisArrivalStatusRepository dialysisRepo;

 @Autowired
 EventHeaderRepository headerRepo;

 @Autowired
 EventDetailsRepository detailsRepo;
 
 @Autowired
 HisServiceDaycare hisService;
 
 @Autowired
 private WhatsAppService whatsAppService;

// @Scheduled(fixedRate = 20000)
//  @Scheduled(cron = "0 0 11,15,21,1 * * 1-6")
 @Scheduled(cron = "0 0 */2 * * *")
 @Transactional
 public void markDialysisArrival() {
  System.out.println("--------Job Started---------");
  try {
   List<Slot> todaysSlots = slotRepo.findByDate(LocalDate.now());

   for (int i = 0; i < todaysSlots.size(); i++) {

    DialysisArrivalStatus dialysisStatus = dialysisRepo.findBySlot(todaysSlots.get(i));

    Map<String, String> dialysisDetails = headerRepo
      .getDailysisStatus(todaysSlots.get(i).getBookingId().getUhid());

    if (!dialysisDetails.isEmpty()) {
     if (dialysisStatus == null) {

      DialysisArrivalStatus dialysisStatusNew = new DialysisArrivalStatus();
      dialysisStatusNew.setSlot(todaysSlots.get(i));
      dialysisStatusNew.setUhid(dialysisDetails.get("patient_id"));
      dialysisStatusNew
        .setArrivalTime(convertStringToLocalDateTime(dialysisDetails.get("Start_date")));
      dialysisStatusNew.setStartTime(convertStringToLocalDateTime(dialysisDetails.get("Start_date")));
      dialysisStatusNew.setEndTime(convertStringToLocalDateTime(dialysisDetails.get("End_date")));
      dialysisStatusNew.setStatus(
        "IN_PROGRESS".equals(dialysisDetails.get("Status"))
          ? DialysisStatus.IN_PROGRESS
          : DialysisStatus.COMPLETED);
      dialysisRepo.save(dialysisStatusNew);
     } else {

      dialysisStatus.setEndTime(convertStringToLocalDateTime(dialysisDetails.get("End_date")));
      dialysisStatus.setStatus(
        "IN_PROGRESS".equals(dialysisDetails.get("Status"))
          ? DialysisStatus.IN_PROGRESS
          : DialysisStatus.COMPLETED);
      dialysisRepo.save(dialysisStatus);
     }
    }
   }
  } catch (Exception e) {
   e.printStackTrace();
  }
  System.out.println("--------Job End---------");
 }

 private static final List<DateTimeFormatter> FORMATTERS = List.of(
         DateTimeFormatter.ISO_OFFSET_DATE_TIME, // 2025-02-04T10:16:00+05:30
         DateTimeFormatter.ISO_ZONED_DATE_TIME,  // 2025-02-04T10:16:00+05:30[Asia/Kolkata]
         DateTimeFormatter.ofPattern("MMM d, yyyy h:mm a"), // Jan 20, 2025 7:45 AM
         DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"), // 2025-02-04 11:31:00
         DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss") // 2025-02-04T11:31:00
     );

     public static LocalDateTime convertStringToLocalDateTime(String time) {
         if (time == null || time.isEmpty()) {
             return null;
         }
         for (DateTimeFormatter formatter : FORMATTERS) {
             try {
                 if (time.contains("+") || time.contains("Z")) {
                     return OffsetDateTime.parse(time, formatter).toLocalDateTime();
                 } else if (time.contains("[")) {
                     return ZonedDateTime.parse(time, formatter).toLocalDateTime();
                 } else {
                     return LocalDateTime.parse(time, formatter);
                 }
             } catch (Exception ignored) {
             }
         }
         throw new IllegalArgumentException("Invalid date format: " + time);
     }
     
     @Scheduled(cron = "0 0 */2 * * *")
     @Transactional
     public void sendDialysisCompletionNotification() {
      
      try {
       List<DialysisArrivalStatus> list = dialysisRepo.getDialysisCompletedRecords();
       if(!list.isEmpty()) {
        for(DialysisArrivalStatus dialysisArrivalStatus : list) {
         String uhid = dialysisArrivalStatus.getUhid();
         String iacode = uhid.split("\\.")[0];
         Integer regNo = Integer.parseInt(uhid.split("\\.")[1]);

         PatientDTO patient = hisService.getPatientByUHID(iacode, regNo);
         String patientPhoneNumber = patient.getPhoneNo();
         String patientName = patient.getPatientName();

         log.info("Calling Whatsapp API - Send remainder notification to patient....");

          try {

                 // Test- whatsapp notification
                 // Cheryshma - **********
                 whatsAppService.sendDialysisNotification(DaycareConstants.DIALYSIS_CONFIRMATION, patientPhoneNumber,
                		 patientName);

                 log.info("**** Whatsapp notification sent successfully to patient. ****");
             } catch (Exception e) {
                 log.error("**** Failed to send Whatsapp notification: {}", e.getMessage());
             }
          
          dialysisArrivalStatus.setIsNotificationSent(true);
          dialysisRepo.save(dialysisArrivalStatus);
        } 
       }
   } catch (Exception e) {
    log.error(e.getMessage());
   }
     }
     
     @Scheduled(cron = "0 0 */2 * * *")
     @Transactional
     public void sendNonArrivalNotification() {
         log.info("Send Non-Arrival Notification Job Started");
         try {
             List<Slot> todaySlots = slotRepo.getTodayActiveSlots();
             LocalDateTime now = LocalDateTime.now();

             todaySlots.forEach(slot -> {
                 LocalDateTime slotStart = slot.getStartingTime();
                 LocalDateTime slotEnd = slot.getEndTime();
                 LocalDateTime arrivalTime = slot.getArrivalTime();

                 if (arrivalTime == null && now.isAfter(slotStart.plusMinutes(30))) {
                     slot.setDialysisStatus("DELAYED");
                 }

                 if (arrivalTime == null && now.isAfter(slotEnd) && slot.getIsNotificationSent()==false) {
                     slot.setDialysisStatus("NOT ARRIVED");
                     slot.setIsNotificationSent(true);
                     
                     String uhid = slot.getBookingId().getUhid();
         String iacode = uhid.split("\\.")[0];
         Integer regNo = Integer.parseInt(uhid.split("\\.")[1]);

         PatientDTO patient = hisService.getPatientByUHID(iacode, regNo);
         String patientPhoneNumber = patient.getPhoneNo();
         String patientName = patient.getPatientName();
         
         String slotDate = slotStart
           .format(DateTimeFormatter.ofPattern("dd-MM-yyyy"));

         String slotTime = slotStart
           .format(DateTimeFormatter.ofPattern("hh:mm a"));

         log.info("Calling Whatsapp API - Send not arrived notification to patient....");

          try {

                 // Test- whatsapp notification
                 // Cheryshma - **********
                 whatsAppService.sendDialysisNotification(DaycareConstants.DIALYSIS_PATIENT_NON_ARRIVAL, patientPhoneNumber,
                		 patientName,slotDate,slotTime);

                 log.info("**** Whatsapp notification sent successfully to patient. ****");
             } catch (Exception e) {
                 log.error("**** Failed to send Whatsapp notification: {}", e.getMessage());
             }
                 }
             });

             slotRepo.saveAll(todaySlots);

         } catch (Exception e) {
             log.error("Error in Send Non-Arrival Notification Job: {}", e.getMessage(), e);
         }
     }
     
     
     @Scheduled(cron = "0 0 * * * *")
     public void sendUpComingSlotRemainderNotification() {
    	 log.info("Send UpComing Slot Remainder Notification Job started");
         try {
             List<Slot> upcomingSlots = slotRepo.getTodayActiveSlots();
             if (!upcomingSlots.isEmpty()) {
                 for (Slot slot : upcomingSlots) {
                     LocalDateTime slotStart = slot.getStartingTime();

                     if (Boolean.TRUE.equals(slot.getIsSystemRemainderNotificationSent())) {
                         continue;
                     }

                     long minutesDiff = Duration.between(LocalDateTime.now(), slotStart).toMinutes();
                     if (minutesDiff >= 90 && minutesDiff <= 121) {
                         try {
                             String uhid = slot.getBookingId().getUhid();
                             String iacode = uhid.split("\\.")[0];
                             Integer regNo = Integer.parseInt(uhid.split("\\.")[1]);

                             PatientDTO patient = hisService.getPatientByUHID(iacode, regNo);
                             String patientPhoneNumber = patient.getPhoneNo();
                             String patientName = patient.getPatientName();

                             String bookingDate = slotStart.toLocalDate()
                                     .format(DateTimeFormatter.ofPattern("dd-MM-yyyy"));
                             String bookingTime = slotStart
                                     .format(DateTimeFormatter.ofPattern("hh:mm a"));

                             log.info("Calling WhatsApp API to send reminder notification...");

                             whatsAppService.sendDialysisNotification(
                                 DaycareConstants.DIALYSIS_APPOINTMENT_REMINDER,
                                 patientPhoneNumber,
                                 patientName,
                                 bookingDate,
                                 bookingTime
                             );

                             slot.setIsSystemRemainderNotificationSent(true);
                             slotRepo.save(slot);

                             log.info("**** WhatsApp notification sent and flag updated ****");

                         } catch (Exception e) {
                             log.error("**** Failed to send WhatsApp notification: {}", e.getMessage(), e);
                         }
                     }
                 }
             }
         } catch (Exception e) {
             log.error("Error in Send Remainder Notification Job: {}", e.getMessage(), e);
         }
         log.info("Send UpComing Slot Remainder Notification Job Ended");
     }

}