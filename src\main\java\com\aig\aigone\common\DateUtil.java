package com.aig.aigone.common;


import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Locale;

public final class DateUtil {

	public static final String FORMAT_YYYY_MM_DD = "yyyy-MM-dd";

	public static final String FORMAT_DD_MM_YYYY = "dd/MM/yyyy";
	
	public static final String FORMAT_DD_MM_YY = "dd/MM/yy";
	
	
	public static final String FORMAT_DD_MM_YYYY_EIPHEN = "dd-MM-yyyy";

	public static final String FORMAT_HH_MM = "HH:mm";

	public static final String FORMAT_YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
	
	public static final String FORMAT_YYYY_MM_DD_T_HH_MM_SS = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
	
	public static final String FORMAT_YYYY_MM_DD_T_HH_MM_SS_NO_Z = "yyyy-MM-dd'T'HH:mm:ss.SSS";

	public static final String FORMAT_DD_MM_YYYY_HH_MM_SS = "dd/MM/yyyy HH:mm:ss";
	
	public static final String FORMAT_DD_MM_YYYY_HH_MM = "dd/MM/yyyy HH:mm";
	
	public static final String FORMAT_YYYY_MM_DD_T_HH_MM_SS_NOT_Z = "yyyy-MM-dd'T'HH:mm:ss";
	
	public static final String FORMAT_YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
	
	public static final String FORMAT_DD_MM_YYYY_HH_MM_NON_ISO = "dd-MM-yyyy HH:mm";
	
	public static String convertBitwiseDaysToString(int days) {
		StringBuilder sb = new StringBuilder();
		if((days & 1) > 0 ) sb.append("S");
		if((days & 2) > 0 ) sb.append("M");
		if((days & 4) > 0 ) sb.append("T");
		if((days & 8) > 0 ) sb.append("W");
		if((days & 16) > 0 ) sb.append("T");
		if((days & 32) > 0 ) sb.append("F");
		if((days & 64) > 0 ) sb.append("S");
		return sb.toString();
	}

	public static String convertBitwiseDaysToFullDayName(int days) {
		StringBuilder sb = new StringBuilder();
		if((days & 1) > 0 ) sb.append("Sun");
		if((days & 2) > 0 ) sb.append("Mon");
		if((days & 4) > 0 ) sb.append("Tue");
		if((days & 8) > 0 ) sb.append("Wed");
		if((days & 16) > 0 ) sb.append("Thu");
		if((days & 32) > 0 ) sb.append("Fri");
		if((days & 64) > 0 ) sb.append("Sat");
		return sb.toString();
	}

	public static List<Integer> convertBitwiseDaysToIntegers(int days) {
		List<Integer> dayList = new ArrayList<>();
		if((days & 1) > 0 ) dayList.add(0); 
		if((days & 2) > 0 ) dayList.add(1);
		if((days & 4) > 0 ) dayList.add(2);
		if((days & 8) > 0 ) dayList.add(3);
		if((days & 16) > 0 ) dayList.add(4);
		if((days & 32) > 0 ) dayList.add(5);
		if((days & 64) > 0 ) dayList.add(6);
		return dayList;
	}


	public static int convertDaysToBitWiseDays(List<Integer> selectedDays) {
		int days = 0;
		for(Integer i : selectedDays) {
			switch (i) {
			case 0:
				days += 1;
				break;
			case 1:
				days += 2;
				break;
			case 2:
				days += 4;
				break;
			case 3:
				days += 8;
				break;
			case 4:
				days += 16;
				break;
			case 5:
				days += 32;
				break;
			case 6:
				days += 64;
				break;
			default:
				break;
			}
		}
		return days;
	}

	//	public static boolean isValid(Date date,String format) {
	//
	//		return true;
	//	}

	public static boolean isBeforeDay(Date date1, Date date2) {
		if (date1 == null || date2 == null) {
			throw new IllegalArgumentException("The dates must not be null");
		}
		Calendar cal1 = Calendar.getInstance();
		cal1.setTime(date1);
		Calendar cal2 = Calendar.getInstance();
		cal2.setTime(date2);
		return isBeforeDay(cal1, cal2);
	}

	public static boolean isBeforeDay(Calendar cal1, Calendar cal2) {
		if (cal1 == null || cal2 == null) {
			throw new IllegalArgumentException("The dates must not be null");
		}
		if (cal1.get(Calendar.ERA) < cal2.get(Calendar.ERA))
			return true;
		if (cal1.get(Calendar.ERA) > cal2.get(Calendar.ERA))
			return false;
		if (cal1.get(Calendar.YEAR) < cal2.get(Calendar.YEAR))
			return true;
		if (cal1.get(Calendar.YEAR) > cal2.get(Calendar.YEAR))
			return false;
		return cal1.get(Calendar.DAY_OF_YEAR) < cal2.get(Calendar.DAY_OF_YEAR);
	}

	public static boolean isAfterDay(Date date1, Date date2) {
		if (date1 == null || date2 == null) {
			throw new IllegalArgumentException("The dates must not be null");
		}
		Calendar cal1 = Calendar.getInstance();
		cal1.setTime(date1);
		Calendar cal2 = Calendar.getInstance();
		cal2.setTime(date2);
		return isAfterDay(cal1, cal2);
	}

	public static boolean isAfterDay(Calendar cal1, Calendar cal2) {
		if (cal1 == null || cal2 == null) {
			throw new IllegalArgumentException("The dates must not be null");
		}
		if (cal1.get(Calendar.ERA) < cal2.get(Calendar.ERA))
			return false;
		if (cal1.get(Calendar.ERA) > cal2.get(Calendar.ERA))
			return true;
		if (cal1.get(Calendar.YEAR) < cal2.get(Calendar.YEAR))
			return false;
		if (cal1.get(Calendar.YEAR) > cal2.get(Calendar.YEAR))
			return true;
		return cal1.get(Calendar.DAY_OF_YEAR) > cal2.get(Calendar.DAY_OF_YEAR);
	}

	public static boolean isSameDay(Date date1, Date date2) {
		if (date1 == null || date2 == null) {
			throw new IllegalArgumentException("The dates must not be null");
		}
		Calendar cal1 = Calendar.getInstance();
		cal1.setTime(date1);
		Calendar cal2 = Calendar.getInstance();
		cal2.setTime(date2);
		return isSameDay(cal1, cal2);
	}

	public static boolean isSameDay(Calendar cal1, Calendar cal2) {
		if (cal1 == null || cal2 == null) {
			throw new IllegalArgumentException("The dates must not be null");
		}
		return (cal1.get(Calendar.ERA) == cal2.get(Calendar.ERA) &&
				cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) && cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR));
	}

	public static boolean isToday(Date date) {
		return isSameDay(date, Calendar.getInstance().getTime());
	}

	public static Date getPreviousDay(Date date){
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		c.add(Calendar.DATE, - 1);
		return c.getTime();
	}
	
	public static Date getPreviousDayByGivenNo(Date date,int noOfDays){
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		c.add(Calendar.DATE, - noOfDays);
		return c.getTime();
	}

	public static Date getNextDay(Date date){
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		c.add(Calendar.DATE, 1);
		return c.getTime();
	}

	public static List<Date> getLastSevenDays(Date date){
		List<Date> sevenDates = new ArrayList<>();
		sevenDates.add(date);
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		for(int i=1;i<=6;i++) {
			c.add(Calendar.DATE, - 1);
			sevenDates.add(c.getTime());	
		}
		return sevenDates;
	}
	
	
	

	public static String format(Date date, String format) {
		//		String dateString = null;
		SimpleDateFormat formatter = new SimpleDateFormat(format);
		//		formatter.format(date);
		return formatter.format(date);
	}

	public static Date convertStringToDate(String date, String format) {
		Date d = null;
		SimpleDateFormat formatter = new SimpleDateFormat(format);
		try {
			d = formatter.parse(date);
		} catch (ParseException e) {
			e.printStackTrace();
		}

		return d;
	}

	private Date getTime(String value) {
		Date time = null;
		if(value != null && value.length() == 4 ) {
			String hh = value.substring(2);
			String mm = value.substring(2, 4);
			int hours = Integer.parseInt(hh);
			int minutes = Integer.parseInt(mm);

			StringBuilder sb = new StringBuilder();
			if(hours >= 0 && hours < 23 && minutes >= 0 || minutes <= 59) {
				sb.append(hh).append(":").append(mm);
				time = DateUtil.convertStringToDate(sb.toString(), DateUtil.FORMAT_HH_MM);
			} 
		} 
		return time;
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	public static List<Date> getDatesBetweenDates(Date startDate, Date endDate) {
		List datesInRange = new ArrayList<>();
		Calendar calendar = getCalendarWithoutTime(startDate);
		Calendar endCalendar = getCalendarWithoutTime(getNextDay(endDate));

		while (calendar.before(endCalendar)) {
			Date result = calendar.getTime();
			datesInRange.add(result);
			calendar.add(Calendar.DATE, 1);
		}

		return datesInRange;
	}

	private static Calendar getCalendarWithoutTime(Date date) {
		Calendar calendar = new GregorianCalendar();
		calendar.setTime(date);
		calendar.set(Calendar.HOUR, 0);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar;
	}


	public static Date addMinutesToDate(Date date, int minutes) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.MINUTE, minutes);
		return calendar.getTime();
	}
	
	public static Date reduceMinutesToDate(Date date, int minutes) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.MINUTE, -minutes);
		return calendar.getTime();
	}
	
	public static Date convertLocalTimeToDate(LocalTime localTime) {
		 LocalDate localDate = LocalDate.now();
	     LocalDateTime localDateTime = LocalDateTime.of(localDate, localTime);
	     return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
	}
	
	public static Date convertTimeStringToDate(String time) {
		LocalTime localTime = LocalTime.parse(time);
		 LocalDate localDate = LocalDate.now();
	     LocalDateTime localDateTime = LocalDateTime.of(localDate, localTime);
	     return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
	}
	
	public static Date getTodayDate() {
		Date today = new Date();
		SimpleDateFormat formatter = new SimpleDateFormat(FORMAT_YYYY_MM_DD);
		try {
			today = formatter.parse(formatter.format(today));
			return today;
		} catch (ParseException e) {
			e.printStackTrace();
			return null;
		}
	}
	
	public static String getTodayDateAsString() {
        Date today = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat(FORMAT_YYYY_MM_DD);
        return formatter.format(today); 
    }
	
	public static String convertDateToString(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat(FORMAT_DD_MM_YYYY_EIPHEN);
        return formatter.format(date); 
    }
	
	public static String convertDateAndTimeToString(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat(FORMAT_DD_MM_YYYY_HH_MM);
        return formatter.format(date); 
    }
	
	public static String convertTimeStampToString(Timestamp date) {
		ZonedDateTime zonedDateTime = date.toInstant().atZone(ZoneId.of("Asia/Kolkata"));

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(FORMAT_DD_MM_YYYY_HH_MM);
        String formattedDate = zonedDateTime.format(formatter);
		return formattedDate;
		
	}
	
	public static String convertDateAndTimeNonIsoToString(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat(FORMAT_DD_MM_YYYY_HH_MM_NON_ISO);
        return formatter.format(date); 
    }
	
	
	public static String convertTimeStampToStringCustomFormat(Timestamp date,String format) {
		ZonedDateTime zonedDateTime = date.toInstant().atZone(ZoneId.of("Asia/Kolkata"));

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format, Locale.ENGLISH);

        String formattedDate = zonedDateTime.format(formatter);
		return formattedDate;
		
	}

}
