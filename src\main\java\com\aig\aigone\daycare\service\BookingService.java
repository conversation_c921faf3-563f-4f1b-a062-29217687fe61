package com.aig.aigone.daycare.service;

import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;

import com.aig.aigone.daycare.dto.AvailabilityDTO;
import com.aig.aigone.daycare.dto.BookingDTO;
import com.aig.aigone.daycare.dto.ExistingBookingDetailsDTO;
import com.aig.aigone.daycare.dto.RescheduleBookingDTO;
import com.aig.aigone.daycare.dto.WaitingListDTO;

public interface BookingService {
	
	public List<AvailabilityDTO> getAvailibility(List<AvailabilityDTO> dateAndSlot,Integer stationId, Integer bedTypeId, Integer cat);
	public boolean bookBed(BookingDTO bookingDetails);
	public boolean existsByUhid(String uhid);
	List<Integer> getBookingIdsByUhid(String uhid);
	
	public boolean rescheduleBooking(RescheduleBookingDTO rescheduleBookingDTO);
	
	public boolean waitingListAppointment(@RequestBody WaitingListDTO bookingDetails);
}
