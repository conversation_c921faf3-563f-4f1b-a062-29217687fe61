package com.aig.aigone.queue.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.model.dto.UserSessionVO;
import com.aig.aigone.queue.dto.AddUserToQueueResponseDto;
import com.aig.aigone.queue.dto.AllotedQueuesResponseDto;
import com.aig.aigone.queue.dto.CommonResponseDto;
import com.aig.aigone.queue.dto.CounterDetailsResponseDto;
import com.aig.aigone.queue.dto.GenericResponse;
import com.aig.aigone.queue.dto.GenericResponse1;
import com.aig.aigone.queue.dto.PatientQueueDto;
import com.aig.aigone.queue.dto.QRCodeDetailsRequestDto;
import com.aig.aigone.queue.dto.QueueWeightAgeDto;
import com.aig.aigone.queue.dto.TokenDetailsResponseDto;
import com.aig.aigone.queue.dto.TokenResponseDto;
import com.aig.aigone.queue.dto.UserQueueAssesmentRequestDto;
import com.aig.aigone.queue.dto.UserQueueDto;
import com.aig.aigone.queue.dto.UserServicesDto;
import com.aig.aigone.queue.dto.GenerateBarCodeRequestDto;
import com.aig.aigone.queue.dto.GenerateBarCodeResponseDto;
import com.aig.aigone.queue.service.QueueService;
import com.aig.aigone.security.SecurityUtil;

import io.swagger.v3.oas.annotations.Operation;

@RestController
@RequestMapping("/api/queue")
public class QueueController {

	@Autowired
	private QueueService queueService;

	@Operation(summary = "fetch staff user allocated queues")
	@GetMapping("/allotedQueues")
	public GenericResponse<AllotedQueuesResponseDto> fetchStaffUserAllocatedQueues() {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return queueService.fetchStaffUserAllocatedQueues(currentUser.getEmployeeId());
	}

	@Operation(summary = "fetch staff user allocated queues")
	@GetMapping("/getQueueDetails/{queueCode}")
	public GenericResponse1<AllotedQueuesResponseDto> getQueueDetails(@PathVariable(name="queueCode",required = true) String queueCode) {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return queueService.getQueueDetails(currentUser.getEmployeeId(),queueCode);
	}
	
	@Operation(summary = "fetch queue weightage")
	@GetMapping("/weightAge")
	public GenericResponse<QueueWeightAgeDto> fetchQueueWeightage() {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return queueService.fetchQueueWeightage(currentUser.getEmployeeId());
	}
	
	@Operation(summary = "fetch token details")
	@GetMapping("/fetchTokenDetails/{uhId}")
	public GenericResponse1<TokenDetailsResponseDto> fetchTokenDetails(@PathVariable(name="uhId",required = true) String uhId) {
		return queueService.fetchTokenDetails(uhId);
	}

	@Operation(summary = "fetch user services")
	@GetMapping("/userServices/{userQueueId}")
	public GenericResponse<UserServicesDto> fetchUserServices(@PathVariable(name = "userQueueId", required = true) Long userQueueId)  {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();

		return queueService.fetchUserServices(currentUser.getEmployeeId(),userQueueId);
	}

	@Operation(summary = "add user to queue")
	@PostMapping("/addUserToQueue/{queueId}")
	public GenericResponse<AddUserToQueueResponseDto> addUserQueue(@PathVariable(name="queueId",required = true) Long queueId,@RequestBody QRCodeDetailsRequestDto qrDetails  ) {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();

		return queueService.addUserToQueue(currentUser.getEmployeeId(),queueId,qrDetails);
	}

	@Operation(summary = "get user queue details")
	@GetMapping("/getUserQueue/{queueId}")
	public GenericResponse<AddUserToQueueResponseDto> getUserQueue(@PathVariable(name="queueId",required = true) Long queueId,@RequestParam(name="preCheckStatus",required = false) String preCheckStatus,@RequestParam(name="queue_ids",required = false) List<Long> queueIds,@RequestParam(name="assignmentOption",required = false) String assignmentOption) {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return queueService.getUserQueue(currentUser.getEmployeeId(),preCheckStatus,queueId, queueIds, assignmentOption, null);
	}

	@Operation(summary = "get user queue details")
	@GetMapping("/getUserQueueByQueueCode/{queueCode}")
	public GenericResponse<AddUserToQueueResponseDto> getUserQueue(@PathVariable(name="queueCode",required = true) String queueCode,@RequestParam(name="preCheckStatus",required = false) String preCheckStatus,@RequestParam(name="queue_ids",required = false) List<Long> queueIds,@RequestParam(name="assignmentOption",required = false) String assignmentOption) {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return queueService.getUserQueue(currentUser.getEmployeeId(),preCheckStatus,null, queueIds, assignmentOption, queueCode);
	}
	
	@Operation(summary = "Update user weightage")
	@PostMapping("/updateWeightage/{userQueueId}")
	public GenericResponse<AddUserToQueueResponseDto> updateQueue(@PathVariable(name="userQueueId",required = true) Long userQueueId,@RequestParam(name="queueWeightageAction",required = false) String queueWeightageAction,@RequestParam(name="queueWeightageActionId",required = false) Long queueWeightageActionId,@RequestParam(name="remarks",required = false) String remarks) {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return queueService.updateUserQueueWeightage(currentUser.getEmployeeId(),queueWeightageAction,queueWeightageActionId,userQueueId,remarks);
	}

	@Operation(summary = "Call next for semi auto")
	@PostMapping("/callNext/{queue_id}")
	public GenericResponse<CommonResponseDto> callNextSemiAuto(@PathVariable(name="queue_id",required = true) Long queueId,@RequestBody QRCodeDetailsRequestDto qrDetails) {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return queueService.callNextSemiAuto(currentUser.getEmployeeId(),queueId,qrDetails);
	}
	
	@Operation(summary = "Update User Queue Assesment Time")
	@PostMapping("/updateUserQueueAssesment")
	public GenericResponse<CommonResponseDto> updateUserQueueAssesment(@RequestBody UserQueueAssesmentRequestDto details) {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return queueService.updateUserQueueAssesment(currentUser.getEmployeeId(),details);
	}
	
	@Operation(summary = "get common remarks")
	@GetMapping("/getCommonRemarks/{type}")
	public GenericResponse<String> getCommonRemarks(@PathVariable(name="type",required = true) String type) {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return queueService.getCommonRemarks(currentUser.getEmployeeId(),type);
	}
	
	@Operation(summary = "call next pre check")
	@GetMapping("/callNextPreCheck/{queueId}")
	public GenericResponse1<AddUserToQueueResponseDto> CallNextPreCheck(@PathVariable(name="queueId",required = true) Long queueId) {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return queueService.callNextPreCheck(currentUser.getEmployeeId(),queueId);
	}
	
	@Operation(summary = "get user queue counters")
	@GetMapping("/getUserQueueCounters/{queueId}")
	public GenericResponse<CounterDetailsResponseDto> getUserQueueCounters(@PathVariable(name="queueId",required = true) Long queueId) {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return queueService.getUserQueueCounters(currentUser.getEmployeeId(), queueId);
	}
	
	@Operation(summary = "get user queue counters")
	@GetMapping("/getCounterDetails/{queueCounterId}")
	public GenericResponse1<CounterDetailsResponseDto> getCounterDetails(@PathVariable(name="queueCounterId",required = true) Long queueCounterId) {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return queueService.getCounterDetails(currentUser.getEmployeeId(), queueCounterId);
	}
	
	@Operation(summary = "Update Counter")
	@PostMapping("/updateCounter/{queueCounterId}/{status}")
	public GenericResponse1<String> updateCounter(@PathVariable(name="queueCounterId",required = true) Long queueCounterId,@PathVariable(name="status",required = true) String status) {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return queueService.updateCounter(currentUser.getEmployeeId(),queueCounterId, status);
	}
	
	@Operation(summary = "get User queue list all")
	@GetMapping("/getUserQueueAll/{queueCode}")
	public GenericResponse<UserQueueDto> getUserQueueAll(@PathVariable(name="queueCode",required = true) String queueCode) {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return queueService.getUserQueueAll(currentUser.getEmployeeId(),queueCode);
	}
	
	@Operation(summary = "Update Pre Check")
	@PostMapping("/updatePreCheckStatus/{userQueueId}")
	public GenericResponse1<String> updatePreCheckStatus(@PathVariable(name="userQueueId",required = true) Long userQueueId,@RequestParam(name="userServiceIds",required = false) List<Long> userServiceIds) {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return queueService.updatePreCheckStatus(currentUser.getEmployeeId(),userQueueId, userServiceIds);
	}
	
	@Operation(summary = "get User queue list all")
	@GetMapping("/getTokenDetails/{tokenId}")
	public GenericResponse1<TokenResponseDto> getUserQueueAll(@PathVariable(name="tokenId",required = true) Long tokenId) {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return queueService.getTokenDetails(currentUser.getEmployeeId(),tokenId);
	}
	
	@Operation(summary = "get staff user allocated ")
	@GetMapping("/getStaffUserAllocatedResource")
	public GenericResponse<String> getStaffUserAllocatedRescouce(@RequestParam(name="resources",required = false) List<String> resources) {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return queueService.getStaffUserAllocatedRescouce(currentUser.getEmployeeId(),resources);
	}
	
	@Operation(summary = "get patient current queue ")
	@GetMapping("/getPatientQueue/{uhId}")
	public GenericResponse1<PatientQueueDto> getPatientQueue(@PathVariable(name="uhId",required = true) String uhId) {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return queueService.getPatientQueue(currentUser.getEmployeeId(),uhId);
	}

	@Operation(summary = "Generate Bar Code")
	@PostMapping("/generateBarCode")
	public GenericResponse<List<GenerateBarCodeResponseDto>> generateBarCode(@RequestBody GenerateBarCodeRequestDto request) {
		UserSessionVO currentUser = SecurityUtil.getCurrentUser();
		return queueService.generateBarCode(currentUser.getEmployeeId(), request.getUserServiceIds());
	}
}
