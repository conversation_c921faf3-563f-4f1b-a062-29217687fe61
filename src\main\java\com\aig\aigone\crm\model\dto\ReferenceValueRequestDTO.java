package com.aig.aigone.crm.model.dto;

import java.time.LocalDateTime;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ReferenceValueRequestDTO {
    @NotNull
    private Long referenceId;

    @NotBlank
    @Size(max = 30)
    private String code;

    @Size(max = 500)
    private String description;

    private Integer displayOrder;
    private LocalDateTime activeFrom;
    private LocalDateTime activeTo;
    

    
    private String remarks;
    
    @NotNull
    private Boolean status;
}