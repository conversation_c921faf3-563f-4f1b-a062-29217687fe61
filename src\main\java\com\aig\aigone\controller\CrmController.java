package com.aig.aigone.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.exception.apierror.ApiResponse;
import com.aig.aigone.model.dto.CrmCreateLeadReqDto;
import com.aig.aigone.model.entity.aigone.CrmCreateLead;
import com.aig.aigone.model.entity.aigone.CrmHealthChecks;
import com.aig.aigone.service.impl.CrmServiceImpl;

import lombok.extern.slf4j.Slf4j;

@RequestMapping("/api/crm/")
@RestController
@Slf4j
public class CrmController {

	
	@Autowired
	CrmServiceImpl crmServiceImpl;
	
	@PostMapping("/createLead")
	public ResponseEntity<ApiResponse<String>> createLead(@RequestBody CrmCreateLeadReqDto crmCreateLeadReqDto) {
	    log.info("Received createLead request: {}", crmCreateLeadReqDto);
	    
	    crmServiceImpl.crmCreateLeadImpl(crmCreateLeadReqDto);

	    ApiResponse<String> response = new ApiResponse<>(
	            true,
	            HttpStatus.CREATED.value(),
	            "Lead created successfully",
	            null  
	    );

	    return new ResponseEntity<>(response, HttpStatus.CREATED);
	}
	
	
	@GetMapping("/getAllLeads")
	public ResponseEntity<List<CrmCreateLead>> fetchAllCrmCreateLead(){
		   List<CrmCreateLead> fetchAllCrmCreateLeadImpl = crmServiceImpl.fetchAllCrmCreateLeadImpl();
		return new ResponseEntity<List<CrmCreateLead>>(fetchAllCrmCreateLeadImpl,HttpStatus.OK);
	}
	
	
	@PutMapping("/updateCrm")
	public ResponseEntity<ApiResponse<Void>> updateCrm(@RequestBody CrmCreateLead crmCreateLead) {
	    boolean updated = crmServiceImpl.updateCrmImpl(crmCreateLead);

	    if (updated) {
	        return ResponseEntity.ok(
	            new ApiResponse<>(
	                true,
	                HttpStatus.OK.value(),
	                "CRM record updated successfully",
	                null
	            )
	        );
	    } else {
	        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(
	            new ApiResponse<>(
	                false,
	                HttpStatus.NOT_FOUND.value(),
	                "CRM record not found with ID: " + crmCreateLead.getId(),
	                null
	            )
	        );
	    }
	}

	@GetMapping("/getLeads/{enquiryType}")
  public ResponseEntity<List<CrmCreateLead>>  fetchCrmCreateLeadByEnquiryType(@PathVariable String enquiryType){  
	  List<CrmCreateLead> fetchCrmCreateLeadByEnquiryType = crmServiceImpl.fetchCrmCreateLeadByEnquiryTypeImpl(enquiryType);
	  return new ResponseEntity<List<CrmCreateLead>>(fetchCrmCreateLeadByEnquiryType, HttpStatus.OK);
  }
	

	@PostMapping("/createCrmHealthCheck")
	public ResponseEntity<ApiResponse<Void>> updateCrmHealthCheck(@RequestBody CrmHealthChecks crmHealthChecks) {
		
		System.out.println(crmHealthChecks.toString());
	    boolean updated = crmServiceImpl.createCrmHealthCheckImpl(crmHealthChecks);

	    if (updated) {
	        return ResponseEntity.ok(
	            new ApiResponse<>(
	                true,
	                HttpStatus.OK.value(),
	                "CRM health Check record updated successfully",
	                null
	            )
	        );
	    } else {
	        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(
	            new ApiResponse<>(
	                false,
	                HttpStatus.NOT_FOUND.value(),
	                "CRM Health Check record not found with ID: ",
	                null
	            )
	        );
	    }
	}
}
