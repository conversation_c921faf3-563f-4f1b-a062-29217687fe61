package com.aig.aigone.bloodbank.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.aig.aigone.model.entity.aigone.Auditable;
@Data
@Entity
@Table(name = "donor_history")
public class DonorHistory extends  Auditable implements Serializable {

	private static final long serialVersionUID = 1L;
	
	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String donorname;
    private String mobile;
    private String bloodGroup;
    private String age;
    private String gender;
    private LocalDate lastDonationDate;
    private String employeeId;
    private String donorType;
    private String coMorbities;
    private String medications;
    private LocalDate registrationDate;
    private Boolean eligibleForDonation;
    private Long fk_donor_id;
    private Long request_id;
    private String donrResponse;
    
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getDonorname() {
		return donorname;
	}
	public void setDonorname(String donorname) {
		this.donorname = donorname;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getBloodGroup() {
		return bloodGroup;
	}
	public void setBloodGroup(String bloodGroup) {
		this.bloodGroup = bloodGroup;
	}
	public String getAge() {
		return age;
	}
	public void setAge(String age) {
		this.age = age;
	}
	public String getGender() {
		return gender;
	}
	public void setGender(String gender) {
		this.gender = gender;
	}
	public LocalDate getLastDonationDate() {
		return lastDonationDate;
	}
	public void setLastDonationDate(LocalDate lastDonationDate) {
		this.lastDonationDate = lastDonationDate;
	}
	public String getEmployeeId() {
		return employeeId;
	}
	public void setEmployeeId(String employeeId) {
		this.employeeId = employeeId;
	}
	public String getDonorType() {
		return donorType;
	}
	public void setDonorType(String donorType) {
		this.donorType = donorType;
	}
	public String getCoMorbities() {
		return coMorbities;
	}
	public void setCoMorbities(String coMorbities) {
		this.coMorbities = coMorbities;
	}
	public String getMedications() {
		return medications;
	}
	public void setMedications(String medications) {
		this.medications = medications;
	}
	public LocalDate getRegistrationDate() {
		return registrationDate;
	}
	public void setRegistrationDate(LocalDate localDate) {
		this.registrationDate = localDate;
	}
	public Boolean getEligibleForDonation() {
		return eligibleForDonation;
	}
	public void setEligibleForDonation(Boolean eligibleForDonation) {
		this.eligibleForDonation = eligibleForDonation;
	}
	public Long getFk_donor_id() {
		return fk_donor_id;
	}
	public void setFk_donor_id(Long fk_donor_id) {
		this.fk_donor_id = fk_donor_id;
	}
	public Long getRequest_id() {
		return request_id;
	}
	public void setRequest_id(Long request_id) {
		this.request_id = request_id;
	}
	public String getDonrResponse() {
		return donrResponse;
	}
	public void setDonrResponse(String donrResponse) {
		this.donrResponse = donrResponse;
	}
	
	
    
}
