package com.aig.aigone.controller.dfcms;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.service.dfcms.CouponUploadConfigService;

@RestController
@RequestMapping("/api/dfcms/coupon-upload-config")
public class CouponUploadConfigController {

    @Autowired
    private CouponUploadConfigService configService;

    @GetMapping("/isExcelUploadEnabled")
    public boolean isExcelUploadEnabled() {
        return configService.isExcelUploadEnabled();
    }}