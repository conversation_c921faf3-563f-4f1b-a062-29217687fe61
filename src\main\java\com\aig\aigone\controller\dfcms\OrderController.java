package com.aig.aigone.controller.dfcms;

import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.model.dto.dfcms.OrderRequest;
import com.aig.aigone.model.dto.dfcms.TransactionsDTO;
import com.aig.aigone.service.dfcms.CouponService;

import io.swagger.v3.oas.annotations.Operation;

@RestController
@RequestMapping("/api/dfcms")
public class OrderController {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderController.class);

    private final CouponService couponService;


    public OrderController(CouponService couponService) {
        this.couponService = couponService;
    }

    @PostMapping("/order")
    @Operation(summary = "Create or Edit an Order")
    public ResponseEntity<?> createOrEditOrder(@RequestBody OrderRequest request,
                                               @RequestParam(value = "orderNo", required = false) String orderNo) throws Exception {
        LOGGER.info("Processing order request. OrderNo: {}", orderNo != null ? orderNo : "New Order");
        TransactionsDTO response = couponService.createOrEditOrder(request, Optional.ofNullable(orderNo));
        
        if (orderNo == null || orderNo.isEmpty()) {
            LOGGER.info("New order created successfully. OrderNo: {}", response.getOrderNO());
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } else {
            LOGGER.info("Order edited successfully. OrderNo: {}", response.getOrderNO());
            return ResponseEntity.ok(response);
        }
    }

    @GetMapping("/scan")
    @Operation(summary = "Scan an Order by Order Number")
    public ResponseEntity<?> scanOrder(@RequestParam("orderNo") String orderNo) throws Exception {
        LOGGER.info("Received request to scan order with order number: {}", orderNo);
        String orderDetails = couponService.scanOrder(orderNo);
        return ResponseEntity.ok(orderDetails);
    }


    @PostMapping("/validate")
    @Operation(summary = "Validate an Order")
    public String validateOrder(@RequestParam String orderNo, 
                                           @RequestParam String status,
                                           @RequestParam String employeeId) throws Exception {
        LOGGER.info("Received request to validate order for Employee ID: {}, Order No: {}", employeeId, orderNo);
        String response = couponService.validateOrder(orderNo, status, employeeId);
        return response;
    }


    @PostMapping("/cancel")
    @Operation(summary = "Cancel an Order")
    public ResponseEntity<?> cancelOrder(@RequestParam String orderNo, @RequestParam String employeeId) throws Exception {
        LOGGER.info("Received request to cancel order: {} for Employee ID: {}", orderNo, employeeId);
        String response = couponService.cancelOrder(orderNo, employeeId);
        return ResponseEntity.ok(response);
    }
    
    @PostMapping("/update-shared-status")
    @Operation(summary = "Update Shared Status of an Order")
    public ResponseEntity<String> updateSharedStatus(
            @RequestParam String orderNo,
            @RequestParam Boolean sharedStatus) {
        couponService.updateSharedStatus(orderNo, sharedStatus);
        return ResponseEntity.ok("Shared status updated successfully for orderNo: " + orderNo);
    }
    
    
}
