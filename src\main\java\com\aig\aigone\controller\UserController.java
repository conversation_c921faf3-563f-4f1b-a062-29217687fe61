package com.aig.aigone.controller;

import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.model.dto.UserPayGroupDto;
import com.aig.aigone.service.UserService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping("/api/users")
@Tag(name = "User", description = "APIs related to Users")
public class UserController {

    private final UserService userService;

    public UserController(UserService userService) {
        this.userService = userService;
    }

    @Operation(summary = "Get users by Pay Group Name")
    @GetMapping("/by-paygroup")
    public ResponseEntity<List<UserPayGroupDto>> getUsersByPayGroup(@RequestParam String payGroupName) {
        List<UserPayGroupDto> users = userService.getUsersByPayGroupAndActiveTrue(payGroupName);
        return ResponseEntity.ok(users);
    }
    
    
    @Operation(summary = "Get users by Pay Group Name and Hospital Location")
    @GetMapping("/by-paygroup-and-location")
    public ResponseEntity<List<UserPayGroupDto>> getUsersByPayGroupAndLocation(
            @RequestParam String payGroupName,
            @RequestParam String hosLocation) {
        
        List<UserPayGroupDto> users = userService.getUsersByPayGroupAndLocationAndActiveTrue(payGroupName, hosLocation);
        return ResponseEntity.ok(users);
    }

    
    
    @GetMapping("/paygroups")
    @Operation(summary = "Get all distinct pay groups", description = "Returns a list of all unique pay group names from UserEntity")
    public ResponseEntity<List<String>> getAllPayGroups() {
        List<String> payGroups = userService.getAllDistinctPayGroups();
        return ResponseEntity.ok(payGroups);
    }
}

