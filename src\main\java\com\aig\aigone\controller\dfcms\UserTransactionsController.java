package com.aig.aigone.controller.dfcms;

import java.io.IOException;
import java.util.Map;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.aig.aigone.model.dto.dfcms.TransactionsDTO;
import com.aig.aigone.service.dfcms.UserTransactionsService;
import com.google.zxing.WriterException;

import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/dfcms/getOrderHistory")
public class UserTransactionsController {

	private final UserTransactionsService userTransactionsService;

	public UserTransactionsController(UserTransactionsService userTransactionsService) {
		this.userTransactionsService = userTransactionsService;

	}

	@GetMapping("/{orderNo}")
	@Operation(summary = "Get User Transaction by Order Number")
	public ResponseEntity<TransactionsDTO> getUserTransaction(@PathVariable String orderNo)
			throws WriterException, IOException {
		log.info("Fetching transaction details for orderNo: {}", orderNo);
		TransactionsDTO transaction = userTransactionsService.getTransactionDetails(orderNo);
		return ResponseEntity.ok(transaction);
	}

	@GetMapping("/orderHistory")
	@Operation(summary = "Get Order History by Staff ID")
	public ResponseEntity<?> getOrderHistoryByStaffId(@RequestParam("staffId") String staffId,
			@RequestParam(value = "from", required = false) String from,
			@RequestParam(value = "to", required = false) String to,
			@RequestParam(value = "orderStatus", required = false) String orderStatus) throws IOException, Exception {
		log.info("Fetching order history for staffId: {}, from: {}, to: {}, orderStatus: {}", staffId, from, to,
				orderStatus);

		// Call the service method with all parameters
		Map<String, Object> transactions = userTransactionsService.getOrderHistoryByStaffId(staffId, from, to,
				orderStatus);

		// Return no content if transactions are empty, otherwise return the list
		return transactions.isEmpty() ? ResponseEntity.noContent().build() : ResponseEntity.ok(transactions);
	}

	@GetMapping("/orderHistory/dateWise")
	@Operation(summary = "Get Transactions Date-Wise")
	public ResponseEntity<?> getTransactions(
			@RequestParam String from, 
			@RequestParam String to,
			@RequestParam(required = false) String orderBy, 
			@RequestParam(required = false) String orderStatus,
			@RequestParam(required = false) String payGroupName) {
		log.info("Fetching transactions from {} to {} with orderBy: {}, orderStatus: {}, payGroupName: {}, hosLocation: {}", 
				from, to, orderBy, orderStatus);

		Map<String, Object> result = userTransactionsService.getTotalTransactionData(from, to, orderBy, orderStatus);

		if (result.isEmpty()) {
			log.warn("No transactions found for the given date range: {} to {}, orderBy: {}, orderStatus: {}, payGroupName: {}, hosLocation: {}", 
					from, to, orderBy, orderStatus);
			return ResponseEntity.noContent().build();
		}

		log.info("Successfully fetched transaction data. Total transactions: {}", result.get("totalOrders"));
		return ResponseEntity.ok(result);
	}

	@GetMapping("/orderHistoryByCounter")
	@Operation(summary = "Get Order History by Counter")
	public ResponseEntity<?> getOrderHistoryByCounter(@RequestParam String validatedBy,
			@RequestParam(required = false) String fromDate, @RequestParam(required = false) String toDate,
			@RequestParam(required = false) String orderStatus) throws IOException, Exception {

		log.info("Fetching order history by counter for validatedBy: {}, fromDate: {}, toDate: {}, orderStatus: {}",
				validatedBy, fromDate, toDate, orderStatus);

		// Call service layer to fetch the data
		Map<String, Object> transactions = userTransactionsService.getOrderHistoryByCounter(validatedBy, fromDate,
				toDate, orderStatus);

		// Return appropriate response
		if ((int) transactions.get("totalOrders") == 0) {
			log.warn("No orders found for validatedBy: {}, fromDate: {}, toDate: {}, orderStatus: {}", validatedBy,
					fromDate, toDate, orderStatus);
			return ResponseEntity.noContent().build();
		}

		log.info("Successfully fetched order history data. Total orders: {}", transactions.get("totalOrders"));
		return ResponseEntity.ok(transactions);
	}

}
