package com.aig.aigone.common;

import java.security.SecureRandom;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

public final class AigOneUtil {
	
	public static final String CACHE_FCM = "fcmCache";
	
	private static final String CHARACTERS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
	
	public static final String INDIA_CODE = "91";
	
	public static final String INDIA_CURRENCY = "INR";
	
	public static final String LANUGAE_ENGLISH = "ENS";
	
	public static final String IP = "IP";
	
	public static final String OP = "OP";
	
	
	// CC Avenue
	public static final String CC_AVENUE_PARAM_MECHANT_ID = "merchant_id";
	
	public static final String CC_AVENUE_PARAM_ORDER_ID = "order_id";
	
	public static final String CC_AVENUE_PARAM_CURRENCY = "currency";

	public static final String CC_AVENUE_PARAM_AMOUNT = "amount";
	
	public static final String CC_AVENUE_PARAM_REDIRECT_URL ="redirect_url";
	
	public static final String CC_AVENUE_PARAM_CANCEL_URL ="cancel_url";
	
	public static final String CC_AVENUE_PARAM_LANGUAGE ="language";
	
	public static final String CC_AVENUE_PARAM_TID ="tid";
	

	
	public static final String CACHE_AIG_ONE ="aigOneCache";
	
	public static final String CACHE_HIS ="hisCache";
	
	public static final String CACHE_EMR ="emrCache";
	
	public static final String CACHE_ICARE ="iCareCache";
	
	public static final String CACHE_KEY_HIS_LOGIN ="HIS_LOGIN";
	
	public static final String CACHE_QUEUE_LOGIN = "smartQueueCache";
	
	public static final String CACHE_HIS_LAB_REPORT = "hisLabReportCache";
	
	public static final String CACHE_HIS_LAB_REPORT_DETAILS = "hisLabReportDetailsCache";
	
	
	public static final String CLIENT_HIS ="HIS";
	
	public static final String CLIENT_ICARE ="ICARE";
	
	public static final String CLIENT_SMS ="SMS";
	
	public static final String CLIENT_RIS = "RIS";
	
	public static final String CLIENT_EMR = "EMR";
	
	public static final String CLIENT_DIALYSIS= "DIALYSIS";
	
	
	public static final String STATUS_FOLLOW= "FOLLOW";
	
	public static final String STATUS_UN_FOLLOW= "UN_FOLLOW";
	
	public static final String CLIENT_PYTHON_FLASK = "CLIENT_PYTHON_FLASK";
	
	
	
	
	
	
	public static String generateTransactionId(int length) {
		return new SecureRandom().ints(length, 0, CHARACTERS.length())
				.mapToObj(CHARACTERS::charAt)
				.map(Object::toString)
				.collect(Collectors.joining());
	}
	
	public static String generateUniqueIdUsingUUid(int length) {
		return UUID.randomUUID().toString();
	}
	
	
	public static String fetchEventByAPI(String api) {
		String event = "";
		if(api.equals("/api/auth/otp/generate")) {
			event = "LOGIN";
		} else if(api.equals("/api/auth/otp/validate")) {
			event = "OTP_VALIDATION";
		} else if(api.equals("/api/auth/otp/resend")) {
			event = "OTP_RESEND";
		} else if(api.equals("/api/his/patient/register")) {
			event = "PATIENT_REGISTRATION";
		} else if(api.equals("/api/his/patient/members")) {
			event = "PATIENT_MEMBERS";
		} else if(api.contains("/api/his/patient/lab/orders")) {
			event = "LAB_ORDERS";
		} else if(api.contains("/api/his/patient/appointments")) {
			event = "APPOINTMENTS";
		} else if(api.contains("/api/his/patient/ip/")) {
			event = "IP_DETAILS";
		} else if(api.contains("/api/his/patient/faqs/")) {
			event = "FAQ";
		} else if(api.contains("/api/his/patient/lab/orders/completed/")) {
			event = "COMPLETED_LAB_ORDERS";
		} else if(api.contains("/api/his/patient/previous/appointments/")) {
			event = "PREVIOUS_APPOINTENTS";
		} else if(api.contains("/api/his/patient/appointment/book")) {
			event = "BOOK_APPOINTENT";
		} else if(api.contains("/api/his/patient/wellnessPackages")) {
			event = "WELLNESSPACKAGES";
		} else if(api.contains("/api/his/patient/ticketQuestions")) {
			event = "TICKET_QUESTIONS";
		} else if(api.contains("/api/his/patient/appointment/delete/")) {
			event = "DELETE_APPOINTMENT";
		} else if(api.contains("/api/his/patient/appointment/details/")) {
			event = "APPOINTMENT_DETAILS";
		} else if(api.contains("/api/his/patient/aig-assistant/")) {
			event = "AIG_ASSISTANT";
		} else if(api.contains("/api/his/patient/uploadDoc")) {
			event = "UPLOAD_DOC";
		} else if(api.contains("/api/his/patient/reports/")) {
			event = "REPORTS_LIST";
		} else if(api.contains("/api/his/patient/reports/view")) {
			event = "REPORT_VIEW";
		} else if(api.contains("/api/his/patient/documents/")) {
			event = "DOCUMENTS";
		} else if(api.contains("/api/his/patient/document/view")) {
			event = "DOCUMENTS_VIEW";
		} else if(api.contains("/api/his/patient/procedureGuide")) {
			event = "PROCEDURE_GUIDE";
		} 
		else if(api.contains("/api/his/patient")) {
			event = "PATIENT_DETAILS";
		}
		// PAyment
		else if(api.contains("/api/payment/init")) {
			event = "PAYMENT_INITIALIZATION";
		} else if(api.contains("/api/payment/status")) {
			event = "PAYMENT_STATUS";
		} else if(api.contains("/api/payment/status")) {
			event = "PAYMENT_STATUS";
		}
		
		//EMployee
		else if(api.contains("/api/his/employee/profile")) {
			event = "EMPLOYEE_PROFILE";
		} else if(api.contains("/api/his/employee/mappedDoctors")) {
			event = "EMPLOYEE_MAPPED_DOCTORS";
		} else if(api.contains("/api/his/employee/roster/")) {
			event = "ROASTER";
		} else if(api.contains("/api/his/employee/roster/")) {
			event = "ROASTER";
		}
		
		//ICare
		else if(api.contains("/api/icare/ticket")) {
			event = "CREATE_TICKET";
		} else if(api.contains("/api/icare/ticket/view")) {
			event = "VIEW_TICKET";
		} else if(api.contains("/api/icare/ticket/details")) {
			event = "VIEW_TICKET_DETAILS";
		} else if(api.contains("/api/icare/handle/save")) {
			event = "TICKET_ACTION_SAVE";
		} else if(api.contains("/api/icare/handle/forward")) {
			event = "TICKET_ACTION_FORWARD";
		} else if(api.contains("/api/icare/handle/forward")) {
			event = "TICKET_ACTION_FORWARD";
		}
		
		return event;
	}
	
	 public static <T> Predicate<T> distinctByKey(
				Function<? super T, ?> keyExtractor) {

			Map<Object, Boolean> seen = new ConcurrentHashMap<>(); 
			return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null; 
		}
	

}
