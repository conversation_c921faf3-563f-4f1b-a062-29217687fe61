package com.aig.aigone.daycare.emr.model;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "hp_care_events_dtl")
public class EventDetails {
	
	@jakarta.persistence.Id
	@Column(name="event_dtl_accn_no")
	private Integer Id;
	
	
	@Column(name="concept_code")
	private String conceptCode;
	
	@Column(name="start_date_time")
	private LocalDateTime startTime;
	
	@Column(name = "event_accn_no")
	private Integer accountNumber;
}
